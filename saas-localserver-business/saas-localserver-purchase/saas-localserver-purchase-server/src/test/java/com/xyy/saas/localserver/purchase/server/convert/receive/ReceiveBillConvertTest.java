//package com.xyy.saas.localserver.purchase.server.convert.receive;
//
//import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
//import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;
//import com.xyy.saas.localserver.purchase.server.dal.dataobject.purchase.PurchaseBillDO;
//import com.xyy.saas.localserver.purchase.server.dal.dataobject.receive.ReceiveBillDO;
//import com.xyy.saas.localserver.purchase.server.dal.dataobject.receive.ReceiveBillDetailDO;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import java.math.BigDecimal;
//import java.time.LocalDateTime;
//import java.util.ArrayList;
//import java.util.List;
//
//import static com.xyy.saas.localserver.purchase.enums.receive.ReceiveBillTypeEnum.ALLOCATION_RECEIVE;
//import static org.junit.jupiter.api.Assertions.*;
//
//@ExtendWith(MockitoExtension.class)
//class ReceiveBillConvertTest {
//
//    @InjectMocks
//    private ReceiveBillConvert receiveBillConvert = ReceiveBillConvert.INSTANCE;
//
//    @Test
//    void testGenerateStoreReceiveBillByAllocation() {
//        // 1. 准备测试数据
//        ReceiveBillDO headReceiveBill = createHeadReceiveBill();
//
//        // 2. 执行测试方法
//        ReceiveBillDO result = receiveBillConvert.generateStoreReceiveBillByAllocation(headReceiveBill);
//
//        // 打印关键信息
//        System.out.println("转换后的对象关键信息：");
//        System.out.println("单据类型: " + result.getBillType());
//        System.out.println("单据状态: " + result.getStatus());
//        System.out.println("租户ID: " + result.getTenantId());
//        System.out.println("租户类型: " + result.getTenantType());
//        System.out.println("总部租户ID: " + result.getHeadTenantId());
//        System.out.println("详情数量: " + (result.getDetails() != null ? result.getDetails().size() : 0));
//
//        // 3. 验证结果
//        assertNotNull(result);
//        // 验证单据类型
//        assertEquals(ALLOCATION_RECEIVE.getCode(), result.getBillType()); // ALLOCATION_RECEIVE
//        // 验证单据状态
//        assertEquals(1, result.getStatus()); // PENDING_RECEIVE
//        // 验证租户信息
//        assertEquals(headReceiveBill.getAllocationBill().getInboundTenantId(), result.getTenantId());
//        assertEquals(2, result.getTenantType()); // CHAIN_STORE
//        assertEquals(headReceiveBill.getAllocationBill().getHeadTenantId(), result.getHeadTenantId());
//
//        // 验证详情列表
//        assertNotNull(result.getDetails());
//        assertEquals(headReceiveBill.getDetails().size(), result.getDetails().size());
//
//        // 验证第一个详情
//        if (!result.getDetails().isEmpty()) {
//            ReceiveBillDetailDO sourceDetail = headReceiveBill.getDetails().get(0);
//            ReceiveBillDetailDO resultDetail = result.getDetails().get(0);
//
//            // 验证基本信息
//            assertEquals(sourceDetail.getProductPref(), resultDetail.getProductPref());
//            assertEquals(sourceDetail.getArriveQuantity(), resultDetail.getArriveQuantity());
//
//            // 验证租户ID
//            assertEquals(result.getTenantId(), resultDetail.getTenantId());
//
//            // 验证商品信息
//            assertNotNull(resultDetail.getExt());
//            assertNotNull(resultDetail.getExt().getProductInfo());
//            assertEquals(sourceDetail.getExt().getProductInfo().getBrandName(),
//                    resultDetail.getExt().getProductInfo().getBrandName());
//        }
//    }
//
//    private ReceiveBillDO createHeadReceiveBill() {
//        ReceiveBillDO headReceiveBill = new ReceiveBillDO();
//        headReceiveBill.setId(1L);
//        headReceiveBill.setBillNo("TEST-RECEIVE-001");
//        headReceiveBill.setPurchaseTime(LocalDateTime.now());
//
//        // 设置调剂单信息
//        PurchaseBillDO allocationBill = new PurchaseBillDO();
//        allocationBill.setInboundTenantId(1001L);
//        allocationBill.setHeadTenantId(1000L);
//        headReceiveBill.setAllocationBill(allocationBill);
//
//        // 设置详情列表
//        List<ReceiveBillDetailDO> details = new ArrayList<>();
//        ReceiveBillDetailDO detail = new ReceiveBillDetailDO();
//        detail.setId(1L);
//        detail.setProductPref("TEST-PRODUCT-001");
//        detail.setArriveQuantity(new BigDecimal("10"));
//
//        // 设置商品信息
//        ExtDTO ext = new ExtDTO();
//        ProductInfoDto productInfo = ProductInfoDto.builder().brandName("测试商品").build();
//        ext.setProductInfo(productInfo);
//        detail.setExt(ext);
//
//        details.add(detail);
//        headReceiveBill.setDetails(details);
//
//        return headReceiveBill;
//    }
//}