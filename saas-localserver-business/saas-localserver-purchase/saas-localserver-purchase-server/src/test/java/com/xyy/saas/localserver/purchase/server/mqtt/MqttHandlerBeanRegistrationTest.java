package com.xyy.saas.localserver.purchase.server.mqtt;

import com.xyy.saas.localserver.entity.mqtt.MqttMessageHandler;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MQTT Handler Bean注册测试
 * 验证Spring能够正确注入List<MqttMessageHandler<?>>
 */
@SpringBootTest
@ActiveProfiles("unit-test")
@TestPropertySource(properties = {
    "mqtt.enabled=true",  // 启用MQTT功能进行测试
    "mqtt.server-url=tcp://localhost:1883",
    "mqtt.access-key=test-key",
    "mqtt.secret-key=test-secret",
    "mqtt.parent-topic=test/topic",
    "mqtt.keep-alive-interval=60",
    "mqtt.connection-timeout=30",
    "mqtt.automatic-reconnect=true",
    "mqtt.instance-id=test-instance",
    "mqtt.group-id=test-group",
    "mqtt.client-id=test-client"
})
public class MqttHandlerBeanRegistrationTest {

    @Autowired(required = false)
    private List<MqttMessageHandler<?>> mqttHandlers;

    @Test
    public void testMqttHandlerBeansRegistration() {
        // 验证MQTT Handler列表不为空
        assertNotNull(mqttHandlers, "MqttMessageHandler列表不应为null");
        
        // 验证至少包含我们添加的两个Handler
        assertTrue(mqttHandlers.size() >= 2, 
            "应该至少包含HeadDispatchHandler和HeadRejectReceiveHandler");
        
        // 验证包含HeadDispatchHandler
        boolean hasHeadDispatchHandler = mqttHandlers.stream()
            .anyMatch(handler -> handler instanceof HeadDispatchHandler);
        assertTrue(hasHeadDispatchHandler, "应该包含HeadDispatchHandler");
        
        // 验证包含HeadRejectReceiveHandler
        boolean hasHeadRejectReceiveHandler = mqttHandlers.stream()
            .anyMatch(handler -> handler instanceof HeadRejectReceiveHandler);
        assertTrue(hasHeadRejectReceiveHandler, "应该包含HeadRejectReceiveHandler");
        
        // 验证每个Handler都有正确的消息类型
        mqttHandlers.forEach(handler -> {
            assertNotNull(handler.getMessageType(), "消息类型不应为null");
            assertNotNull(handler.getDataClass(), "数据类型不应为null");
        });
        
        System.out.println("✅ MQTT Handler Bean注册测试通过");
        System.out.println("注册的Handler数量: " + mqttHandlers.size());
        mqttHandlers.forEach(handler -> 
            System.out.println("- " + handler.getClass().getSimpleName() + 
                             " (消息类型: " + handler.getMessageType() + ")"));
    }
    
    @Test
    public void testMqttHandlerMessageTypes() {
        if (mqttHandlers == null || mqttHandlers.isEmpty()) {
            return; // 如果MQTT功能未启用，跳过测试
        }
        
        // 验证消息类型的唯一性
        long uniqueMessageTypes = mqttHandlers.stream()
            .map(MqttMessageHandler::getMessageType)
            .distinct()
            .count();
        
        assertEquals(mqttHandlers.size(), uniqueMessageTypes, 
            "每个Handler应该有唯一的消息类型");
        
        System.out.println("✅ MQTT Handler消息类型唯一性测试通过");
    }
}
