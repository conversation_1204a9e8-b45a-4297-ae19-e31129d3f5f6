#供应商信息表
DROP TABLE IF EXISTS saas_purchase_supplier;
create table saas_purchase_supplier (
    id                                         bigint unsigned                         primary key comment '主键ID',
    guid                                       varchar(32)   default ''                not null comment '供应商编码(租户本地新建供应商生成规则：GYSC+租户id(来源)+机器码+日期+4位流水)',
    source_supplier_guid                       varchar(32)   default ''                not null comment '源供应商编码',
    source                                     bigint        default 0                 null comment '来源（非系统默认供应商，数据来源默认为租户id。否则为平台名称或代码）',
    system_default                             bit           default b'1'              not null comment '系统默认',
    name                                       varchar(256)  default ''                null comment '供应商名称',
    type                                       int(11)       default null              null comment '供应商类别（字典配置-20048）',
    mnemonic_code                              varchar(256)  default ''                null comment '助记码（混合查询条件）',
    legal_representative                       varchar(256)  default ''                null comment '法定代表人',
    registered_address                         varchar(256)  default ''                null comment '注册地址',
    business_scope                             varchar(2048) default ''                null comment '经营范围（字典配置-10005）',
    business_license                           varchar(256)  default ''                null comment '营业执照编码',
    licence_authority                          varchar(256)  default ''                null comment '发证机关',
    registered_date                            date          default null              null comment '注册日期',
    expiration_date                            date          default null              null comment '有效期至',
    expiration_date_type                       tinyint(4)    default null              null comment '有效期至方式:1--长期，2--手填，当为2的时候，expiration_date必须有值',
    tri_cert_merged                            bit           default b'0'              not null comment '是否三证合一：0--否，1--是',
    deposit_bank                               varchar(256)  default ''                null comment '开户银行',
    bank_account                               varchar(256)  default ''                null comment '银行账号',
    account_name                               varchar(256)  default ''                null comment '开户户名',
    organization_certification_code            varchar(256)  default ''                null comment '组织机构代码',
    organization_certification_date            date          default null              null comment '组织机构发证日期',
    organization_certification_expiration_date date          default null              null comment '组织机构有效期至',
    organization_certification_tax_no          varchar(256)  default ''                null comment '组织机构税务登记号',
    organization_certification_authority       varchar(256)  default ''                null comment '组织机构代码证发证机关',
    registered_address_cod                     varchar(30)   default ''                not null comment '注册地址code',
    store_address_code                         varchar(30)   default ''                not null comment '仓库地址code',
    store_address                              varchar(256)  default ''                not null comment '仓库详情地址',
    signet                                     varchar(2048) default ''                not null comment '印章印模附件',
    shipment_template                          varchar(2048) default ''                not null comment '随货同行单样式附件',
    qualification_infos                        text                                    null comment '资质与经营范围json数据',
    proxy_ID_card                              varchar(32)   default ''                not null comment '委托人身份证号',
    proxy_ID_card_expiration_date              date          default null              null comment '委托人身份证有效期',
    msfx_ref_ent_id                            varchar(128)  default ''                null comment '码上放心-企业唯一标识',
    msfx_ent_id                                varchar(128)  default ''                null comment '码上放心-企业ID',
    relate_distribute                          bit default b'0'                         not null comment '关联分发业务',
    remark                                     text                                    null comment '备注（存json数据）',
    disabled                                   bit           default b'0'              not null comment '是否禁用',
    creator                                    varchar(64)   default ''                not null comment '创建者',
    create_time                                datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                                    varchar(64)   default ''                null comment '更新者',
    update_time                                datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                                    bit           default b'0'              not null comment '是否删除',
unique key uk_guid (guid)
) comment = '供应商信息表';

#租户(单体/总部)-供应商-销售人员信息
DROP TABLE IF EXISTS saas_purchase_tenant_supplier_sales;
create table saas_purchase_tenant_supplier_sales (
    id                                bigint unsigned                         primary key comment '主键ID',
    tenant_id                         bigint        default 0                 not null comment '租户ID',
    supplier_guid                     varchar(32)   default ''                not null comment '供应商编号',
    sales_name                        varchar(128)  default ''                not null comment '销售人员姓名',
    authorized_area                   varchar(128)  default ''                not null comment '授权区域',
    authorization_num                 varchar(128)  default ''                not null comment '授权书号',
    authorization_num_expiration_date datetime      default null              null comment '授权书号有效期',
    phone_number                      varchar(20)   default ''                not null comment '手机号码',
    authorized_varieties              varchar(255)  default ''                not null comment '授权信息',
    id_card                           varchar(36)   default ''                not null comment '身份证号',
    id_card_expiration_date           datetime      default null              null comment '身份证有效期',
    id_card_attachment                varchar(2048) default ''                not null comment '身份证附件',
    authorization_attachment          varchar(2048) default ''                not null comment '授权书附件',
    authorized_scope                  varchar(2048) default ''                not null COMMENT '经营范围',
    creator                           varchar(64)   default ''                not null comment '创建者',
    create_time                       datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                           varchar(64)   default ''                null comment '更新者',
    update_time                       datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                           bit           default b'0'              not null comment '是否删除'
) comment = '租户-供应商-销售人员信息';

#租户(单体/总部)-供应商-关联关系
DROP TABLE IF EXISTS saas_purchase_tenant_supplier_relation;
create table saas_purchase_tenant_supplier_relation (
    id                                bigint unsigned                           primary key comment '主键ID',
    tenant_id                         bigint          default 0                 not null comment '租户ID',
    supplier_guid                     varchar(32)     default ''                not null comment '供应商编号',
    status                            tinyint(2)      default 0                 not null comment '首营状态：1-审核中，2-审核通过，3-审核未通过',
    creator                           varchar(64)     default ''                not null comment '创建者',
    create_time                       datetime        default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                           varchar(64)     default ''                null comment '更新者',
    update_time                       datetime        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                           bit             default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_supplier_guid (tenant_id,supplier_guid)
) comment = '租户-供应商-关联关系';

#采购单表
DROP TABLE IF EXISTS saas_purchase_bill;
create table saas_purchase_bill
(
    id                            bigint unsigned                           primary key comment '主键ID',
    plan_bill_no                  varchar(32)     default ''                not null comment '计划单号',
    purchase_bill_no                 varchar(32)     default ''                not null comment '订单号',
    mall_order_no                 varchar(32)     default ''                not null comment '商城订单号',
    tenant_id                     bigint          default 0                 not null comment '租户ID',
    tenant_type                   tinyint(4)      default 0                 not null comment '租户类型（1-单体门店、2-连锁门店、3-连锁总部）',
    head_tenant_id                bigint          default 0                 not null comment '总部租户ID',
    inbound_tenant_id             bigint          default 0                 not null comment '入库租户ID（主配）',
    outbound_tenant_id            bigint          default 0                 not null comment '出库租户ID（调剂）',
    composite_bill_no             varchar(256)  default ''                  null comment '综合单据号（单号混合）',
    bill_type                     tinyint(4)      default 0                 not null comment '单据类型（1-采购订单、2-门店要货、3-门店调剂、4-总部铺货）',
    import_mode                   tinyint(4)      default 0                 not null comment '导入方式（1-采购计划（正常创建）、2-一键入库 、3-接口拉取（比如药帮忙订单）、4-excel导入）',
    remote_received               bit             default b'0'              not null comment '是否远程收货',
    submitted                     bit             default b'0'              not null comment '已提交（类同于是否暂存）',
    status                        tinyint(2)      default 0                 not null comment '采购状态（1-已创建计划（采购计划）、2-已下单待审批、3-已审批待发货、4-发货中（部分发货）、5-已发货（全部发货）、6-已完成（已收货）、7-采购失败、8-已撤销（审批前撤销）、9-已驳回（审批不通过））',
    medicine_type                 tinyint(2)      default null              null comment '药品类型（1-中药、2-非中药）',
    purchase_mode                 tinyint(2)      default 1                 not null comment '采购方式（1-线下采购、2-无仓（B2B 无仓）、3-共仓（B2C 共仓））',
    supplier_guid                 varchar(32)     default ''                not null comment '供应商编码',
    supplier_name                 varchar(50)     default ''                not null comment '供应商名称',
    purchase_content              varchar(2000)   default ''                not null comment '采购内容',
    purchase_quantity             decimal(11,2)   default 0.00              null comment '采购总数量（原单退货扣减可退数量）',
    purchase_amount               decimal(17,2)   default 0.00              not null comment '采购金额（总金额）',
    delivered_quantity            decimal(11,2)   default 1.00              not null comment '已发货数量',
    returnable_quantity           decimal(11,2)   default 0.00              null comment '可退总数量（原单退货扣减可退数量，下游收货单的实际入库数量）',
    product_kind                  int             default 1                 not null comment '商品种类',
    planner                       varchar(64)     default ''                not null comment '计划员（计划审核员）',
    plan_time                     datetime        default null              null comment '计划时间（计划审核时间）',
    purchaser                     varchar(64)     default ''                not null comment '采购员（采购审核员）',
    purchase_time                 datetime        default null              null comment '采购时间（采购审核时间）',
    checker                       varchar(64)   default ''                  not null comment '复核员',
    check_time                    datetime        default null              null comment '复核时间（采购审核时间）',
    receiver                      varchar(64)   default ''                  not null comment '收货人（门店收货人信息）',
    receiver_phone                varchar(20)   default ''                  not null comment '收货人电话',
    receiver_area                 varchar(100)  default ''                  not null comment '收货人所在区域',
    receiver_address              varchar(100)  default ''                  not null comment '收货人地址',
    remark                        varchar(255)    default ''                null comment '备注',
    version                       int             default 1                 not null comment '版本(乐观锁)',
    creator                       varchar(64)     default ''                not null comment '创建者',
    create_time                   datetime        default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                       varchar(64)     default ''                null comment '更新者',
    update_time                   datetime        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                       bit             default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_plan_bill_no (tenant_id,plan_bill_no)
) comment '采购单表';
CREATE INDEX idx_tenant_id_purchase_bill_no
    ON saas_purchase_bill (tenant_id, purchase_bill_no);

#采购明细表
DROP TABLE IF EXISTS saas_purchase_bill_detail;
create table saas_purchase_bill_detail
(
    id                       bigint unsigned                         primary key comment '主键ID',
    plan_bill_no             varchar(32)     default ''              not null comment '计划单号',
    purchase_bill_no            varchar(32)     default ''              not null comment '订单号',
    tenant_id                bigint        default 0                 not null comment '租户ID',
    product_pref             varchar(30)   default ''                not null comment '商品编码',
    lot_no                   varchar(32)   default null              null comment '批号',
    production_date          date          default null              null COMMENT '生产日期（YYYY-MM-DD）',
    expiry_date              date          default null              null COMMENT '有效期至（YYYY-MM-DD）',
    position_guid            varchar(20)   default 0.00              null comment '（合格）存储区编号',
    in_tax_rate              decimal(4,2)  default 0.00              null comment '进项税率（百分比）',
    out_tax_rate             decimal(4,2) default 0.00               null comment '销项税率（百分比）',
    price                    decimal(10,4) default 0.00              null comment '采购单价',
    purchase_amount          decimal(14,2) default 0.00              null comment '采购总金额',
    purchase_quantity        decimal(8,2)  default 1.00              not null comment '采购数量',
    delivered_quantity       decimal(8,2)  default 1.00              not null comment '已发货数量',
    returnable_quantity      decimal(8,2)  default 0.00              null comment '可退数量（原单退货扣减可退数量，下游收货单的实际入库数量）',
    channel_id               varchar(32)   default ''                null comment '渠道ID',
    source_line_no           varchar(256)  default ''                null comment '源行号（和三方ERP对接时需要）',
    medicare_project_code    varchar(100)                            null comment '医保项目编码',
    medicare_project_name    varchar(256)                            null comment '医保项目名称',
    medicare_project_level   tinyint      default 0                  not null comment '医保项目等级',
    medicare_min_package_num tinyint      default 0                  not null comment '医保最小包装数量',
    ext                      text                                    null comment '扩展信息（当前商品信息）',
    remark                   varchar(255)  default ''                null comment '备注',
    creator                  varchar(64)   default ''                not null comment '创建者',
    create_time              datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                  varchar(64)   default ''                null comment '更新者',
    update_time              datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                  bit           default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_bill_no_product_pref_lot_no (tenant_id,plan_bill_no,product_pref,lot_no)
) comment '采购明细表';
CREATE INDEX idx_tenant_id_purchase_bill_no_product_pref
    ON saas_purchase_bill_detail (tenant_id,purchase_bill_no,product_pref,lot_no);

#收货单
DROP TABLE IF EXISTS saas_purchase_receive_bill;
create table saas_purchase_receive_bill
(
    id                            bigint unsigned                         primary key comment '主键ID',
    bill_no                       varchar(32)   default ''                not null comment '收货（收货/验收/入库）单号',
    source_bill_no                varchar(32)   default ''                not null comment '来源单号',
    delivery_bill_no              varchar(32)   default ''                not null comment '配送出库单号',
    shipment_no                   varchar(30)   default ''                null comment '随货同行单号',
    mall_order_no                 varchar(32)   default ''                not null comment '商城订单号',
    outbound_tenant_id            bigint        default 0                 not null comment '租户ID',
    tenant_id                     bigint        default 0                 not null comment '租户ID',
    tenant_type                   tinyint(4)    default 0                 not null comment '租户类型（1-单体门店、2-连锁门店、3-连锁总部）',
    head_tenant_id                bigint        default 0                 not null comment '总部租户ID',
    composite_bill_no             varchar(256)  default ''                null comment '综合单据号（单号混合）',
    bill_type                     tinyint(4)    default 0                 not null comment '单据类型（1-采购收货、2-拒收收货、3-退货收货、4-调剂收货、5-要货收货、6-铺货收货）',
    purchase_mode                 tinyint(2)    default 1                 not null comment '采购方式（1-线下采购、2-无仓（B2B 无仓）、3-共仓（B2C 共仓））',
    status                        tinyint(2)    default 0                 not null comment '状态（1-待收货、2-待验收、3-待入库、4-已入库、5-已拒收）',
    supplier_guid                 varchar(32)   default ''                not null comment '供应商编码',
    supplier_name                 varchar(50)   default ''                not null comment '供应商名称',
    supplier_sales                varchar(50)   default ''                not null comment '供应商销售员',
    product_kind                  int           default 1                 not null comment '商品种类',
    receive_quantity              decimal(11,2) default 1.00              not null comment '收货数量',
    discount                      decimal(4,2)  default 0.00              null comment '折扣（百分比）',
    discount_amount             decimal(17,2) default 0.00              null comment '折扣总金额',
    receive_content               varchar(2000) default ''                not null comment '收货内容',
    receive_amount                decimal(17,2) default 0.00              not null comment '收货金额（折后总金额）',
    deliverer                     varchar(64)   default ''                not null comment '配送员',
    delivery_time                 datetime      default CURRENT_TIMESTAMP not null comment '配送出库时间',
    receiver                      varchar(64)   default ''                not null comment '收货员',
    receive_time                  datetime      default null              null comment '收货时间',
    accepter                      varchar(64)   default ''                not null comment '验收员',
    accept_time                   datetime      default null              null comment '验收时间',
    warehouser                    varchar(64)   default ''                not null comment '入库员',
    warehouse_time                datetime      default null              null comment '入库时间',
    checker                       varchar(64)   default ''                not null comment '复核员',
    check_time                    datetime      default null              null comment '复核时间',
    quality_inspector             varchar(300)  default ''                null comment '质检员',
    quality_inspection_report     varchar(300)  default ''                null comment '质检报告单',
    remark                        varchar(255)  default ''                null comment '备注',
    version                       int           default 1                 not null comment '版本(乐观锁)',
    creator                       varchar(64)   default ''                not null comment '创建者',
    create_time                   datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                       varchar(64)   default ''                null comment '更新者',
    update_time                   datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                       bit           default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_bill_no (tenant_id,bill_no)
) comment '收货单';

#收货单明细
DROP TABLE IF EXISTS saas_purchase_receive_bill_detail;
create table saas_purchase_receive_bill_detail
(
    id                        bigint unsigned                         primary key comment '主键ID',
    bill_no                   varchar(32)   default ''                not null comment '验收入库单号',
    tenant_id                 bigint        default 0                 not null comment '租户ID',
    product_pref              varchar(30)   default ''                not null comment '商品编码',
    lot_no                    varchar(32)   default null              null comment '批号',
    production_date           date          default null              null COMMENT '生产日期（YYYY-MM-DD）',
    expiry_date               date          default null              null COMMENT '有效期至（YYYY-MM-DD）',
    price                     decimal(10,4) default 0.00              null comment '含税成本价（单价）',
    tax_rate                  decimal(4,2)  default 0.00              null comment '税率（百分比）',
    discount                  decimal(4,2)  default 0.00              null comment '折扣（百分比）',
    discounted_price          decimal(14,2) default 0.00              null comment '折后含税单价',
    arrive_quantity           decimal(8,2)  default 1.00              not null comment '到货数量（发货数量）',
    receive_quantity          decimal(8,2)  default 1.00              not null comment '收货数量',
    reject_quantity           decimal(8,2)  default 1.00              not null comment '拒收数量',
    receive_amount            decimal(14,2) default 0.00              null comment '收货金额（折后单价*收货数量）',
    accept_conclusion         tinyint(1)    default 1                 null comment '验收结论（1-合格、0-锁定）',
    sample_quantity           decimal(8,2)  default 1.00              not null comment '抽样数量',
    unqualified_quantity      decimal(8,2)  default 1.00              not null comment '不合格品数量',
    unqualified_amount        decimal(14,2) default 0.00              not null comment '不合格品总金额（折后总金额）',
    unqualified_reason        varchar(256)  default ''                null comment '不合格原因',
    unqualified_position_guid varchar(20)   default 0.00              null comment '不合格品隔离区编码',
    qualified_quantity        decimal(8,2)  default 1.00              not null comment '合格品数量',
    qualified_amount          decimal(14,2) default 1                 not null comment '合格品总金额（折后总金额）',
    qualified_position_guid   varchar(20)   default 0.00              null comment '合格品储存区编码',
    warehouse_quantity        decimal(8,2)  default 1.00              not null comment '入库数量',
    warehouse_amount          decimal(14,2) default 0.00              null comment '入库金额（折后总金额）',
    treatment                 varchar(256)  default ''                null comment '处理措施',
    channel_id                varchar(32)   default ''                null comment '渠道ID',
    sterilization_batch_no    varchar(32)   default ''                null comment '灭菌批次',
    source_line_no            varchar(256)  default ''                null comment '源行号（和三方ERP对接时需要）',
    medicare_project_code     varchar(100)                            null comment '医保项目编码',
    medicare_project_name     varchar(256)                            null comment '医保项目名称',
    medicare_project_level    tinyint       default 0                 not null comment '医保项目等级',
    medicare_min_package_num  tinyint       default 0                 not null comment '医保最小包装数量',
    ext                       text                                    null comment '扩展信息（当前记录医保信息、追溯码信息）',
    remark                    varchar(255)  default ''                null comment '备注',
    creator                   varchar(64)   default ''                not null comment '创建者',
    create_time               datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                   varchar(64)   default ''                null comment '更新者',
    update_time               datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                   bit           default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_bill_no_product_pref_lot_no (tenant_id,bill_no,product_pref,lot_no)
) comment '收货单明细';

#（单体/总部/门店）退货单信息表
DROP TABLE IF EXISTS saas_purchase_return_bill;
create table saas_purchase_return_bill
(
    id                            bigint unsigned                         primary key comment '主键ID',
    bill_no                       varchar(32)   default ''                not null comment '单号',
    purchase_bill_no              varchar(32)   default ''                not null comment '采购单号',
    mall_order_no                 varchar(32)   default ''                not null comment '商城订单号',
    inbound_tenant_id             bigint        default 0                 not null comment '入库门店租户ID',
    tenant_id                     bigint        default 0                 not null comment '租户ID',
    tenant_type                   tinyint(4)    default 0                 not null comment '租户类型（1-单体门店、2-连锁门店、3-连锁总部）',
    head_tenant_id                bigint        default 0                 not null comment '总部租户ID',
    composite_bill_no             varchar(256)  default ''                  null comment '综合单据号（单号混合）',
    bill_type                     tinyint(4)    default 0                 not null comment '单据类型（1-采购退货单、2-门店退货单（要货退货）、3-门店调剂单）',
    status                        tinyint(2)    default 0                 not null comment '状态（1-待审批、2-待出库、3-待复核、4-已出库、5-已驳回、6-已撤销）',
    submitted                     bit           default b'0'              not null comment '已提交（类同于是否暂存）',
    supplier_guid                 varchar(32)   default ''                not null comment '供应商编码',
    supplier_name                 varchar(50)   default ''                not null comment '供应商名称',
    product_kind                  int           default 1                 not null comment '商品种类',
    return_content                varchar(2000) default ''                not null comment '退货内容',
    return_quantity               decimal(11,2) default 0.00              not null comment '退货数量',
    return_amount                 decimal(17,2) default 0.00              not null comment '退货总金额',
    cost_amount                   decimal(17,2) default 0.00              not null comment '成本总金额',
    operator                      varchar(64)   default ''                not null comment '出库操作员',
    operate_time                  datetime      default ''                not null comment '出库时间',
    checker                       varchar(64)   default ''                not null comment '复核员',
    check_time                    datetime      default null              null comment '复核时间',
    remark                        varchar(255)  default ''                null comment '备注',
    version                       int           default 1                 not null comment '版本(乐观锁)',
    creator                       varchar(64)   default ''                not null comment '创建者',
    create_time                   datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                       varchar(64)   default ''                null comment '更新者',
    update_time                   datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                       bit           default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_bill_no (tenant_id,bill_no)
) comment '退货单信息表';

#（单体/总部/门店）退货单详情表
DROP TABLE IF EXISTS saas_purchase_return_bill_detail;
create table saas_purchase_return_bill_detail
(
    id                            bigint unsigned                         primary key comment '主键ID',
    bill_no                       varchar(32)   default ''                not null comment '单号',
    tenant_id                     bigint        default 0                 not null comment '租户ID',
    product_pref                  varchar(30)   default ''                not null comment '商品编码',
    lot_no                        varchar(32)   default null              null comment '批号',
    production_date               date          default null              null COMMENT '生产日期（YYYY-MM-DD）',
    expiry_date                   date          default null              null COMMENT '有效期至（YYYY-MM-DD）',
    position_guid                 varchar(20)   default 0.00              null comment '总部（合格）货位编号',
    in_tax_rate                   decimal(4,2)  default 0.00              null comment '税率（百分比）',
    price                         decimal(10,4) default 0.00              null comment '含税成本价（单价）',
    out_tax_rate                  decimal(4,2)  default 0.00              null comment '销项税率',
    outbound_quantity             decimal(8,2)  default 1.00              not null comment '出库数量',
    outbound_price                decimal(10,4) default 0.00              null comment '出库单价',
    outbound_amount               decimal(14,2) default 0.00              null comment '出库总金额',
    channel_id                    varchar(32)   default ''                null comment '渠道ID',
    medicare_project_code         varchar(100)                            null comment '医保项目编码',
    medicare_project_name         varchar(256)                            null comment '医保项目名称',
    medicare_project_level        tinyint      default 0                  not null comment '医保项目等级',
    medicare_min_package_num      tinyint      default 0                  not null comment '医保最小包装数量',
    ext                           text                                    null comment '扩展信息（当前记录医保信息、快照信息）',
    return_reason                 tinyint(2)    default 0                 null comment '退货原因(1-破损、2-召回、3-滞销、4-过期失效、5-近效期、6-质量问题、7-其他)',
    remark                        varchar(255)  default ''                null comment '备注',
    creator                       varchar(64)   default ''                not null comment '创建者',
    create_time                   datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                       varchar(64)   default ''                null comment '更新者',
    update_time                   datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                       bit           default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_bill_no_product_pref_lot_no (tenant_id,bill_no,product_pref,lot_no)
) comment '退货单详情表';

#缺货单信息表
DROP TABLE IF EXISTS saas_purchase_stockout_bill;
create table saas_purchase_stockout_bill
(
    id                            bigint unsigned                         primary key comment '主键ID',
    bill_no                       varchar(32)   default ''                not null comment '单号',
    tenant_id                     bigint        default 0                 not null comment '租户ID',
    store_tenant_id               bigint        default 0                 not null comment '门店租户ID',
    requisition_bill_no           varchar(32)   default ''                not null comment '要货单号(连锁才有)',
    composite_bill_no             varchar(256)  default ''                null comment '综合单据号（单号混合）',
    status                        tinyint(2)    default 0                 not null comment '状态（1-缺货、2-已完成）',
    product_kind                  int           default 1                 not null comment '商品种类',
    stockout_content              varchar(2000) default ''                not null comment '缺货内容',
    stockout_quantity             decimal(11,2) default 1.00              not null comment '缺货数量',
    require_quantity              decimal(11,2) default 1.00              not null comment '要货数量',
    remark                        varchar(255)  default ''                null comment '备注',
    version                       int           default 1                 not null comment '版本(乐观锁)',
    creator                       varchar(64)   default ''                not null comment '创建者',
    create_time                   datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                       varchar(64)   default ''                null comment '更新者',
    update_time                   datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                       bit           default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_bill_no (tenant_id,bill_no)
) comment '缺货单信息表';

#缺货单详情表
DROP TABLE IF EXISTS saas_purchase_stockout_bill_detail;
create table saas_purchase_stockout_bill_detail
(
    id                            bigint unsigned                         primary key comment '主键ID',
    bill_no                       varchar(32)   default ''                not null comment '单号',
    tenant_id                     bigint        default 0                 not null comment '租户ID',
    product_pref                  varchar(30)   default ''                not null comment '商品编码',
    require_quantity              decimal(8,2)  default 1.00              not null comment '要货数量',
    stockout_quantity             decimal(8,2)  default 1.00              not null comment '缺货数量',
    medicare_project_code         varchar(100)                            null comment '医保项目编码',
    medicare_project_name         varchar(256)                            null comment '医保项目名称',
    medicare_project_level        tinyint      default 0                  not null comment '医保项目等级',
    medicare_min_package_num      tinyint      default 0                  not null comment '医保最小包装数量',
    ext                           text                                    null comment '扩展信息（当前记录医保信息、快照信息）',
    remark                        varchar(255)  default ''                null comment '备注',
    creator                       varchar(64)   default ''                not null comment '创建者',
    create_time                   datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                       varchar(64)   default ''                null comment '更新者',
    update_time                   datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                       bit           default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_bill_no_product_pref (tenant_id,bill_no,product_pref)
) comment '缺货单详情表';

#采购扩展信息-三方erp单据信息
DROP TABLE IF EXISTS saas_purchase_erp_bill;
create table saas_purchase_erp_bill
(
    id                            bigint unsigned                         primary key comment '主键ID',
    bill_no                       varchar(32)   default ''                not null comment '采购单/收货单/配送单号',
    tenant_id                     bigint        default 0                 not null comment '租户ID',
    erp_bill_no                   varchar(32)   default ''                not null comment '三方erp订单号',
    sales_bill_no                 varchar(32)   default ''                not null comment '三方erp销售单号',
    outbound_bill_no              varchar(32)   default ''                not null comment '三方erp出库单号',
    warehouse_bill_no             varchar(32)   default ''                not null comment '三方erp入库单号',
    refund_storage_bill_no        varchar(32)   default ''                not null comment '三方erp销售退回入库单号(神农XSTHRK)',
    composite_bill_no             varchar(256)  default ''                null comment '综合单据号（单号混合）',
    cancel_reason                 varchar(256)  default ''                not null comment '三方erp取消原因',
    remark                        varchar(255)  default ''                null comment '备注',
    creator                       varchar(64)   default ''                not null comment '创建者',
    create_time                   datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                       varchar(64)   default ''                null comment '更新者',
    update_time                   datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                       bit           default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_bill_no (tenant_id,bill_no)
) comment '采购-三方erp单据信息';

#采购扩展信息-运输信息表
DROP TABLE IF EXISTS saas_purchase_transport;
CREATE TABLE saas_purchase_transport (
    id                            bigint unsigned                         primary key comment '主键ID',
    bill_no                       varchar(32)   default ''                not null comment '采购单/收货单/配送单号',
    tenant_id                     bigint        default 0                 not null comment '租户ID',
    authorized_representative     varchar(100)  default ''                null comment '委托经办人',
    carrier_name                  varchar(100)  default ''                null comment '承运人',
    carrier_entity                varchar(100)  default ''                null comment '承运单位名称',
    carrier_entity_uscc           varchar(100)  default ''                null comment '承运单位统一社会信用代码（国家标准长度）',
    transport_mode                varchar(20)   default ''                null comment '运输方式（如：road/air/ship）',
    departure_address             varchar(100)  default ''                null comment '启运地址',
    departure_time                datetime      default null              null comment '实际启运时间',
    departure_temperature         decimal(5,2)  default 0.00              null comment '启运温度（℃）',
    departure_humidity            decimal(5,2)  default 0.00              null comment '启运湿度（%）',
    tracking_no                   varchar(32)   default null              null comment '运单号',
    transport_vehicle             tinyint(2)    default 0                 null comment '运输工具类型（1-汽运、2-货运、3-冷藏车、4-冷藏箱、5-保温箱、6-其他）',
    vehicle_identifier            varchar(20)   default ''                null comment '运输工具标识（车牌号/航班号等）',
    driver                        varchar(20)   default ''                null comment '驾驶员姓名',
    driver_credential             varchar(255)  default ''                null comment '驾驶员证件',
    transport_temperature_monitor text                                    null comment '运输温度（℃）监控（json格式，每天的温度）',
    transport_humidity_monitor    text                                    null comment '运输湿度（%）监控（json格式，每天的湿度）',
    arrival_time                  datetime      default null              null comment '到货时间',
    arrival_temperature           decimal(5,2)  default 0.00              null comment '到货温度（℃）',
    arrival_humidity              decimal(5,2)  default 0.00              null comment '到货湿度（%）',
    shipment_time                 datetime      default null              null comment '随货同行单时间',
    shipment_file_url             varchar(300)  default ''                null comment '随货同行单文件地址',
    remark                        varchar(255)  default ''                null comment '备注',
    creator                       varchar(64)   default ''                not null comment '创建者',
    create_time                   datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                       varchar(64)   default ''                null comment '更新者',
    update_time                   datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                       bit           default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_bill_no (tenant_id,bill_no)
) COMMENT '采购-运输信息表';

#采购扩展信息-发票信息表
DROP TABLE IF EXISTS saas_purchase_invoice;
create table saas_purchase_invoice
(
    id                        bigint unsigned                         primary key comment '主键ID',
    bill_no                   varchar(32)   default ''                not null comment '采购单/收货单/配送单号',
    tenant_id                 bigint        default 0                 not null comment '租户ID',
    invoice_no                varchar(32)   default ''                null comment '发票号',
    invoice_code              varchar(32)   default ''                null comment '发票代码',
    invoice_type              varchar(32)   default ''                null comment '发票类型 1：电子普通发票 2：增值税发票 3：纸质普通发票，用于委托配送',
    accompanying_shipment     bit           default b'0'              null comment '发票是否随货通行 0：不随行 1：随行，用于委托配送',
    invoice_amount            decimal(14,2) default 0.00              null comment '发票金额',
    invoice_file_name         varchar(300)  default ''                null comment '发票文件名称',
    invoice_file_url          varchar(300)  default ''                null comment '发票文件地址',
    issued_time               datetime      default null              null comment '实际开(发)票时间',
    remark                    varchar(255)  default ''                null comment '备注',
    creator                   varchar(64)   default ''                not null comment '创建者',
    create_time               datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                   varchar(64)   default ''                null comment '更新者',
    update_time               datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                   bit           default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_bill_no (tenant_id,bill_no)
) comment '采购-发票信息表';
