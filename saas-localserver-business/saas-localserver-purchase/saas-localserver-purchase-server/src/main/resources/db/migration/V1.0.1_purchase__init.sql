#供应商信息表
DROP TABLE IF EXISTS saas_purchase_supplier;
create table saas_purchase_supplier (
    id                                         bigint        default 0                 not null comment '主键ID',
    pref                                       varchar(32)   default ''                not null comment '供应商编码(租户本地新建供应商生成规则：GYSC+租户id(来源)+机器码+日期+4位流水)',
    ec_supplier_pref                           varchar(32)   default ''                not null comment '商城供应商编码',
    name                                       varchar(256)  default ''                null comment '供应商名称',
    type                                       int(11)       default null              null comment '供应商类别（字典配置-20048）',
    mnemonic_code                              varchar(256)  default ''                null comment '助记码（混合查询条件）',
    system_default                             bit           default b'1'              not null comment '系统默认',
    source                                     bigint        default 0                 null comment '来源（非系统默认供应商，数据来源默认为租户id）',
    legal_representative                       varchar(256)  default ''                null comment '法定代表人',
    registered_address                         varchar(256)  default ''                null comment '注册地址',
    business_scope                             varchar(2048) default ''                null comment '经营范围（字典配置-10005）',
    business_license                           varchar(256)  default ''                null comment '营业执照编码',
    licence_authority                          varchar(256)  default ''                null comment '发证机关',
    registered_date                            date          default null              null comment '注册日期',
    expiration_date                            date          default null              null comment '有效期至',
    expiration_date_type                       tinyint(4)    default null              null comment '有效期至方式:1--长期，2--手填，当为2的时候，expiration_date必须有值',
    tri_cert_merged                            bit           default b'0'              not null comment '是否三证合一：0--否，1--是',
    deposit_bank                               varchar(256)  default ''                null comment '开户银行',
    bank_account                               varchar(256)  default ''                null comment '银行账号',
    account_name                               varchar(256)  default ''                null comment '开户户名',
    organization_certification_code            varchar(256)  default ''                null comment '组织机构代码',
    organization_certification_date            date          default null              null comment '组织机构发证日期',
    organization_certification_expiration_date date          default null              null comment '组织机构有效期至',
    organization_certification_tax_no          varchar(256)  default ''                null comment '组织机构税务登记号',
    organization_certification_authority       varchar(256)  default ''                null comment '组织机构代码证发证机关',
    registered_address_cod                     varchar(30)   default ''                not null comment '注册地址code',
    store_address_code                         varchar(30)   default ''                not null comment '仓库地址code',
    store_address                              varchar(256)  default ''                not null comment '仓库详情地址',
    signet                                     varchar(2048) default ''                not null comment '印章印模附件',
    shipment_template                          varchar(2048) default ''                not null comment '随货同行单样式附件',
    qualification_infos                        text                                    null comment '资质与经营范围json数据',
    proxy_ID_card                              varchar(32)   default ''                not null comment '委托人身份证号',
    proxy_ID_card_expiration_date              date          default null              null comment '委托人身份证有效期',
    msfx_ref_ent_id                            varchar(128)  default ''                null comment '码上放心-企业唯一标识',
    msfx_ent_id                                varchar(128)  default ''                null comment '码上放心-企业ID',
    relate_distribute                          bit default b'0'                         not null comment '关联分发业务',
    remark                                     text                                    null comment '备注（存json数据）',
    disabled                                   bit           default b'0'              not null comment '是否禁用',
    creator                                    varchar(64)   default ''                not null comment '创建者',
    create_time                                datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                                    varchar(64)   default ''                null comment '更新者',
    update_time                                datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                                    bit           default b'0'              not null comment '是否删除',
unique key uk_pref (pref)
) comment = '供应商信息表';

#租户(单体/总部)-供应商-销售人员信息
DROP TABLE IF EXISTS saas_purchase_tenant_supplier_sales;
create table saas_purchase_tenant_supplier_sales (
    id                                bigint        default 0                 not null comment '（云端）主键ID',
    ls_id                             bigint unsigned                         primary key comment '租户本地主键ID',
    tenant_id                         bigint        default 0                 not null comment '租户ID',
    supplier_pref                     varchar(32)   default ''                not null comment '供应商编号',
    sales_name                        varchar(128)  default ''                not null comment '销售人员姓名',
    authorized_area                   varchar(128)  default ''                not null comment '授权区域',
    authorization_num                 varchar(128)  default ''                not null comment '授权书号',
    authorization_num_expiration_date datetime      default null              null comment '授权书号有效期',
    phone_number                      varchar(20)   default ''                not null comment '手机号码',
    authorized_varieties              varchar(255)  default ''                not null comment '授权信息',
    id_card                           varchar(36)   default ''                not null comment '身份证号',
    id_card_expiration_date           datetime      default null              null comment '身份证有效期',
    id_card_attachment                varchar(2048) default ''                not null comment '身份证附件',
    authorization_attachment          varchar(2048) default ''                not null comment '授权书附件',
    authorized_scope                  varchar(2048) default ''                not null COMMENT '经营范围',
    creator                           varchar(64)   default ''                not null comment '创建者',
    create_time                       datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                           varchar(64)   default ''                null comment '更新者',
    update_time                       datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                           bit           default b'0'              not null comment '是否删除'
) comment = '租户-供应商-销售人员信息';

#租户(单体/总部)-供应商-关联关系
DROP TABLE IF EXISTS saas_purchase_tenant_supplier_relation;
create table saas_purchase_tenant_supplier_relation (
    id                                bigint          default 0                 not null comment '（云端）主键ID',
    ls_id                             bigint unsigned                           primary key comment '租户本地主键ID',
    tenant_id                         bigint          default 0                 not null comment '租户ID',
    supplier_pref                     varchar(32)     default ''                not null comment '供应商编号',
    status                            tinyint(2)      default 0                 not null comment '首营状态：1-审核中，2-审核通过，3-审核未通过',
    creator                           varchar(64)     default ''                not null comment '创建者',
    create_time                       datetime        default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                           varchar(64)     default ''                null comment '更新者',
    update_time                       datetime        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                           bit             default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_supplier_pref (tenant_id,supplier_pref)
) comment = '租户-供应商-关联关系';

#采购单表
DROP TABLE IF EXISTS saas_purchase_bill;
create table saas_purchase_bill
(
    id                            bigint          default 0                 not null comment '（云端）主键ID',
    ls_id                         bigint unsigned                           primary key comment '租户本地主键ID',
    plan_bill_no                  varchar(32)     default ''                not null comment '计划单号',
    purchase_bill_no                 varchar(32)     default ''                not null comment '订单号',
    tenant_id                     bigint          default 0                 not null comment '租户ID',
    tenant_type                   tinyint(4)      default 0                 not null comment '租户类型（1-单体门店、2-连锁门店、3-连锁总部）',
    requisition_bill_no           varchar(20)     default ''                not null comment '要货单号(连锁总部才有)',
    remote_receive_bill_no        varchar(20)     default ''                not null comment '远程收货单号',
    composite_bill_no             varchar(256)    default ''                null comment '综合单据号（单号混合）',
    bill_source                   tinyint(4)      default 0                 not null comment '单据来源（0-采购计划、1-一键入库（单体）、2-药帮忙一键入库（单体））',
    ec_order_no                   varchar(32)     default ''                not null comment '商城订单号',
    purchase_mode                 tinyint(2)      default 1                 not null comment '采购方式（0-线下采购、1-无仓（B2B 无仓）、2-共仓（B2C 共仓））',
    purchase_phase                tinyint(2)      default 0                 not null comment '采购阶段（0-暂存、1-提交计划、2-待下单、3-已下单）',
    status                        tinyint(2)      default 0                 not null comment '采购订单状态（0-暂存、1-待审批、2-采购中、3-已采购、4-已完成、5-采购失败、6-已驳回、7-已撤销）',
    medicine_type                 tinyint(2)      default 1                 not null comment '药品类型（1-中药、2-非中药）',
    supplier_pref                 varchar(32)     default ''                not null comment '供应商编码',
    supplier_name                 varchar(50)     default ''                not null comment '供应商名称',
    product_kind                  int             default 1                 not null comment '商品种类',
    purchase_content              varchar(2000)   default ''                not null comment '采购内容',
    purchase_quantity             decimal(11,2)   default 0.00              null comment '采购总数量（原单退货扣减可退数量）',
    purchase_amount               decimal(17,2)   default 0.00              not null comment '采购金额（总金额）',
    returnable_quantity           decimal(8,2)    default 0.00              null comment '可退总数量（原单退货扣减可退数量，下游收货单的实际入库数量）',
    planner                       varchar(64)     default ''                not null comment '计划员',
    plan_time                     datetime        default null              null comment '计划时间',
    purchaser                     varchar(64)     default ''                not null comment '采购员',
    purchase_time                 datetime        default null              null comment '采购时间',
    upload                        bit             default b'0'              not null comment '是否上报（财务等）',
    remark                        varchar(255)    default ''                null comment '备注',
    version                       int             default 1                 not null comment '版本(乐观锁)',
    creator                       varchar(64)     default ''                not null comment '创建者',
    create_time                   datetime        default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                       varchar(64)     default ''                null comment '更新者',
    update_time                   datetime        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                       bit             default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_plan_bill_no (tenant_id,plan_bill_no)
) comment '采购单表';

CREATE INDEX idx_tenant_id_purchase_bill_no
    ON saas_purchase_bill (tenant_id, purchase_bill_no);

#采购订单明细表
DROP TABLE IF EXISTS saas_purchase_bill_detail;
create table saas_purchase_bill_detail
(
    id                       bigint        default 0                 not null comment '（云端）主键ID',
    ls_id                    bigint unsigned                         primary key comment '租户本地主键ID',
    plan_bill_no             varchar(32)     default ''              not null comment '计划单号',
    purchase_bill_no            varchar(32)     default ''              not null comment '订单号',
    tenant_id                bigint        default 0                 not null comment '租户ID',
    product_pref             varchar(30)   default ''                not null comment '商品编码',
    price                    decimal(10,4) default 0.00              null comment '含税成本价（单价）',
    tax_rate                 decimal(4,2)  default 0.00              null comment '税率（百分比）',
    purchase_amount          decimal(14,2) default 0.00              null comment '含税成本金额（总金额）',
    purchase_quantity        decimal(8,2)  default 1.00              not null comment '采购数量',
    returnable_quantity      decimal(8,2)  default 0.00              null comment '可退数量（原单退货扣减可退数量，下游收货单的实际入库数量）',
    channel_id               varchar(32)   default ''                null comment '渠道ID',
    source_line_no           varchar(256)  default ''                null comment '源行号（和三方ERP对接时需要）',
    medicare_project_code    varchar(100)                            null comment '医保项目编码',
    medicare_project_name    varchar(256)                            null comment '医保项目名称',
    medicare_project_level   tinyint      default 0                  not null comment '医保项目等级',
    medicare_min_package_num tinyint      default 0                  not null comment '医保最小包装数量',
    ext                      text                                    null comment '扩展信息（当前商品信息）',
    remark                   varchar(255)  default ''                null comment '备注',
    creator                  varchar(64)   default ''                not null comment '创建者',
    create_time              datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                  varchar(64)   default ''                null comment '更新者',
    update_time              datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                  bit           default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_bill_no_product_pref (tenant_id,plan_bill_no,product_pref)
) comment '采购明细表';
CREATE INDEX idx_tenant_id_purchase_bill_no_product_pref
    ON saas_purchase_bill_detail (tenant_id,purchase_bill_no,product_pref);

#收货单（包含收货复核、入库验收、入库。这三步流程的单据）
DROP TABLE IF EXISTS saas_purchase_inbound_verification_bill;
create table saas_purchase_inbound_verification_bill
(
    id                            bigint        default 0                 not null comment '（云端）主键ID',
    ls_id                         bigint unsigned                         primary key comment '租户本地主键ID',
    bill_no                       varchar(32)   default ''                not null comment '单号',
    tenant_id                     bigint        default 0                 not null comment '租户ID',
    tenant_type                   tinyint(4)    default 0                 not null comment '租户类型（1-单体门店、2-连锁门店、3-连锁总部）',
    head_tenant_id                bigint        default 0                 not null comment '总部租户ID',
    purchase_bill_no                 varchar(32)   default ''                not null comment '采购订单号（单体/连锁总部有）',
    requisition_bill_no           varchar(32)   default ''                not null comment '要货单号(连锁门店才有)',
    remote_receive_bill_no        varchar(32)   default ''                not null comment '远程收货单号（连锁门店、总部都有）',
    dispatch_bill_no              varchar(32)   default ''                not null comment '总部出库单号（连锁门店才有）',
    return_bill_no                varchar(32)   default ''                not null comment '门店退货申请单号（连锁总部有）',
    rejection_bill_no             varchar(32)   default ''                not null comment '拒收收货单号（连锁总部/连锁门店有）',
    shipment_no                   varchar(30)   default ''                null comment '随货同行单号',
    composite_bill_no             varchar(256)  default ''                null comment '综合单据号（单号混合）',
    bill_source                   tinyint(4)    default 0                 not null comment '单据来源（0-药品采购（单体）、1-药品采购（总部）、2-门店拒收（总部入库）、3-门店退货（总部入库）、4-门店调剂（总部入库）、5-门店要货（门店入库）、6-总部拒收（门店入库）、7-总部铺货（门店入库）、8-门店调剂（门店入库））',
    ec_order_no                   varchar(32)   default ''                not null comment '商城订单号',
    purchase_mode                 tinyint(2)    default 1                 not null comment '采购方式（0-线下采购、1-无仓（B2B 无仓）、2-共仓（B2C 共仓））',
    status                        tinyint(2)    default 0                 not null comment '状态（0-待收货、1-待验收、2-待入库、3-已入库、4-已拒收）',
    supplier_pref                 varchar(32)   default ''                not null comment '供应商编码',
    supplier_name                 varchar(50)   default ''                not null comment '供应商名称',
    supplier_sales                varchar(50)   default ''                not null comment '供应商销售员',
    product_kind                  int           default 1                 not null comment '商品种类',
    receive_quantity              decimal(11,2) default 1.00              not null comment '收货数量',
    discount                      decimal(4,2)  default 0.00              null comment '折扣（百分比）',
    discount_amount             decimal(17,2) default 0.00              null comment '折扣总金额',
    receive_content               varchar(2000) default ''                not null comment '收货内容',
    receive_amount                decimal(17,2) default 0.00              not null comment '收货金额（折后总金额）',
    receiver                      varchar(64)   default ''                not null comment '收货员',
    receive_time                  datetime      default null              null comment '收货时间',
    accepter                      varchar(64)   default ''                not null comment '验收员',
    accept_time                   datetime      default null              null comment '验收时间',
    warehouser                    varchar(64)   default ''                not null comment '入库员',
    warehouse_time                datetime      default null              null comment '入库时间',
    quality_inspector             varchar(300)  default ''                null comment '质检员',
    quality_inspection_report     varchar(300)  default ''                null comment '质检报告单',
    upload                        bit           default b'0'              not null comment '是否上报（财务等）',
    remark                        varchar(255)  default ''                null comment '备注',
    version                       int           default 1                 not null comment '版本(乐观锁)',
    creator                       varchar(64)   default ''                not null comment '创建者',
    create_time                   datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                       varchar(64)   default ''                null comment '更新者',
    update_time                   datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                       bit           default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_bill_no (tenant_id,bill_no)
) comment '收货单';

#收货单明细（包含收货复核、入库验收、入库。这三步流程的单据）
DROP TABLE IF EXISTS saas_purchase_inbound_verification_bill_detail;
create table saas_purchase_inbound_verification_bill_detail
(
    id                        bigint        default 0                 not null comment '（云端）主键ID',
    ls_id                     bigint unsigned                         primary key comment '租户本地主键ID',
    bill_no                   varchar(32)   default ''                not null comment '单号',
    tenant_id                 bigint        default 0                 not null comment '租户ID',
    product_pref              varchar(30)   default ''                not null comment '商品编码',
    lot_no                    varchar(32)   default null              null comment '批号',
    production_date           date          default null              null COMMENT '生产日期（YYYY-MM-DD）',
    expiry_date               date          default null              null COMMENT '有效期至（YYYY-MM-DD）',
    price                     decimal(10,4) default 0.00              null comment '含税成本价（单价）',
    tax_rate                  decimal(4,2)  default 0.00              null comment '税率（百分比）',
    discount                  decimal(4,2)  default 0.00              null comment '折扣（百分比）',
    discounted_price          decimal(14,2) default 0.00              null comment '折后含税单价',
    arrive_quantity           decimal(8,2) default 1.00               not null comment '到货数量',
    receive_quantity          decimal(8,2) default 1.00               not null comment '收货数量',
    reject_quantity           decimal(8,2) default 1.00               not null comment '拒收数量',
    receive_amount            decimal(14,2) default 0.00              null comment '收货金额（折后单价*收货数量）',
    accept_conclusion         tinyint(1)     default 1                null comment '验收结论（1-合格、0-锁定）',
    sample_quantity           decimal(8,2) default 1.00               not null comment '抽样数量',
    unqualified_quantity      decimal(8,2) default 1.00               not null comment '不合格品数量',
    unqualified_amount        decimal(14,2) default 0.00              not null comment '不合格品总金额（折后总金额）',
    unqualified_reason        varchar(256)  default ''                null comment '不合格原因',
    unqualified_location_pref varchar(20)   default 0.00              null comment '不合格品隔离区编码',
    qualified_quantity        decimal(8,2) default 1.00               not null comment '合格品数量',
    qualified_amount          decimal(14,2) default 1                 not null comment '合格品总金额（折后总金额）',
    qualified_location_pref   varchar(20)   default 0.00              null comment '合格品储存区编码',
    warehouse_quantity        decimal(8,2) default 1.00               not null comment '入库数量',
    warehouse_amount          decimal(14,2) default 0.00              null comment '入库金额（折后总金额）',
#     return_reason             tinyint(2)    default 0                 null comment '退货原因(1-破损、2-召回、3-滞销、4-过期失效、5-近效期、6-质量问题、7-其他)',
    treatment                 varchar(256)  default ''                null comment '处理措施',
    channel_id                varchar(32)   default ''                null comment '渠道ID',
    sterilization_batch_no    varchar(32)   default ''                null comment '灭菌批次',
    source_line_no            varchar(256)  default ''                null comment '源行号（和三方ERP对接时需要）',
    medicare_project_code     varchar(100)                            null comment '医保项目编码',
    medicare_project_name     varchar(256)                            null comment '医保项目名称',
    medicare_project_level    tinyint      default 0                  not null comment '医保项目等级',
    medicare_min_package_num  tinyint      default 0                  not null comment '医保最小包装数量',
    ext                       text                                    null comment '扩展信息（当前记录医保信息、追溯码信息）',
    remark                    varchar(255)  default ''                null comment '备注',
    creator                   varchar(64)   default ''                not null comment '创建者',
    create_time               datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                   varchar(64)   default ''                null comment '更新者',
    update_time               datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                   bit           default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_bill_no_product_pref (tenant_id,bill_no,product_pref)
) comment '收货单明细表';

#（连锁门店）要货单信息表
DROP TABLE IF EXISTS saas_purchase_requisition_bill;
create table saas_purchase_requisition_bill
(
    id                            bigint        default 0                 not null comment '（云端）主键ID',
    ls_id                         bigint unsigned                         primary key comment '租户本地主键ID',
    bill_no                       varchar(32)   default ''                not null comment '单号',
    tenant_id                     bigint        default 0                 not null comment '租户ID',
    head_tenant_id                bigint        default 0                 not null comment '总部租户ID',
    outbound_tenant_id            bigint        default 0                 not null comment '出库租户ID（调剂）',
    stockout_bill_no              varchar(32)   default ''                not null comment '缺货单号',
    bill_source                   tinyint(4)    default 0                 not null comment '单据来源（0-门店要货、1-门店调剂）',
    requisition_content           varchar(2000) default ''                not null comment '要货内容',
    requisition_quantity          varchar(2000) default ''                not null comment '要货数量',
    requisition_amount            varchar(2000) default ''                not null comment '要货金额（总金额）',
    returnable_quantity           decimal(11,2) default 0.00              null comment '可退总数量（原单退货扣减可退数量，下游收货单的实际入库数量）',
    product_kind                  int           default 1                 not null comment '商品种类',
    receiver                      varchar(64)   default ''                not null comment '收货人',
    receiver_phone                varchar(20)   default ''                not null comment '收货人电话',
    receiver_area                 varchar(100)  default ''                not null comment '收货人所在区域',
    receiver_address              varchar(100)  default ''                not null comment '收货人地址',
    status                        tinyint(2)    default 0                 not null comment '状态（0-暂存、1-待审批、2-待配送、3-待收货、4-部分收货、5-已完成、6-已撤销、7-已驳回）',
    remark                        varchar(255)  default ''                null comment '备注',
    version                       int           default 1                 not null comment '版本(乐观锁)',
    creator                       varchar(64)   default ''                not null comment '创建者',
    create_time                   datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                       varchar(64)   default ''                null comment '更新者',
    update_time                   datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                       bit           default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_bill_no (tenant_id,bill_no)
) comment '（连锁门店）要货单信息表';

#（连锁门店）要货单明细表
DROP TABLE IF EXISTS saas_purchase_requisition_bill_detail;
create table saas_purchase_requisition_bill_detail
(
    id                            bigint        default 0                 not null comment '（云端）主键ID',
    ls_id                         bigint unsigned                         primary key comment '租户本地主键ID',
    bill_no                       varchar(32)   default ''                not null comment '单号',
    tenant_id                     bigint        default 0                 not null comment '租户ID',
    product_pref                  varchar(30)   default ''                not null comment '商品编码',
    price                         decimal(10,4) default 0.00              null comment '含税成本价（单价）',
    tax_rate                      decimal(4,2)  default 0.00              null comment '税率（百分比）',
    requisition_quantity          decimal(8,2)  default 1.00              not null comment '要货数量',
    requisition_amount            decimal(14,2) default 0.00              null comment '含税成本金额（总金额）',
    delivered_quantity            decimal(8,2)  default 1.00              not null comment '已配送数量',
    returnable_quantity           decimal(8,2)  default 0.00              null comment '可退数量（原单退货扣减可退数量，下游收货单的实际入库数量）',
    channel_id                    varchar(32)   default ''                null comment '渠道ID',
    medicare_project_code         varchar(100)                            null comment '医保项目编码',
    medicare_project_name         varchar(256)                            null comment '医保项目名称',
    medicare_project_level        tinyint       default 0                 not null comment '医保项目等级',
    medicare_min_package_num      tinyint       default 0                 not null comment '医保最小包装数量',
    ext                           text                                    null comment '扩展信息（当前记录医保信息、快照信息）',
    remark                        varchar(255)  default ''                null comment '备注',
    version                       int           default 1                 not null comment '版本(乐观锁)',
    creator                       varchar(64)   default ''                not null comment '创建者',
    create_time                   datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                       varchar(64)   default ''                null comment '更新者',
    update_time                   datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                       bit           default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_bill_no_product_pref (tenant_id,bill_no,product_pref)
) comment '（连锁门店）要货单明细表';

#（连锁总部）配送单信息表
DROP TABLE IF EXISTS saas_purchase_delivery_bill;
create table saas_purchase_delivery_bill
(
    id                            bigint        default 0                 not null comment '（云端）主键ID',
    ls_id                         bigint unsigned                         primary key comment '租户本地主键ID',
    bill_no                       varchar(32)   default ''                not null comment '单号',
    tenant_id                     bigint        default 0                 not null comment '租户ID',
    requisition_bill_no           varchar(32)   default ''                not null comment '要货单号',
    stockout_bill_no              varchar(32)   default ''                not null comment '缺货单号',
    remote_receive_bill_no        varchar(32)   default ''                not null comment '远程收货单号',
    composite_bill_no             varchar(256)  default ''                null comment '综合单据号（单号混合）',
    bill_source                   tinyint(4)    default 0                 not null comment '单据来源（0-门店要货（总部出库）、1-总部铺货（总部出库）、2-门店调剂（总部出库））',
    bill_time                     datetime      default CURRENT_TIMESTAMP not null comment '制单时间',
    delivery_time                 datetime      default CURRENT_TIMESTAMP not null comment '配送时间',
    delivery_content              varchar(2000) default ''                not null comment '配送内容',
    delivery_quantity             decimal(11,2) default ''                not null comment '配送数量',
    delivery_amount               decimal(17,2) default ''                not null comment '配送金额（总金额）',
    product_kind                  int           default 1                 not null comment '商品种类',
    checker                       varchar(64)   default ''                not null comment '复核员',
    receive_tenant_id             bigint        default 0                 not null comment '收货租户ID',
    receiver_phone                varchar(20)   default ''                not null comment '收货人电话',
    receiver_area                 varchar(100)  default ''                not null comment '收货人所在区域',
    receiver_address              varchar(100)  default ''                not null comment '收货人地址',
    outbound_bill_nos             varchar(1000) default 1                 null comment '出库单编号（用,隔开）',
    remark                        varchar(255)  default ''                null comment '备注',
    version                       int           default 1                 not null comment '版本(乐观锁)',
    creator                       varchar(64)   default ''                not null comment '创建者',
    create_time                   datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                       varchar(64)   default ''                null comment '更新者',
    update_time                   datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                       bit           default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_bill_no (tenant_id,bill_no)
) comment '（连锁总部）配送单信息表';

#（连锁总部）配送单明细表
DROP TABLE IF EXISTS saas_purchase_delivery_bill_detail;
create table saas_purchase_delivery_bill_detail
(
    id                            bigint        default 0                 not null comment '（云端）主键ID',
    ls_id                         bigint unsigned                         primary key comment '租户本地主键ID',
    bill_no                       varchar(32)   default ''                not null comment '单号',
    tenant_id                     bigint        default 0                 not null comment '租户ID',
    product_pref                  varchar(30)   default ''                not null comment '商品编码',
    lot_no                        varchar(32)   default null              null comment '批号',
    production_date               date          default null              null COMMENT '生产日期（YYYY-MM-DD）',
    expiry_date                   date          default null              null COMMENT '有效期至（YYYY-MM-DD）',
    location_pref                 varchar(20)   default 0.00              null comment '（合格）存储区编号',
    in_tax_rate                   decimal(4,2)  default 0.00              null comment '进项税率（百分比）',
    price                         decimal(10,4) default 0.00              null comment '含税成本价（单价）',
    out_tax_rate                  decimal(4,2) default 0.00               null comment '销项税率',
    delivery_price                decimal(10,4) default 0.00              null comment '配送含税价（总部出给门店的价格）',
    delivery_quantity             decimal(8,2) default 1.00               not null comment '配送数量',
    delivery_amount               decimal(14,2) default 0.00              null comment '含税成本金额（总金额）',
    delivered_quantity            decimal(8,2) default 1.00               not null comment '已配送数量',
    delivered_amount              decimal(14,2) default 0.00              null comment '含税成本金额（已配送）',
    channel_id                    varchar(32)   default ''                null comment '渠道ID',
    medicare_project_code         varchar(100)                            null comment '医保项目编码',
    medicare_project_name         varchar(256)                            null comment '医保项目名称',
    medicare_project_level        tinyint      default 0                  not null comment '医保项目等级',
    medicare_min_package_num      tinyint      default 0                  not null comment '医保最小包装数量',
    ext                           text                                    null comment '扩展信息（当前记录医保信息、快照信息）',
    remark                        varchar(255)  default ''                null comment '备注',
    version                       int           default 1                 not null comment '版本(乐观锁)',
    creator                       varchar(64)   default ''                not null comment '创建者',
    create_time                   datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                       varchar(64)   default ''                null comment '更新者',
    update_time                   datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                       bit           default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_bill_no_product_pref_lot_no (tenant_id,bill_no,product_pref,lot_no)
) comment '（连锁总部）配送单明细表';

#（连锁总部）出库单信息表
DROP TABLE IF EXISTS saas_purchase_outbound_bill;
create table saas_purchase_outbound_bill
(
    id                            bigint        default 0                 not null comment '（云端）主键ID',
    ls_id                         bigint unsigned                         primary key comment '租户本地主键ID',
    bill_no                       varchar(32)   default ''                not null comment '单号',
    tenant_id                     bigint        default 0                 not null comment '租户ID',
    tenant_type                   tinyint(4)    default 3                 not null comment '租户类型（3-连锁总部）',
    delivery_bill_no              varchar(32)   default ''                not null comment '配送单号',
    bill_source                   tinyint(4)    default 0                 not null comment '单据来源（0-（总部）配送出库）',
    outbound_content              varchar(2000) default ''                not null comment '出库内容',
    outbound_quantity             decimal(11,2) default ''                not null comment '出库数量',
    outbound_amount               decimal(17,2) default ''                not null comment '出库金额（总金额）',
    product_kind                  int           default 1                 not null comment '商品种类',
    checker                       varchar(64)   default ''                not null comment '复核员',
    remark                        varchar(255)  default ''                null comment '备注',
    creator                       varchar(64)   default ''                not null comment '创建者',
    create_time                   datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                       varchar(64)   default ''                null comment '更新者',
    update_time                   datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                       bit           default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_bill_no (tenant_id,bill_no)
) comment '（连锁总部）出库单信息表';

#（连锁总部）出库单详情表
DROP TABLE IF EXISTS saas_purchase_outbound_bill_detail;
create table saas_purchase_outbound_bill_detail
(
    id                            bigint        default 0                 not null comment '（云端）主键ID',
    ls_id                         bigint unsigned                         primary key comment '租户本地主键ID',
    bill_no                       varchar(20)   default ''                not null comment '单号',
    tenant_id                     bigint        default 0                 not null comment '租户ID',
    product_pref                  varchar(30)   default ''                not null comment '商品编码',
    lot_no                        varchar(32)   default null              null comment '批号',
    production_date               date          default null              null COMMENT '生产日期（YYYY-MM-DD）',
    expiry_date                   date          default null              null COMMENT '有效期至（YYYY-MM-DD）',
    location_pref                 varchar(20)   default 0.00              null comment '总部（合格）存储区编号',
    in_tax_rate                   decimal(4,2)  default 0.00              null comment '税率（百分比）',
    price                         decimal(10,4) default 0.00              null comment '含税成本价（单价）',
    out_tax_rate                  decimal(4,2) default 0.00              null comment '销项税率',
    outbound_quantity             decimal(8,2) default 1.00              not null comment '出库数量',
    outbound_price                decimal(10,4) default 0.00              null comment '售价（总部出给门店的价格）',
    outbound_amount               decimal(14,2) default 0.00              null comment '含税成本金额（总金额）',
    channel_id                    varchar(32)   default ''                null comment '渠道ID',
    medicare_project_code         varchar(100)                            null comment '医保项目编码',
    medicare_project_name         varchar(256)                            null comment '医保项目名称',
    medicare_project_level        tinyint      default 0                  not null comment '医保项目等级',
    medicare_min_package_num      tinyint      default 0                  not null comment '医保最小包装数量',
    ext                           text                                    null comment '扩展信息（当前记录医保信息、快照信息）',
    remark                        varchar(255)  default ''                null comment '备注',
    creator                       varchar(64)   default ''                not null comment '创建者',
    create_time                   datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                       varchar(64)   default ''                null comment '更新者',
    update_time                   datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                       bit           default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_bill_no_product_pref_lot_no (tenant_id,bill_no,product_pref,lot_no)
) comment '（连锁总部）出库单详情表';

#（单体/总部/门店）退货单信息表
DROP TABLE IF EXISTS saas_purchase_return_bill;
create table saas_purchase_return_bill
(
    id                            bigint        default 0                 not null comment '（云端）主键ID',
    ls_id                         bigint unsigned                         primary key comment '租户本地主键ID',
    bill_no                       varchar(32)   default ''                not null comment '单号',
    tenant_id                     bigint        default 0                 not null comment '租户ID',
    tenant_type                   tinyint(4)    default 0                 not null comment '租户类型（1-单体门店、2-连锁门店、3-连锁总部）',
    head_tenant_id                bigint        default 0                 not null comment '总部租户ID',
    purchase_bill_no                 varchar(32)   default ''                not null comment '采购订单号（总连锁部、单体原单退货才有）',
    requisition_bill_no           varchar(32)   default ''                not null comment '要货单号(连锁门店才有)',
    remote_receive_bill_no        varchar(32)   default ''                not null comment '远程收货单号',
    composite_bill_no             varchar(256)  default ''                null comment '综合单据号（单号混合）',
    bill_source                   tinyint(4)    default 0                 not null comment '单据来源（0-单体退货、1-总部退货、2-门店退货、3-门店调剂）',
    status                        tinyint(2)    default 0                 not null comment '状态（0-暂存、1-待审批、2-待出库、3-待复核、4-已出库、5-已驳回、6-已撤销）',
    supplier_pref                 varchar(32)   default ''                not null comment '供应商编码',
    supplier_name                 varchar(50)   default ''                not null comment '供应商名称',
    product_kind                  int           default 1                 not null comment '商品种类',
    return_content                varchar(2000) default ''                not null comment '退货内容',
    return_quantity               decimal(11,2) default 0.00              not null comment '退货数量',
    return_amount                 decimal(17,2) default 0.00              not null comment '退货总金额',
    cost_amount                   decimal(17,2) default 0.00              not null comment '成本总金额',
    operator                      varchar(64)   default ''                not null comment '出库操作员',
    operate_time                  datetime      default ''                not null comment '出库时间',
    checker                       varchar(64)   default ''                not null comment '复核员',
    check_time                    datetime      default null              null comment '复核时间',
    ec_order_no                   varchar(32)   default ''                not null comment '商城订单号',
    upload                        bit           default b'0'              not null comment '是否上报（财务等）',
    remark                        varchar(255)  default ''                null comment '备注',
    version                       int           default 1                 not null comment '版本(乐观锁)',
    creator                       varchar(64)   default ''                not null comment '创建者',
    create_time                   datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                       varchar(64)   default ''                null comment '更新者',
    update_time                   datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                       bit           default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_bill_no (tenant_id,bill_no)
) comment '（单体/总部/门店）退货单信息表';

#（单体/总部/门店）退货单详情表
DROP TABLE IF EXISTS saas_purchase_return_bill_detail;
create table saas_purchase_return_bill_detail
(
    id                            bigint        default 0                 not null comment '（云端）主键ID',
    ls_id                         bigint unsigned                         primary key comment '租户本地主键ID',
    bill_no                       varchar(32)   default ''                not null comment '单号',
    tenant_id                     bigint        default 0                 not null comment '租户ID',
    product_pref                  varchar(30)   default ''                not null comment '商品编码',
    lot_no                        varchar(32)   default null              null comment '批号',
    production_date               date          default null              null COMMENT '生产日期（YYYY-MM-DD）',
    expiry_date                   date          default null              null COMMENT '有效期至（YYYY-MM-DD）',
    location_pref                 varchar(20)   default 0.00              null comment '总部（合格）货位编号',
    in_tax_rate                   decimal(4,2)  default 0.00              null comment '税率（百分比）',
    price                         decimal(10,4) default 0.00              null comment '含税成本价（单价）',
    out_tax_rate                  decimal(4,2)  default 0.00              null comment '销项税率',
    outbound_quantity             decimal(8,2)  default 1.00              not null comment '出库数量',
    outbound_price                decimal(10,4) default 0.00              null comment '成本均价',
    outbound_amount               decimal(14,2) default 0.00              null comment '含税成本金额（总金额）',
    channel_id                    varchar(32)   default ''                null comment '渠道ID',
    medicare_project_code         varchar(100)                            null comment '医保项目编码',
    medicare_project_name         varchar(256)                            null comment '医保项目名称',
    medicare_project_level        tinyint      default 0                  not null comment '医保项目等级',
    medicare_min_package_num      tinyint      default 0                  not null comment '医保最小包装数量',
    ext                           text                                    null comment '扩展信息（当前记录医保信息、快照信息）',
    return_reason                 tinyint(2)    default 0                 null comment '退货原因(1-破损、2-召回、3-滞销、4-过期失效、5-近效期、6-质量问题、7-其他)',
    remark                        varchar(255)  default ''                null comment '备注',
    creator                       varchar(64)   default ''                not null comment '创建者',
    create_time                   datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                       varchar(64)   default ''                null comment '更新者',
    update_time                   datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                       bit           default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_bill_no_product_pref_lot_no (tenant_id,bill_no,product_pref,lot_no)
) comment '（单体/总部/门店）退货单详情表';

#（总部）缺货单信息表
DROP TABLE IF EXISTS saas_purchase_stockout_bill;
create table saas_purchase_stockout_bill
(
    id                            bigint        default 0                 not null comment '（云端）主键ID',
    ls_id                         bigint unsigned                         primary key comment '租户本地主键ID',
    bill_no                       varchar(32)   default ''                not null comment '单号',
    tenant_id                     bigint        default 0                 not null comment '租户ID',
    store_tenant_id               bigint        default 0                 not null comment '门店租户ID',
    requisition_bill_no           varchar(32)   default ''                not null comment '要货单号(连锁才有)',
    delivery_bill_no              varchar(32)   default ''                not null comment '配送单号',
    composite_bill_no             varchar(256)  default ''                null comment '综合单据号（单号混合）',
    status                        tinyint(2)    default 0                 not null comment '状态（0-缺货、1-已完成）',
    product_kind                  int           default 1                 not null comment '商品种类',
    stockout_content              varchar(2000) default ''                not null comment '缺货内容',
    stockout_quantity             decimal(11,2) default 1.00              not null comment '缺货数量',
    require_quantity              decimal(11,2) default 1.00              not null comment '要货数量',
    remark                        varchar(255)  default ''                null comment '备注',
    version                       int           default 1                 not null comment '版本(乐观锁)',
    creator                       varchar(64)   default ''                not null comment '创建者',
    create_time                   datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                       varchar(64)   default ''                null comment '更新者',
    update_time                   datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                       bit           default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_bill_no (tenant_id,bill_no)
) comment '（总部）缺货单信息表';

#（总部）缺货单详情表
DROP TABLE IF EXISTS saas_purchase_stockout_bill_detail;
create table saas_purchase_stockout_bill_detail
(
    id                            bigint        default 0                 not null comment '（云端）主键ID',
    ls_id                         bigint unsigned                         primary key comment '租户本地主键ID',
    bill_no                       varchar(32)   default ''                not null comment '单号',
    tenant_id                     bigint        default 0                 not null comment '租户ID',
    product_pref                  varchar(30)   default ''                not null comment '商品编码',
    require_quantity              decimal(8,2)  default 1.00              not null comment '要货数量',
    stockout_quantity             decimal(8,2)  default 1.00              not null comment '缺货数量',
    medicare_project_code         varchar(100)                            null comment '医保项目编码',
    medicare_project_name         varchar(256)                            null comment '医保项目名称',
    medicare_project_level        tinyint      default 0                  not null comment '医保项目等级',
    medicare_min_package_num      tinyint      default 0                  not null comment '医保最小包装数量',
    ext                           text                                    null comment '扩展信息（当前记录医保信息、快照信息）',
    remark                        varchar(255)  default ''                null comment '备注',
    creator                       varchar(64)   default ''                not null comment '创建者',
    create_time                   datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                       varchar(64)   default ''                null comment '更新者',
    update_time                   datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                       bit           default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_bill_no_product_pref (tenant_id,bill_no,product_pref)
) comment '（总部）缺货单详情表';

#采购-三方erp单据信息
DROP TABLE IF EXISTS saas_purchase_erp_bill;
create table saas_purchase_erp_bill
(
    id                            bigint        default 0                 not null comment '（云端）主键ID',
    ls_id                         bigint unsigned                         primary key comment '租户本地主键ID',
    bill_no                       varchar(32)   default ''                not null comment '采购单/收货单/配送单号',
    tenant_id                     bigint        default 0                 not null comment '租户ID',
    erp_bill_no                   varchar(32)   default ''                not null comment '三方erp订单号',
    sales_bill_no                 varchar(32)   default ''                not null comment '三方erp销售单号',
    outbound_bill_no              varchar(32)   default ''                not null comment '三方erp出库单号',
    warehouse_bill_no             varchar(32)   default ''                not null comment '三方erp入库单号',
    refund_storage_bill_no        varchar(32)   default ''                not null comment '三方erp销售退回入库单号(神农XSTHRK)',
    composite_bill_no             varchar(256)  default ''                null comment '综合单据号（单号混合）',
    cancel_reason                 varchar(256)  default ''                not null comment '三方erp取消原因',
    remark                        varchar(255)  default ''                null comment '备注',
    creator                       varchar(64)   default ''                not null comment '创建者',
    create_time                   datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                       varchar(64)   default ''                null comment '更新者',
    update_time                   datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                       bit           default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_bill_no (tenant_id,bill_no)
) comment '采购-三方erp单据信息';

#采购-运输信息表
DROP TABLE IF EXISTS saas_purchase_transport;
CREATE TABLE saas_purchase_transport (
    id                            bigint        default 0                 not null comment '（云端）主键ID',
    ls_id                         bigint unsigned                         primary key comment '租户本地主键ID',
    bill_no                       varchar(32)   default ''                not null comment '采购单/收货单/配送单号',
    tenant_id                     bigint        default 0                 not null comment '租户ID',
    authorized_representative     varchar(100)  default ''                null comment '委托经办人',
    carrier_name                  varchar(100)  default ''                null comment '承运人',
    carrier_entity                varchar(100)  default ''                null comment '承运单位名称',
    carrier_entity_uscc           varchar(100)  default ''                null comment '承运单位统一社会信用代码（国家标准长度）',
    transport_mode                varchar(20)   default ''                null comment '运输方式（如：road/air/ship）',
    departure_address             varchar(100)  default ''                null comment '启运地址',
    departure_time                datetime      default null              null comment '实际启运时间',
    departure_temperature         decimal(5,2)  default 0.00              null comment '启运温度（℃）',
    departure_humidity            decimal(5,2)  default 0.00              null comment '启运湿度（%）',
    tracking_no                   varchar(32)   default null              null comment '运单号',
    transport_vehicle             tinyint(2)    default 0                 null comment '运输工具类型（1-汽运、2-货运、3-冷藏车、4-冷藏箱、5-保温箱、6-其他）',
    vehicle_identifier            varchar(20)   default ''                null comment '运输工具标识（车牌号/航班号等）',
    driver                        varchar(20)   default ''                null comment '驾驶员姓名',
    driver_credential             varchar(255)  default ''                null comment '驾驶员证件',
    transport_temperature_monitor text                                    null comment '运输温度（℃）监控（json格式，每天的温度）',
    transport_humidity_monitor    text                                    null comment '运输湿度（%）监控（json格式，每天的湿度）',
    arrival_time                  datetime      default null              null comment '到货时间',
    arrival_temperature           decimal(5,2)  default 0.00              null comment '到货温度（℃）',
    arrival_humidity              decimal(5,2)  default 0.00              null comment '到货湿度（%）',
    shipment_time                 datetime      default null              null comment '随货同行单时间',
    shipment_file_url             varchar(300)  default ''                null comment '随货同行单文件地址',
    remark                        varchar(255)  default ''                null comment '备注',
    creator                       varchar(64)   default ''                not null comment '创建者',
    create_time                   datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                       varchar(64)   default ''                null comment '更新者',
    update_time                   datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                       bit           default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_bill_no (tenant_id,bill_no)
) COMMENT '采购-运输信息表';

#采购-发票信息表
DROP TABLE IF EXISTS saas_purchase_invoice;
create table saas_purchase_invoice
(
    id                        bigint        default 0                 not null comment '（云端）主键ID',
    ls_id                     bigint unsigned                         primary key comment '租户本地主键ID',
    bill_no                   varchar(32)   default ''                not null comment '采购单/收货单/配送单号',
    tenant_id                 bigint        default 0                 not null comment '租户ID',
    invoice_no                varchar(32)   default ''                null comment '发票号',
    invoice_code              varchar(32)   default ''                null comment '发票代码',
    invoice_type              varchar(32)   default ''                null comment '发票类型 1：电子普通发票 2：增值税发票 3：纸质普通发票，用于委托配送',
    accompanying_shipment     bit           default b'0'              null comment '发票是否随货通行 0：不随行 1：随行，用于委托配送',
    invoice_amount            decimal(14,2) default 0.00              null comment '发票金额',
    invoice_file_name         varchar(300)  default ''                null comment '发票文件名称',
    invoice_file_url          varchar(300)  default ''                null comment '发票文件地址',
    issued_time               datetime      default null              null comment '实际开(发)票时间',
    remark                    varchar(255)  default ''                null comment '备注',
    creator                   varchar(64)   default ''                not null comment '创建者',
    create_time               datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                   varchar(64)   default ''                null comment '更新者',
    update_time               datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                   bit           default b'0'              not null comment '是否删除',
    unique key uk_tenant_id_bill_no (tenant_id,bill_no)
) comment '采购-发票信息表';

#（单体/总部）采购记录表(租户-供应商-商品 维度)
-- DROP TABLE IF EXISTS saas_purchase_supplier_product_record;
-- create table saas_purchase_supplier_product_record
-- (
--     id                            bigint        default 0                 not null comment '（云端）主键ID',
--     ls_id                         bigint unsigned                         primary key comment '租户本地主键ID',
--     tenant_id                     bigint        default 0                 not null comment '租户ID',
--     tenant_type                   tinyint(4)    default 0                 not null comment '租户类型（1-单体门店、2-连锁门店、3-连锁总部）',
--     supplier_pref                 varchar(32)   default ''                not null comment '供应商编码',
--     supplier_name                 varchar(50)   default ''                not null comment '供应商名称',
--     product_pref                  varchar(30)   default ''                not null comment '商品编码',
--     common_name                   varchar(30)   default ''                not null comment '通用名',
--     purchase_quantity             decimal(12,2) default 1.00              not null comment '采购数量',
--     warehouse_quantity            decimal(12,2) default 1.00              not null comment '入库数量',
--     amount                        decimal(16,2) default 1.00              not null comment '采购总金额',
--     price                         decimal(10,4) default 1.00              not null comment '采购均价',
--     min_purchase_price            decimal(10,4) default 1.00              not null comment '最低采购价',
--     max_purchase_price            decimal(10,4) default 1.00              not null comment '最高采购价',
--     version                       int           default 1                 not null comment '版本(乐观锁)',
--     remark                        varchar(255)  default ''                null comment '备注',
--     creator                       varchar(64)   default ''                not null comment '创建者',
--     create_time                   datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
--     updater                       varchar(64)   default ''                null comment '更新者',
--     update_time                   datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
--     deleted                       bit           default b'0'              not null comment '是否删除',
--     unique key uk_tenant_supplier_product (tenant_id, supplier_pref, product_pref)
-- ) comment '（单体/总部）采购记录表';