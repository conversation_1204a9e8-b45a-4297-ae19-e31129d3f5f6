<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.localserver.purchase.server.dal.mysql.receive.ReceiveBillMapper">

    <sql id="ReceiveBillBaseColumns">
        pb.id,
        pb.bill_no,
        pb.source_bill_no,
        pb.delivery_bill_no,
        pb.shipment_no,
        pb.mall_order_no,
        pb.outbound_tenant_id,
        pb.tenant_id,
        pb.tenant_type,
        pb.head_tenant_id,
        pb.composite_bill_no,
        pb.bill_type,
        pb.purchase_mode,
        pb.status,
        pb.supplier_guid,
        pb.supplier_name,
        pb.supplier_sales,
        pb.product_kind,
        pb.receive_quantity,
        pb.discount,
        pb.discount_amount,
        pb.receive_content,
        pb.receive_amount,
        pb.deliverer,
        pb.delivery_time,
        pb.receiver,
        pb.receive_time,
        pb.accepter,
        pb.accept_time,
        pb.warehouser,
        pb.warehouse_time,
        pb.checker,
        pb.check_time,
        pb.quality_inspector,
        pb.quality_inspection_report,
        pb.remark,
        pb.version,
        pb.creator,
        pb.create_time,
        pb.updater,
        pb.update_time,
        pb.deleted
    </sql>

    <sql id="PurchaseTransportColumns">
        pt.id as transport_id,
        pt.bill_no,
        pt.tenant_id,
        pt.authorized_representative,
        pt.carrier_name,
        pt.carrier_entity,
        pt.carrier_entity_uscc,
        pt.transport_mode,
        pt.departure_address,
        pt.departure_time,
        pt.departure_temperature,
        pt.departure_humidity,
        pt.tracking_no,
        pt.transport_vehicle,
        pt.vehicle_identifier,
        pt.driver,
        pt.driver_credential,
        pt.transport_temperature_monitor,
        pt.transport_humidity_monitor,
        pt.arrival_time,
        pt.arrival_temperature,
        pt.arrival_humidity,
        pt.shipment_time,
        pt.shipment_file_url
    </sql>

    <sql id="PurchaseInvoiceColumns">
        pi.id as invoice_id,
        pi.bill_no,
        pi.tenant_id,
        pi.invoice_no,
        pi.invoice_code,
        pi.invoice_type,
        pi.accompanying_shipment,
        pi.invoice_amount,
        pi.invoice_file_name,
        pi.invoice_file_url,
        pi.issued_time
    </sql>

    <sql id="PurchaseErpBillColumns">
        peb.id as erp_bill_id,
        peb.bill_no,
        peb.tenant_id,
        peb.erp_bill_no,
        peb.sales_bill_no,
        peb.outbound_bill_no,
        peb.warehouse_bill_no,
        peb.refund_storage_bill_no,
        peb.composite_bill_no,
        peb.cancel_reason
    </sql>

    <sql id="ReceiveBillDetailColumns">
        pbd.id as detail_id,
        pbd.bill_no,
        pbd.tenant_id,
        pbd.product_pref,
        pbd.lot_no,
        pbd.production_date,
        pbd.expiry_date,
        pbd.price,
        pbd.tax_rate,
        pbd.discount,
        pbd.discounted_price,
        pbd.arrive_quantity,
        pbd.receive_quantity,
        pbd.reject_quantity,
        pbd.receive_amount,
        pbd.accept_conclusion,
        pbd.sample_quantity,
        pbd.unqualified_quantity,
        pbd.unqualified_amount,
        pbd.unqualified_reason,
        pbd.unqualified_position_guid,
        pbd.qualified_quantity,
        pbd.qualified_amount,
        pbd.qualified_position_guid,
        pbd.warehouse_quantity,
        pbd.warehouse_amount,
        pbd.treatment,
        pbd.channel_id,
        pbd.sterilization_batch_no,
        pbd.source_line_no,
        pbd.medicare_project_code,
        pbd.medicare_project_name,
        pbd.medicare_project_level,
        pbd.medicare_min_package_num,
        pbd.ext,
        pbd.remark as detail_remark,
        pbd.creator as detail_creator,
        pbd.create_time as detail_create_time,
        pbd.updater as detail_updater,
        pbd.update_time as detail_update_time,
        pbd.deleted as detail_deleted
    </sql>

    <!-- 收货单映射 -->
    <resultMap id="ReceiveBillResultMap" type="com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDTO">
        <id column="id" property="id"/>
        <result column="bill_no" property="billNo"/>
        <result column="source_bill_no" property="sourceBillNo"/>
        <result column="delivery_bill_no" property="deliveryBillNo"/>
        <result column="shipment_no" property="shipmentNo"/>
        <result column="mall_order_no" property="mallOrderNo"/>
        <result column="outbound_tenant_id" property="outboundTenantId"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="tenant_type" property="tenantType"/>
        <result column="head_tenant_id" property="headTenantId"/>
        <result column="composite_bill_no" property="compositeBillNo"/>
        <result column="bill_type" property="billType"/>
        <result column="purchase_mode" property="purchaseMode"/>
        <result column="status" property="status"/>
        <result column="supplier_guid" property="supplierGuid"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="supplier_sales" property="supplierSales"/>
        <result column="product_kind" property="productKind"/>
        <result column="receive_quantity" property="receiveQuantity"/>
        <result column="discount" property="discount"/>
        <result column="discount_amount" property="discountAmount"/>
        <result column="receive_content" property="receiveContent"/>
        <result column="receive_amount" property="receiveAmount"/>
        <result column="deliverer" property="deliverer"/>
        <result column="delivery_time" property="deliveryTime"/>
        <result column="receiver" property="receiver"/>
        <result column="receive_time" property="receiveTime"/>
        <result column="accepter" property="accepter"/>
        <result column="accept_time" property="acceptTime"/>
        <result column="warehouser" property="warehouser"/>
        <result column="warehouse_time" property="warehouseTime"/>
        <result column="checker" property="checker"/>
        <result column="check_time" property="checkTime"/>
        <result column="quality_inspector" property="qualityInspector"/>
        <result column="quality_inspection_report" property="qualityInspectionReport"/>
        <result column="remark" property="remark"/>
        <result column="version" property="version"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="updater" property="updater"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>

        <!-- 运输信息映射 -->
        <association property="transport" javaType="com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseTransportDTO">
            <id column="transport_id" property="id"/>
            <result column="bill_no" property="billNo"/>
            <result column="tenant_id" property="tenantId"/>
            <result column="authorized_representative" property="authorizedRepresentative"/>
            <result column="carrier_name" property="carrierName"/>
            <result column="carrier_entity" property="carrierEntity"/>
            <result column="carrier_entity_uscc" property="carrierEntityUscc"/>
            <result column="transport_mode" property="transportMode"/>
            <result column="departure_address" property="departureAddress"/>
            <result column="departure_time" property="departureTime"/>
            <result column="departure_temperature" property="departureTemperature"/>
            <result column="departure_humidity" property="departureHumidity"/>
            <result column="tracking_no" property="trackingNo"/>
            <result column="transport_vehicle" property="transportVehicle"/>
            <result column="vehicle_identifier" property="vehicleIdentifier"/>
            <result column="driver" property="driver"/>
            <result column="driver_credential" property="driverCredential"/>
            <result column="transport_temperature_monitor" property="transportTemperatureMonitor"/>
            <result column="transport_humidity_monitor" property="transportHumidityMonitor"/>
            <result column="arrival_time" property="arrivalTime"/>
            <result column="arrival_temperature" property="arrivalTemperature"/>
            <result column="arrival_humidity" property="arrivalHumidity"/>
            <result column="shipment_time" property="shipmentTime"/>
            <result column="shipment_file_url" property="shipmentFileUrl"/>
        </association>

        <!-- 发票信息映射 -->
        <association property="invoice" javaType="com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseInvoiceDTO">
            <id column="invoice_id" property="id"/>
            <result column="bill_no" property="billNo"/>
            <result column="tenant_id" property="tenantId"/>
            <result column="invoice_no" property="invoiceNo"/>
            <result column="invoice_code" property="invoiceCode"/>
            <result column="invoice_type" property="invoiceType"/>
            <result column="accompanying_shipment" property="accompanyingShipment"/>
            <result column="invoice_amount" property="invoiceAmount"/>
            <result column="invoice_file_name" property="invoiceFileName"/>
            <result column="invoice_file_url" property="invoiceFileUrl"/>
            <result column="issued_time" property="issuedTime"/>
        </association>

        <!-- ERP单据映射 -->
        <association property="erpBill" javaType="com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseErpBillDTO">
            <id column="erp_bill_id" property="id"/>
            <result column="bill_no" property="billNo"/>
            <result column="tenant_id" property="tenantId"/>
            <result column="erp_bill_no" property="erpBillNo"/>
            <result column="sales_bill_no" property="salesBillNo"/>
            <result column="outbound_bill_no" property="outboundBillNo"/>
            <result column="warehouse_bill_no" property="warehouseBillNo"/>
            <result column="refund_storage_bill_no" property="refundStorageBillNo"/>
            <result column="composite_bill_no" property="compositeBillNo"/>
            <result column="cancel_reason" property="cancelReason"/>
        </association>

        <!-- 收货详情映射 -->
        <collection property="details" ofType="com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDetailDTO">
            <id column="detail_id" property="id"/>
            <result column="bill_no" property="billNo"/>
            <result column="tenant_id" property="tenantId"/>
            <result column="product_pref" property="productPref"/>
            <result column="lot_no" property="lotNo"/>
            <result column="production_date" property="productionDate"/>
            <result column="expiry_date" property="expiryDate"/>
            <result column="price" property="price"/>
            <result column="tax_rate" property="taxRate"/>
            <result column="discount" property="discount"/>
            <result column="discounted_price" property="discountedPrice"/>
            <result column="arrive_quantity" property="arriveQuantity"/>
            <result column="receive_quantity" property="receiveQuantity"/>
            <result column="reject_quantity" property="rejectQuantity"/>
            <result column="receive_amount" property="receiveAmount"/>
            <result column="accept_conclusion" property="acceptConclusion"/>
            <result column="sample_quantity" property="sampleQuantity"/>
            <result column="unqualified_quantity" property="unqualifiedQuantity"/>
            <result column="unqualified_amount" property="unqualifiedAmount"/>
            <result column="unqualified_reason" property="unqualifiedReason"/>
            <result column="unqualified_position_guid" property="unqualifiedPositionGuid"/>
            <result column="qualified_quantity" property="qualifiedQuantity"/>
            <result column="qualified_amount" property="qualifiedAmount"/>
            <result column="qualified_position_guid" property="qualifiedPositionGuid"/>
            <result column="warehouse_quantity" property="warehouseQuantity"/>
            <result column="warehouse_amount" property="warehouseAmount"/>
            <result column="treatment" property="treatment"/>
            <result column="channel_id" property="channelId"/>
            <result column="sterilization_batch_no" property="sterilizationBatchNo"/>
            <result column="source_line_no" property="sourceLineNo"/>
            <result column="medicare_project_code" property="medicareProjectCode"/>
            <result column="medicare_project_name" property="medicareProjectName"/>
            <result column="medicare_project_level" property="medicareProjectLevel"/>
            <result column="medicare_min_package_num" property="medicareMinPackageNum"/>
            <result column="ext" property="ext" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
            <result column="detail_remark" property="remark"/>
        </collection>
    </resultMap>

    <select id="getReceiveBillWithDetails" resultMap="ReceiveBillResultMap">
        SELECT
        <include refid="ReceiveBillBaseColumns"/>,
        <include refid="PurchaseTransportColumns"/>,
        <include refid="PurchaseInvoiceColumns"/>,
        <include refid="PurchaseErpBillColumns"/>,
        <include refid="ReceiveBillDetailColumns"/>
        FROM saas_purchase_receive_bill pb
        LEFT JOIN saas_purchase_transport pt ON pb.bill_no = pt.bill_no AND pb.tenant_id = pt.tenant_id
        LEFT JOIN saas_purchase_invoice pi ON pb.bill_no = pi.bill_no AND pb.tenant_id = pi.tenant_id
        LEFT JOIN saas_purchase_erp_bill peb ON pb.bill_no = peb.bill_no AND pb.tenant_id = peb.tenant_id
        LEFT JOIN saas_purchase_receive_bill_detail pbd ON pb.bill_no = pbd.bill_no AND pb.tenant_id = pbd.tenant_id
        WHERE pb.bill_no = #{billNo}
        AND pb.tenant_id = #{tenantId}
    </select>

    <select id="getReceiveBillWithDetailsBySourceBill" resultMap="ReceiveBillResultMap">
        SELECT
        <include refid="ReceiveBillBaseColumns"/>,
        <include refid="ReceiveBillDetailColumns"/>
        FROM saas_purchase_receive_bill pb
        LEFT JOIN saas_purchase_receive_bill_detail pbd ON pb.bill_no = pbd.bill_no AND pb.tenant_id = pbd.tenant_id
        WHERE pb.source_bill_no = #{sourceBillNo}
        AND pb.tenant_id = #{tenantId}
    </select>

</mapper> 