<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.localserver.purchase.server.dal.mysql.purchase.PurchaseBillMapper">

    <sql id="PurchaseBillBaseColumns">
        pb.id,
        pb.plan_bill_no,
        pb.purchase_bill_no,
        pb.mall_order_no,
        pb.tenant_id,
        pb.tenant_type,
        pb.head_tenant_id,
        pb.inbound_tenant_id,
        pb.outbound_tenant_id,
        pb.composite_bill_no,
        pb.bill_type,
        pb.import_mode,
        pb.remote_received,
        pb.submitted,
        pb.status,
        pb.medicine_type,
        pb.purchase_mode,
        pb.supplier_guid,g

        pb.supplier_name,
        pb.purchase_content,
        pb.purchase_quantity,
        pb.purchase_amount,
        pb.delivered_quantity,
        pb.returnable_quantity,
        pb.product_kind,
        pb.planner,
        pb.plan_time,
        pb.purchaser,
        pb.purchase_time,
        pb.checker,
        pb.check_time,
        pb.receiver,
        pb.receiver_phone,
        pb.receiver_area,
        pb.receiver_address,
        pb.remark,
        pb.version,
        pb.creator,
        pb.create_time,
        pb.updater,
        pb.update_time,
        pb.deleted
    </sql>

    <sql id="PurchaseTransportColumns">
        pt.id as transport_id,
        pt.bill_no,
        pt.tenant_id,
        pt.authorized_representative,
        pt.carrier_name,
        pt.carrier_entity,
        pt.carrier_entity_uscc,
        pt.transport_mode,
        pt.departure_address,
        pt.departure_time,
        pt.departure_temperature,
        pt.departure_humidity,
        pt.tracking_no,
        pt.transport_vehicle,
        pt.vehicle_identifier,
        pt.driver,
        pt.driver_credential,
        pt.transport_temperature_monitor,
        pt.transport_humidity_monitor,
        pt.arrival_time,
        pt.arrival_temperature,
        pt.arrival_humidity,
        pt.shipment_time,
        pt.shipment_file_url
    </sql>

    <sql id="PurchaseInvoiceColumns">
        pi.id as invoice_id,
        pi.bill_no,
        pi.tenant_id,
        pi.invoice_no,
        pi.invoice_code,
        pi.invoice_type,
        pi.accompanying_shipment,
        pi.invoice_amount,
        pi.invoice_file_name,
        pi.invoice_file_url,
        pi.issued_time
    </sql>

    <sql id="PurchaseErpBillColumns">
        peb.id as erp_bill_id,
        peb.bill_no,
        peb.tenant_id,
        peb.erp_bill_no,
        peb.sales_bill_no,
        peb.outbound_bill_no,
        peb.warehouse_bill_no,
        peb.refund_storage_bill_no,
        peb.composite_bill_no,
        peb.cancel_reason
    </sql>

    <sql id="PurchaseBillDetailColumns">
        pbd.id as detail_id,
        pbd.plan_bill_no,
        pbd.purchase_bill_no,
        pbd.tenant_id,
        pbd.product_pref,
        pbd.lot_no,
        pbd.production_date,
        pbd.expiry_date,
        pbd.position_guid,
        pbd.in_tax_rate,
        pbd.out_tax_rate,
        pbd.price,
        pbd.purchase_amount,
        pbd.purchase_quantity,
        pbd.delivered_quantity,
        pbd.returnable_quantity,
        pbd.channel_id,
        pbd.source_line_no,
        pbd.medicare_project_code,
        pbd.medicare_project_name,
        pbd.medicare_project_level,
        pbd.medicare_min_package_num,
        pbd.ext,
        pbd.remark as detail_remark,
        pbd.creator as detail_creator,
        pbd.create_time as detail_create_time,
        pbd.updater as detail_updater,
        pbd.update_time as detail_update_time,
        pbd.deleted as detail_deleted
    </sql>

    <resultMap id="PurchaseBillWithDetailsResultMap" type="com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDTO">
        <id column="id" property="id"/>
        <result column="plan_bill_no" property="planBillNo"/>
        <result column="purchase_bill_no" property="purchaseBillNo"/>
        <result column="mall_order_no" property="mallOrderNo"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="tenant_type" property="tenantType"/>
        <result column="head_tenant_id" property="headTenantId"/>
        <result column="inbound_tenant_id" property="inboundTenantId"/>
        <result column="outbound_tenant_id" property="outboundTenantId"/>
        <result column="composite_bill_no" property="compositeBillNo"/>
        <result column="bill_type" property="billType"/>
        <result column="import_mode" property="importMode"/>
        <result column="remote_received" property="remoteReceived"/>
        <result column="submitted" property="submitted"/>
        <result column="status" property="status"/>
        <result column="medicine_type" property="medicineType"/>
        <result column="purchase_mode" property="purchaseMode"/>
        <result column="supplier_guid" property="supplierGuid"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="purchase_content" property="purchaseContent"/>
        <result column="purchase_quantity" property="purchaseQuantity"/>
        <result column="purchase_amount" property="purchaseAmount"/>
        <result column="delivered_quantity" property="deliveredQuantity"/>
        <result column="returnable_quantity" property="returnableQuantity"/>
        <result column="product_kind" property="productKind"/>
        <result column="planner" property="planner"/>
        <result column="plan_time" property="planTime"/>
        <result column="purchaser" property="purchaser"/>
        <result column="purchase_time" property="purchaseTime"/>
        <result column="checker" property="checker"/>
        <result column="check_time" property="checkTime"/>
        <result column="receiver" property="receiver"/>
        <result column="receiver_phone" property="receiverPhone"/>
        <result column="receiver_area" property="receiverArea"/>
        <result column="receiver_address" property="receiverAddress"/>
        <result column="remark" property="remark"/>
        <result column="version" property="version"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="updater" property="updater"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>

        <!-- 运输信息映射 -->
        <association property="transport" javaType="com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseTransportDTO">
            <id column="transport_id" property="id"/>
            <result column="bill_no" property="billNo"/>
            <result column="tenant_id" property="tenantId"/>
            <result column="authorized_representative" property="authorizedRepresentative"/>
            <result column="carrier_name" property="carrierName"/>
            <result column="carrier_entity" property="carrierEntity"/>
            <result column="carrier_entity_uscc" property="carrierEntityUscc"/>
            <result column="transport_mode" property="transportMode"/>
            <result column="departure_address" property="departureAddress"/>
            <result column="departure_time" property="departureTime"/>
            <result column="departure_temperature" property="departureTemperature"/>
            <result column="departure_humidity" property="departureHumidity"/>
            <result column="tracking_no" property="trackingNo"/>
            <result column="transport_vehicle" property="transportVehicle"/>
            <result column="vehicle_identifier" property="vehicleIdentifier"/>
            <result column="driver" property="driver"/>
            <result column="driver_credential" property="driverCredential"/>
            <result column="transport_temperature_monitor" property="transportTemperatureMonitor"/>
            <result column="transport_humidity_monitor" property="transportHumidityMonitor"/>
            <result column="arrival_time" property="arrivalTime"/>
            <result column="arrival_temperature" property="arrivalTemperature"/>
            <result column="arrival_humidity" property="arrivalHumidity"/>
            <result column="shipment_time" property="shipmentTime"/>
            <result column="shipment_file_url" property="shipmentFileUrl"/>
        </association>

        <!-- ERP单据映射 -->
        <association property="erpBill" javaType="com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseErpBillDTO">
            <id column="erp_bill_id" property="id"/>
            <result column="bill_no" property="billNo"/>
            <result column="tenant_id" property="tenantId"/>
            <result column="erp_bill_no" property="erpBillNo"/>
            <result column="sales_bill_no" property="salesBillNo"/>
            <result column="outbound_bill_no" property="outboundBillNo"/>
            <result column="warehouse_bill_no" property="warehouseBillNo"/>
            <result column="refund_storage_bill_no" property="refundStorageBillNo"/>
            <result column="composite_bill_no" property="compositeBillNo"/>
            <result column="cancel_reason" property="cancelReason"/>
        </association>


        <collection property="details" ofType="com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDetailDTO">
            <id column="detail_id" property="id"/>
            <result column="plan_bill_no" property="planBillNo"/>
            <result column="purchase_bill_no" property="purchaseBillNo"/>
            <result column="product_pref" property="productPref"/>
            <result column="lot_no" property="lotNo"/>
            <result column="production_date" property="productionDate"/>
            <result column="expiry_date" property="expiryDate"/>
            <result column="position_guid" property="positionGuid"/>
            <result column="in_tax_rate" property="inTaxRate"/>
            <result column="out_tax_rate" property="outTaxRate"/>
            <result column="price" property="price"/>
            <result column="purchase_amount" property="purchaseAmount"/>
            <result column="purchase_quantity" property="purchaseQuantity"/>
            <result column="delivered_quantity" property="deliveredQuantity"/>
            <result column="returnable_quantity" property="returnableQuantity"/>
            <result column="channel_id" property="channelId"/>
            <result column="source_line_no" property="sourceLineNo"/>
            <result column="medicare_project_code" property="medicareProjectCode"/>
            <result column="medicare_project_name" property="medicareProjectName"/>
            <result column="medicare_project_level" property="medicareProjectLevel"/>
            <result column="medicare_min_package_num" property="medicareMinPackageNum"/>
            <result column="ext" property="ext" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
            <result column="detail_remark" property="remark"/>
        </collection>
    </resultMap>

    <select id="getPurchaseBillWithDetails" resultMap="PurchaseBillWithDetailsResultMap">
        SELECT 
            <include refid="PurchaseBillBaseColumns"/>,
            <include refid="PurchaseBillDetailColumns"/>
        FROM saas_purchase_bill pb
        LEFT JOIN saas_purchase_bill_detail pbd ON pb.purchase_bill_no = pbd.purchase_bill_no AND pb.tenant_id = pbd.tenant_id
        WHERE pb.purchase_bill_no = #{purchaseBillNo}
        AND pb.tenant_id = #{tenantId}
    </select>

    <select id="getPurchaseBillWithDetailsByPlanBillNo" resultMap="PurchaseBillWithDetailsResultMap">
        SELECT 
            <include refid="PurchaseBillBaseColumns"/>,
            <include refid="PurchaseBillDetailColumns"/>
        FROM saas_purchase_bill pb
        LEFT JOIN saas_purchase_bill_detail pbd ON pb.plan_bill_no = pbd.plan_bill_no AND pb.tenant_id = pbd.tenant_id
        WHERE pb.plan_bill_no = #{planBillNo}
        AND pb.tenant_id = #{tenantId}
    </select>

    <select id="getPurchaseBillWithDetailsByMallOrderNo" resultMap="PurchaseBillWithDetailsResultMap">
        SELECT 
            <include refid="PurchaseBillBaseColumns"/>,
            <include refid="PurchaseBillDetailColumns"/>
        FROM saas_purchase_bill pb
        LEFT JOIN saas_purchase_bill_detail pbd ON pb.mall_order_no = pbd.mall_order_no AND pb.tenant_id = pbd.tenant_id
        WHERE pb.mall_order_no = #{mallOrderNo}
        AND pb.tenant_id = #{tenantId}
    </select>

    <update id="updatePurchaseBillByVersion">
        UPDATE saas_purchase_bill
        SET
            <if test="status != null">
                status = #{status},
            </if>
            <if test="delivered_quantity != null">
                delivered_quantity = #{deliveredQuantity},
            </if>
            <if test="returnable_quantity != null">
                returnable_quantity = #{returnableQuantity},
            </if>
        update_time = NOW(),
        version = version + 1
        WHERE purchase_bill_no = #{purchaseBillNo}
        AND tenant_id = #{tenantId}
        AND version = #{version}
    </update>

</mapper> 