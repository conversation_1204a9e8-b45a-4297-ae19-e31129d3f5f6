<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.localserver.purchase.server.dal.mysql.returned.ReturnBillMapper">

    <sql id="ReturnBillBaseColumns">
        rb.id,
        rb.bill_no,
        rb.purchase_bill_no,
        rb.mall_order_no,
        rb.inbound_tenant_id,
        rb.tenant_id,
        rb.tenant_type,
        rb.head_tenant_id,
        rb.composite_bill_no,
        rb.bill_type,
        rb.status,
        rb.submitted,
        rb.supplier_guid,
        rb.supplier_name,
        rb.product_kind,
        rb.return_content,
        rb.return_quantity,
        rb.return_amount,
        rb.cost_amount,
        rb.operator,
        rb.operate_time,
        rb.checker,
        rb.check_time,
        rb.remark,
        rb.version,
        rb.creator,
        rb.create_time,
        rb.updater,
        rb.update_time,
        rb.deleted
    </sql>

    <sql id="ReturnBillDetailColumns">
        rbd.id as detail_id,
        rbd.bill_no,
        rbd.tenant_id,
        rbd.product_pref,
        rbd.lot_no,
        rbd.production_date,
        rbd.expiry_date,
        rbd.position_guid,
        rbd.in_tax_rate,
        rbd.price,
        rbd.out_tax_rate,
        rbd.outbound_quantity,
        rbd.outbound_price,
        rbd.outbound_amount,
        rbd.channel_id,
        rbd.medicare_project_code,
        rbd.medicare_project_name,
        rbd.medicare_project_level,
        rbd.medicare_min_package_num,
        rbd.ext,
        rbd.return_reason,
        rbd.remark as detail_remark,
        rbd.creator as detail_creator,
        rbd.create_time as detail_create_time,
        rbd.updater as detail_updater,
        rbd.update_time as detail_update_time,
        rbd.deleted as detail_deleted
    </sql>

    <!-- 退货单映射 -->
    <resultMap id="ReturnBillResultMap" type="com.xyy.saas.localserver.purchase.api.returned.dto.ReturnBillDTO">
        <id column="id" property="id"/>
        <result column="bill_no" property="billNo"/>
        <result column="purchase_bill_no" property="purchaseBillNo"/>
        <result column="mall_order_no" property="mallOrderNo"/>
        <result column="inbound_tenant_id" property="inboundTenantId"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="tenant_type" property="tenantType"/>
        <result column="head_tenant_id" property="headTenantId"/>
        <result column="composite_bill_no" property="compositeBillNo"/>
        <result column="bill_type" property="billType"/>
        <result column="status" property="status"/>
        <result column="submitted" property="submitted"/>
        <result column="supplier_guid" property="supplierGuid"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="product_kind" property="productKind"/>
        <result column="return_content" property="returnContent"/>
        <result column="return_quantity" property="returnQuantity"/>
        <result column="return_amount" property="returnAmount"/>
        <result column="cost_amount" property="costAmount"/>
        <result column="operator" property="operator"/>
        <result column="operate_time" property="operateTime"/>
        <result column="checker" property="checker"/>
        <result column="check_time" property="checkTime"/>
        <result column="remark" property="remark"/>
        <result column="version" property="version"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="updater" property="updater"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>

        <!-- 退货详情映射 -->
        <collection property="details" ofType="com.xyy.saas.localserver.purchase.api.returned.dto.ReturnBillDetailDTO">
            <id column="detail_id" property="id"/>
            <result column="bill_no" property="billNo"/>
            <result column="tenant_id" property="tenantId"/>
            <result column="product_pref" property="productPref"/>
            <result column="lot_no" property="lotNo"/>
            <result column="production_date" property="productionDate"/>
            <result column="expiry_date" property="expiryDate"/>
            <result column="position_guid" property="positionGuid"/>
            <result column="in_tax_rate" property="inTaxRate"/>
            <result column="price" property="price"/>
            <result column="out_tax_rate" property="outTaxRate"/>
            <result column="outbound_quantity" property="outboundQuantity"/>
            <result column="outbound_price" property="outboundPrice"/>
            <result column="outbound_amount" property="outboundAmount"/>
            <result column="channel_id" property="channelId"/>
            <result column="medicare_project_code" property="medicareProjectCode"/>
            <result column="medicare_project_name" property="medicareProjectName"/>
            <result column="medicare_project_level" property="medicareProjectLevel"/>
            <result column="medicare_min_package_num" property="medicareMinPackageNum"/>
            <result column="ext" property="ext" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
            <result column="return_reason" property="returnReason"/>
            <result column="detail_remark" property="remark"/>
        </collection>
    </resultMap>

    <select id="getReturnBillWithDetails" resultMap="ReturnBillResultMap">
        SELECT
        <include refid="ReturnBillBaseColumns"/>,
        <include refid="ReturnBillDetailColumns"/>
        FROM saas_purchase_return_bill rb
        LEFT JOIN saas_purchase_return_bill_detail rbd ON rb.bill_no = rbd.bill_no AND rb.tenant_id = rbd.tenant_id
        WHERE rb.bill_no = #{billNo}
        AND rb.tenant_id = #{tenantId}
    </select>

    <!-- 根据版本号更新退货单状态 -->
    <update id="updateReturnBillByVersion">
        UPDATE saas_purchase_return_bill
        SET status = #{status},
            version = version + 1,
            update_time = NOW()
        WHERE id = #{id}
          AND version = #{version}
          AND deleted = 0
    </update>

</mapper> 