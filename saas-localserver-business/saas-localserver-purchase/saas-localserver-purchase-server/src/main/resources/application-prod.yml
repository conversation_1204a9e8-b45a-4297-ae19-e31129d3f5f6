#spring:
#  cloud:
#    nacos:
#      server-addr: mse-68d9cfe2-p.nacos-ans.mse.aliyuncs.com:8848
#      discovery:
#        namespace: ${spring.profiles.active}
#        group: http
#      config:
#        namespace: ${spring.profiles.active}
#        group: ${spring.application.name}
#        server-addr: ${spring.cloud.nacos.server-addr}
#        prefix: ${spring.application.name}
#        file-extension: yaml
#  config:
#    import:
#      - optional:nacos:${spring.application.name}-${spring.profiles.active}.yaml

#  datasource:
#    dynamic:
#      datasource:
#        master:
#          url: ********************************************************************************************************************************************************************************************
#          username: app_remote_prescription_w
#          password: UY27Hdy9uTYe
#        slave:
#          lazy: true
#          url: ********************************************************************************************************************************************************************************************
#          username: app_remote_prescription_w
#          password: UY27Hdy9uTYe

#  data:
#    redis:
#      host: r-bp1fa3xmxqz7f5v0pd.redis.rds.aliyuncs.com
#      port: 6379
#      password: JEf8CVrnY0G3RPEZ
#      database: 14

#rocketmq:
#  name-server: ************:9876
#  producer:
#    group: saas_transmitter_system
saas-cloud:
    url: