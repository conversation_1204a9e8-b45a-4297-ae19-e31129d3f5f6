package com.xyy.saas.localserver.purchase.server.service.stockout;

import com.xyy.saas.localserver.purchase.api.stockout.dto.StockoutBillDetailDTO;
import com.xyy.saas.localserver.purchase.api.stockout.dto.StockoutBillDetailPageReqDTO;
import jakarta.validation.*;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * （总部）缺货单明细 Service 接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface StockoutBillDetailService {

    /**
     * 创建（总部）缺货单明细
     *
     * @param createDTO 创建信息
     * @return 编号
     */
    Long createStockoutBillDetail(@Valid StockoutBillDetailDTO createDTO);

    /**
     * 更新（总部）缺货单明细
     *
     * @param updateDTO 更新信息
     */
    void updateStockoutBillDetail(@Valid StockoutBillDetailDTO updateDTO);

    /**
     * 删除（总部）缺货单明细
     *
     * @param id 编号
     */
    void deleteStockoutBillDetail(Long id);

    /**
     * 获得（总部）缺货单明细
     *
     * @param id 编号
     * @return （总部）缺货单明细
     */
    StockoutBillDetailDTO getStockoutBillDetail(Long id);

    /**
     * 获得（总部）缺货单明细分页
     *
     * @param pageReqDTO 分页查询
     * @return （总部）缺货单明细分页
     */
    PageResult<StockoutBillDetailDTO> getStockoutBillDetailPage(StockoutBillDetailPageReqDTO pageReqDTO);

}