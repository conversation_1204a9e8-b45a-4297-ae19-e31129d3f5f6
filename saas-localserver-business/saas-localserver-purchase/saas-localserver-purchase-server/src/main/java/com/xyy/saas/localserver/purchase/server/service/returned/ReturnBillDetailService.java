//package com.xyy.saas.localserver.purchase.server.service.returned;
//
//import com.xyy.saas.localserver.purchase.server.admin.returned.vo.ReturnBillDetailPageReqVO;
//import com.xyy.saas.localserver.purchase.server.admin.returned.vo.ReturnBillDetailSaveReqVO;
//import com.xyy.saas.localserver.purchase.server.dal.dataobject.returned.ReturnBillDetailDO;
//import jakarta.validation.*;
//import cn.iocoder.yudao.framework.common.pojo.PageResult;
//
///**
// * （单体/总部/门店）退货单明细 Service 接口
// *
// * <AUTHOR>
// */
//public interface ReturnBillDetailService {
//
//    /**
//     * 创建（单体/总部/门店）退货单明细
//     *
//     * @param createReqVO 创建信息
//     * @return 编号
//     */
//    Long createPurchaseReturnBillDetail(@Valid ReturnBillDetailSaveReqVO createReqVO);
//
//    /**
//     * 更新（单体/总部/门店）退货单明细
//     *
//     * @param updateReqVO 更新信息
//     */
//    void updatePurchaseReturnBillDetail(@Valid ReturnBillDetailSaveReqVO updateReqVO);
//
//    /**
//     * 删除（单体/总部/门店）退货单明细
//     *
//     * @param id 编号
//     */
//    void deletePurchaseReturnBillDetail(Long id);
//
//    /**
//     * 获得（单体/总部/门店）退货单明细
//     *
//     * @param id 编号
//     * @return （单体/总部/门店）退货单明细
//     */
//    ReturnBillDetailDO getPurchaseReturnBillDetail(Long id);
//
//    /**
//     * 获得（单体/总部/门店）退货单明细分页
//     *
//     * @param pageReqVO 分页查询
//     * @return （单体/总部/门店）退货单明细分页
//     */
//    PageResult<ReturnBillDetailDO> getPurchaseReturnBillDetailPage(ReturnBillDetailPageReqVO pageReqVO);
//
//}