package com.xyy.saas.localserver.purchase.server.service.supplier;

import com.xyy.saas.localserver.purchase.api.supplier.dto.TenantSupplierRelationDTO;
import com.xyy.saas.localserver.purchase.api.supplier.dto.TenantSupplierRelationPageReqDTO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 租户-供应商-关联关系 Service 接口
 *
 * <p>
 * 该接口定义了租户与供应商关联关系相关的核心业务操作，包括：
 * </p>
 * <ul>
 * <li>关联关系的增删改查等基础操作</li>
 * <li>关联关系的分页查询</li>
 * <li>供应商开通和首营审核</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface TenantSupplierRelationService {

    // ========== 基础操作方法 ==========

    /**
     * 创建租户-供应商-关联关系
     *
     * @param createDTO 创建信息
     * @return 编号
     */
    Long createTenantSupplierRelation(TenantSupplierRelationDTO createDTO);

    /**
     * 更新租户-供应商-关联关系
     *
     * @param updateDTO 更新信息
     */
    void updateTenantSupplierRelation(TenantSupplierRelationDTO updateDTO);

    /**
     * 删除租户-供应商-关联关系
     *
     * @param id 编号
     */
    void deleteTenantSupplierRelation(Long id);

    /**
     * 获得租户-供应商-关联关系
     *
     * @param id 编号
     * @return 租户-供应商-关联关系
     */
    TenantSupplierRelationDTO getTenantSupplierRelation(Long id);

    // ========== 查询方法 ==========

    /**
     * 获得租户-供应商-关联关系分页
     *
     * @param pageReqDTO 分页查询
     * @return 租户-供应商-关联关系分页
     */
    PageResult<TenantSupplierRelationDTO> getTenantSupplierRelationPage(TenantSupplierRelationPageReqDTO pageReqDTO);

}