package com.xyy.saas.localserver.purchase.server.protocol.purchase;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.localserver.purchase.server.admin.purchase.vo.PurchaseBillRespVO;
import com.xyy.saas.localserver.purchase.server.admin.purchase.vo.PurchaseBillSaveReqVO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

/**
 * @Desc 采购接口
 * @Date 2025/04/29
 * <AUTHOR>
 */

@HttpExchange(accept = "application/json", contentType = "application/json",url = "${saas-cloud.url}")
public interface PurchaseClient {

    /**
     * 收货后更新总部配送单
     *
     * @param headDistribution 总部配送单信息
     * @return 更新结果
     */
    @PostExchange("saas-cloud/head-distribution/confirm-receive")
    CommonResult<Boolean> confirmDistributionReceive(@RequestBody PurchaseBillSaveReqVO headDistribution);

    /**
     * 查询调剂但
     *
     * @param id 调剂单id
     * @return 更新结果
     */
    @PostExchange("saas-cloud/store-allocation/get")
    CommonResult<PurchaseBillRespVO> get(@RequestParam("id") Long id);

}
