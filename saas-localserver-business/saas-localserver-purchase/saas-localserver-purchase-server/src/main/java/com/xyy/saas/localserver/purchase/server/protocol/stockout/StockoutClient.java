package com.xyy.saas.localserver.purchase.server.protocol.stockout;

import org.springframework.web.service.annotation.HttpExchange;

/**
 * @Desc 缺货接口
 * @Date 2025/04/29
 * <AUTHOR>
 */

@HttpExchange(accept = "application/json", contentType = "application/json",url = "${saas-cloud.url}")
public interface StockoutClient {

//	/**
//	 * 创建总部缺货单
//	 *
//	 * @param createReqVO 创建请求
//	 * @return 编号
//	 */
//	@PostExchange("saas-cloud/stockout-bill/create")
//	CommonResult<Long> createStockoutBill(@RequestBody StockoutBillSaveReqVO createReqVO);


}
