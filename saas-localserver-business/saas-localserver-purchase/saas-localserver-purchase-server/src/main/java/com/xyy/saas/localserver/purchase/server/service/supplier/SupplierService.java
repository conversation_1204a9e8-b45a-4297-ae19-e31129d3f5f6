package com.xyy.saas.localserver.purchase.server.service.supplier;

import com.xyy.saas.localserver.purchase.api.supplier.dto.SupplierDTO;
import com.xyy.saas.localserver.purchase.api.supplier.dto.SupplierPageReqDTO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 供应商信息 Service 接口
 *
 * <p>
 * 该接口定义了供应商信息相关的核心业务操作，包括：
 * </p>
 * <ul>
 * <li>供应商信息的增删改查等基础操作</li>
 * <li>供应商信息的分页查询</li>
 * <li>供应商资质的校验</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SupplierService {

    // ========== 基础操作方法 ==========

    /**
     * 创建供应商信息
     *
     * @param createDTO 创建信息
     * @return 供应商编码
     */
    String createSupplier(SupplierDTO createDTO);

    /**
     * 更新供应商信息
     *
     * @param updateDTO 更新信息
     */
    void updateSupplier(SupplierDTO updateDTO);

    /**
     * 删除供应商信息
     *
     * @param id 编号
     */
    void deleteSupplier(Long id);

    /**
     * 获得供应商信息
     *
     * @param id 编号
     * @return 供应商信息
     */
    SupplierDTO getSupplier(Long id);

    // ========== 查询方法 ==========

    /**
     * 获得供应商信息分页
     *
     * @param pageReqDTO 分页查询
     * @return 供应商信息分页
     */
    PageResult<SupplierDTO> getSupplierPage(SupplierPageReqDTO pageReqDTO);

}