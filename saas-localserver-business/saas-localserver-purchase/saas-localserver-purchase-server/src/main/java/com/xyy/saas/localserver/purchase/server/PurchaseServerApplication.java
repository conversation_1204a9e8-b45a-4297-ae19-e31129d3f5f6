package com.xyy.saas.localserver.purchase.server;

import cn.iocoder.yudao.framework.idempotent.config.YudaoIdempotentConfiguration;
import cn.iocoder.yudao.framework.mq.redis.config.YudaoRedisMQProducerAutoConfiguration;
import cn.iocoder.yudao.framework.ratelimiter.config.YudaoRateLimiterConfiguration;
import cn.iocoder.yudao.framework.redis.config.YudaoRedisAutoConfiguration;
import cn.iocoder.yudao.framework.signature.config.YudaoApiSignatureAutoConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;

@Slf4j
@SpringBootApplication(scanBasePackages = { "com.xyy.saas.*" }, exclude = {
                RedisAutoConfiguration.class,
                YudaoRedisAutoConfiguration.class,
                YudaoIdempotentConfiguration.class,
                YudaoRateLimiterConfiguration.class,
                YudaoRedisMQProducerAutoConfiguration.class,
                YudaoApiSignatureAutoConfiguration.class,
})

public class PurchaseServerApplication {

        public static void main(String[] args) {
                SpringApplication.run(PurchaseServerApplication.class, args);
        }
}
