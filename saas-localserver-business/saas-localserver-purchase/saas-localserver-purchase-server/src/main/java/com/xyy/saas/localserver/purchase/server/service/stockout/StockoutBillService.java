package com.xyy.saas.localserver.purchase.server.service.stockout;

import com.xyy.saas.localserver.purchase.api.stockout.dto.StockoutBillDTO;
import com.xyy.saas.localserver.purchase.api.stockout.dto.StockoutBillPageReqDTO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 采购-缺货单信息 Service 接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface StockoutBillService {

    /**
     * 创建采购-缺货单信息
     *
     * @param createDTO 创建信息
     * @return 创建结果
     */
    Long createStockoutBill(StockoutBillDTO createDTO);

    /**
     * 更新采购-缺货单信息
     *
     * @param updateDTO 更新信息
     */
    void updateStockoutBill(StockoutBillDTO updateDTO);

    /**
     * 删除采购-缺货单信息
     *
     * @param id 编号
     */
    void deleteStockoutBill(Long id);

    /**
     * 获得采购-缺货单信息
     *
     * @param id 编号
     * @return 采购-缺货单信息
     */
    StockoutBillDTO getStockoutBill(Long id);

    /**
     * 获得采购-缺货单信息分页
     *
     * @param pageReqDTO 分页查询参数
     * @return 采购-缺货单信息分页
     */
    PageResult<StockoutBillDTO> getStockoutBillPage(StockoutBillPageReqDTO pageReqDTO);
}