package com.xyy.saas.localserver.purchase.server.service.supplier.impl;

import com.xyy.saas.localserver.purchase.api.supplier.dto.SupplierDTO;
import com.xyy.saas.localserver.purchase.api.supplier.dto.SupplierPageReqDTO;
import com.xyy.saas.localserver.purchase.server.convert.supplier.SupplierConvert;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.supplier.SupplierDO;
import com.xyy.saas.localserver.purchase.server.dal.mysql.supplier.SupplierMapper;
import com.xyy.saas.localserver.purchase.server.enums.BillNoTypeEnum;
import com.xyy.saas.localserver.purchase.server.service.supplier.SupplierService;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import java.time.LocalDateTime;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.purchase.enums.ErrorCodeConstants.*;

/**
 * 供应商信息 Service 实现类
 *
 * <p>
 * 该实现类提供了供应商信息相关的核心业务操作实现，包括：
 * </p>
 * <ul>
 * <li>供应商信息的增删改查等基础操作</li>
 * <li>供应商信息的分页查询</li>
 * <li>供应商资质的校验</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Validated
public class SupplierServiceImpl implements SupplierService {

    @Resource
    private SupplierMapper supplierMapper;

    // ========== 基础操作方法 ==========

    @Override
    public String createSupplier(SupplierDTO createDTO) {
        // 1. 校验供应商（营业执照）信息存在
        validateBusinessLicenseExists(createDTO.getBusinessLicense());

        // 2. 设置供应商编码
        createDTO.setGuid(BillNoTypeEnum.SUPPLIER_GUID.getBillNo(LocalDateTime.now()));

        // 3. 保存供应商信息
        supplierMapper.insert(SupplierConvert.INSTANCE.convert2DO(createDTO));

        // 4. 返回
        return createDTO.getGuid();
    }

    @Override
    public void updateSupplier(SupplierDTO updateDTO) {
        // 1. 校验存在
        validateSupplierExists(updateDTO.getId());
        // 2. 更新
        supplierMapper.updateById(SupplierConvert.INSTANCE.convert2DO(updateDTO));
    }

    @Override
    public void deleteSupplier(Long id) {
        // 1. 校验存在
        validateSupplierExists(id);
        // 2. 删除
        supplierMapper.deleteById(id);
    }

    @Override
    public SupplierDTO getSupplier(Long id) {
        // 1. 查询供应商
        SupplierDO supplier = supplierMapper.selectById(id);
        // 2. 转换返回
        return SupplierConvert.INSTANCE.convert2DTO(supplier);
    }

    // ========== 查询方法 ==========

    @Override
    public PageResult<SupplierDTO> getSupplierPage(SupplierPageReqDTO pageReqDTO) {
        // 1. 查询分页
        PageResult<SupplierDO> pageResult = supplierMapper.selectPage(pageReqDTO);
        // 2. 转换返回
        return SupplierConvert.INSTANCE.convert2DTO(pageResult);
    }

    // ========== 私有方法 ==========

    /**
     * 校验供应商是否存在
     *
     * @param id 供应商编号
     */
    private void validateSupplierExists(Long id) {
        if (supplierMapper.selectById(id) == null) {
            throw exception(SUPPLIER_NOT_EXISTS);
        }
    }

    /**
     * 校验供应商（营业执照）信息存在
     *
     * @param businessLicense 营业执照
     */
    private void validateBusinessLicenseExists(String businessLicense) {
        if (supplierMapper.selectByBusinessLicense(businessLicense) == null) {
            throw exception(SUPPLIER_BUSINESS_LICENSE_EXISTS);
        }
    }

}