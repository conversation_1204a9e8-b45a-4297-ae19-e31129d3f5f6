package com.xyy.saas.localserver.purchase.server.service.supplier.impl;

import com.xyy.saas.localserver.purchase.api.supplier.dto.TenantSupplierSalesDTO;
import com.xyy.saas.localserver.purchase.api.supplier.dto.TenantSupplierSalesPageReqDTO;
import com.xyy.saas.localserver.purchase.server.convert.supplier.TenantSupplierSalesConvert;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.supplier.TenantSupplierSalesDO;
import com.xyy.saas.localserver.purchase.server.dal.mysql.supplier.TenantSupplierSalesMapper;
import com.xyy.saas.localserver.purchase.server.service.supplier.TenantSupplierSalesService;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.purchase.enums.ErrorCodeConstants.*;

/**
 * 租户-供应商-销售人员信息 Service 实现类
 *
 * <p>
 * 该实现类提供了租户与供应商销售人员信息相关的核心业务操作实现，包括：
 * </p>
 * <ul>
 * <li>销售人员信息的增删改查等基础操作</li>
 * <li>销售人员信息的分页查询</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Validated
public class TenantSupplierSalesServiceImpl implements TenantSupplierSalesService {

    @Resource
    private TenantSupplierSalesMapper tenantSupplierSalesMapper;

    // ========== 基础操作方法 ==========

    @Override
    public Long createTenantSupplierSales(TenantSupplierSalesDTO createReqDTO) {
        // 1. 插入销售人员信息
        TenantSupplierSalesDO tenantSupplierSales = TenantSupplierSalesConvert.INSTANCE.convert2DO(createReqDTO);
        tenantSupplierSalesMapper.insert(tenantSupplierSales);
        // 2. 返回
        return tenantSupplierSales.getId();
    }

    @Override
    public void updateTenantSupplierSales(TenantSupplierSalesDTO updateDTO) {
        // 1. 校验存在
        validateTenantSupplierSalesExists(updateDTO.getId());
        // 2. 更新
        tenantSupplierSalesMapper.updateById(TenantSupplierSalesConvert.INSTANCE.convert2DO(updateDTO));
    }

    @Override
    public void deleteTenantSupplierSales(Long id) {
        // 1. 校验存在
        validateTenantSupplierSalesExists(id);
        // 2. 删除
        tenantSupplierSalesMapper.deleteById(id);
    }

    @Override
    public TenantSupplierSalesDTO getTenantSupplierSales(Long id) {
        // 1. 查询销售人员信息
        TenantSupplierSalesDO tenantSupplierSales = tenantSupplierSalesMapper.selectById(id);
        // 2. 转换返回
        return TenantSupplierSalesConvert.INSTANCE.convert2DTO(tenantSupplierSales);
    }

    // ========== 查询方法 ==========

    @Override
    public PageResult<TenantSupplierSalesDTO> getTenantSupplierSalesPage(TenantSupplierSalesPageReqDTO pageReqDTO) {
        // 1. 查询分页
        PageResult<TenantSupplierSalesDO> pageResult = tenantSupplierSalesMapper.selectPage(pageReqDTO);
        // 2. 转换返回
        return TenantSupplierSalesConvert.INSTANCE.convert2DTO(pageResult);
    }

    // ========== 私有方法 ==========

    /**
     * 校验销售人员信息是否存在
     *
     * @param id 销售人员信息编号
     */
    private void validateTenantSupplierSalesExists(Long id) {
        if (tenantSupplierSalesMapper.selectById(id) == null) {
            throw exception(TENANT_SUPPLIER_SALES_NOT_EXISTS);
        }
    }

}