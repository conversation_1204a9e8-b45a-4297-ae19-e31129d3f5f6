package com.xyy.saas.localserver.purchase.server.service.supplier;

import com.xyy.saas.localserver.purchase.api.supplier.dto.TenantSupplierSalesDTO;
import com.xyy.saas.localserver.purchase.api.supplier.dto.TenantSupplierSalesPageReqDTO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 租户-供应商-销售人员信息 Service 接口
 *
 * <p>
 * 该接口定义了租户与供应商销售人员信息相关的核心业务操作，包括：
 * </p>
 * <ul>
 * <li>销售人员信息的增删改查等基础操作</li>
 * <li>销售人员信息的分页查询</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface TenantSupplierSalesService {

    // ========== 基础操作方法 ==========

    /**
     * 创建租户-供应商-销售人员信息
     *
     * @param createDTO 创建信息
     * @return 编号
     */
    Long createTenantSupplierSales(TenantSupplierSalesDTO createDTO);

    /**
     * 更新租户-供应商-销售人员信息
     *
     * @param updateDTO 更新信息
     */
    void updateTenantSupplierSales(TenantSupplierSalesDTO updateDTO);

    /**
     * 删除租户-供应商-销售人员信息
     *
     * @param id 编号
     */
    void deleteTenantSupplierSales(Long id);

    /**
     * 获得租户-供应商-销售人员信息
     *
     * @param id 编号
     * @return 租户-供应商-销售人员信息
     */
    TenantSupplierSalesDTO getTenantSupplierSales(Long id);

    // ========== 查询方法 ==========

    /**
     * 获得租户-供应商-销售人员信息分页
     *
     * @param pageReqDTO 分页查询
     * @return 租户-供应商-销售人员信息分页
     */
    PageResult<TenantSupplierSalesDTO> getTenantSupplierSalesPage(TenantSupplierSalesPageReqDTO pageReqDTO);

}