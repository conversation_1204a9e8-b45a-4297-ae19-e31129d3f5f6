package com.xyy.saas.localserver.purchase.server.service.stockout.impl;

import com.xyy.saas.localserver.purchase.api.stockout.dto.StockoutBillDetailDTO;
import com.xyy.saas.localserver.purchase.api.stockout.dto.StockoutBillDetailPageReqDTO;
import com.xyy.saas.localserver.purchase.server.convert.stockout.StockoutBillDetailConvert;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.stockout.StockoutBillDetailDO;
import com.xyy.saas.localserver.purchase.server.dal.mysql.stockout.StockoutBillDetailMapper;
import com.xyy.saas.localserver.purchase.server.service.stockout.StockoutBillDetailService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;

import cn.iocoder.yudao.framework.common.pojo.PageResult;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.purchase.enums.ErrorCodeConstants.STOCKOUT_BILL_DETAIL_NOT_EXISTS;

/**
 * （总部）缺货单明细 Service 实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Validated
public class StockoutBillDetailServiceImpl implements StockoutBillDetailService {

    @Resource
    private StockoutBillDetailMapper stockoutBillDetailMapper;

    // ========== 1. 公共方法 ==========

    /**
     * 创建（总部）缺货单明细
     *
     * @param createDTO 创建信息
     * @return 编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createStockoutBillDetail(StockoutBillDetailDTO createDTO) {
        // 1. 将 DTO 转换为 DO
        StockoutBillDetailDO stockoutBillDetail = StockoutBillDetailConvert.INSTANCE.convert2DO(createDTO);
        // 2. 插入
        stockoutBillDetailMapper.insert(stockoutBillDetail);
        // 3. 返回
        return stockoutBillDetail.getId();
    }

    /**
     * 更新（总部）缺货单明细
     *
     * @param updateDTO 更新信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStockoutBillDetail(StockoutBillDetailDTO updateDTO) {
        // 1. 校验存在
        validateStockoutBillDetailExists(updateDTO.getId());
        // 2. 将 DTO 转换为 DO
        StockoutBillDetailDO updateObj = StockoutBillDetailConvert.INSTANCE.convert2DO(updateDTO);
        // 3. 更新
        stockoutBillDetailMapper.updateById(updateObj);
    }

    /**
     * 删除（总部）缺货单明细
     *
     * @param id 编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteStockoutBillDetail(Long id) {
        // 1. 校验存在
        validateStockoutBillDetailExists(id);
        // 2. 删除
        stockoutBillDetailMapper.deleteById(id);
    }

    /**
     * 获得（总部）缺货单明细
     *
     * @param id 编号
     * @return （总部）缺货单明细
     */
    @Override
    public StockoutBillDetailDTO getStockoutBillDetail(Long id) {
        // 1. 获取缺货单明细
        StockoutBillDetailDO stockoutBillDetail = stockoutBillDetailMapper.selectById(id);
        // 2. 将 DO 转换为 DTO
        return StockoutBillDetailConvert.INSTANCE.convert2DTO(stockoutBillDetail);
    }

    /**
     * 获得（总部）缺货单明细分页
     *
     * @param pageReqDTO 分页查询
     * @return （总部）缺货单明细分页
     */
    @Override
    public PageResult<StockoutBillDetailDTO> getStockoutBillDetailPage(
            StockoutBillDetailPageReqDTO pageReqDTO) {
        // 1. 查询分页
        PageResult<StockoutBillDetailDO> pageResult = stockoutBillDetailMapper.selectPage(pageReqDTO);
        // 2. 将 DO 转换为 DTO
        return StockoutBillDetailConvert.INSTANCE.convert2DTO(pageResult);
    }

    // ========== 2. 辅助方法 ==========

    /**
     * 校验（总部）缺货单明细是否存在
     *
     * @param id 编号
     */
    private void validateStockoutBillDetailExists(Long id) {
        if (stockoutBillDetailMapper.selectById(id) == null) {
            throw exception(STOCKOUT_BILL_DETAIL_NOT_EXISTS);
        }
    }

}