package com.xyy.saas.localserver.purchase.server.protocol;

import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.Map;
import static com.xyy.saas.localserver.purchase.enums.ErrorCodeConstants.CLOUD_SERVICE_NOT_EXISTS;

/**
 * 云端服务调用客户端
 */
@Slf4j
@Component
public class CloudServiceClient {

    private final Map<Class<?>, ObjectProvider<?>> clientProviders = new HashMap<>();

    /**
     * 注册云端服务客户端
     *
     * @param clientClass    客户端类型
     * @param clientProvider 客户端提供者
     * @param <T>            客户端类型
     */
    public <T> void registerClient(Class<T> clientClass, ObjectProvider<T> clientProvider) {
        clientProviders.put(clientClass, clientProvider);
    }

    /**
     * 获取云端服务客户端
     *
     * @param clientClass 客户端类型
     * @param <T>         客户端类型
     * @return 客户端实例
     */
    @SuppressWarnings("unchecked")
    public <T> T getClient(Class<T> clientClass) {
        ObjectProvider<?> provider = clientProviders.get(clientClass);
        Assert.notNull(provider, CLOUD_SERVICE_NOT_EXISTS.getMsg());
        return (T) provider.getIfAvailable();
    }

    /**
     * 调用云端服务
     *
     * @param service 服务实例
     * @param <T>     返回类型
     * @return 调用结果
     */
    public <T> CommonResult<T> call(CloudService<T> service) {
        try {
            return service.call();
        } catch (Exception e) {
            log.error("调用云端服务失败", e);
            return null;
        }
    }
}