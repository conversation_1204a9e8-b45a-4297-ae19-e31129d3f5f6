package com.xyy.saas.localserver.purchase.server.service.supplier.impl;

import com.xyy.saas.localserver.purchase.api.supplier.dto.TenantSupplierRelationDTO;
import com.xyy.saas.localserver.purchase.api.supplier.dto.TenantSupplierRelationPageReqDTO;
import com.xyy.saas.localserver.purchase.server.convert.supplier.TenantSupplierRelationConvert;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.supplier.TenantSupplierRelationDO;
import com.xyy.saas.localserver.purchase.server.dal.mysql.supplier.TenantSupplierRelationMapper;
import com.xyy.saas.localserver.purchase.server.service.supplier.SupplierService;
import com.xyy.saas.localserver.purchase.server.service.supplier.TenantSupplierRelationService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.purchase.enums.ErrorCodeConstants.*;

/**
 * 租户-供应商-关联关系 Service 实现类
 *
 * <p>
 * 该实现类提供了租户与供应商关联关系相关的核心业务操作实现，包括：
 * </p>
 * <ul>
 * <li>关联关系的增删改查等基础操作</li>
 * <li>关联关系的分页查询</li>
 * <li>供应商开通和首营审核</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Validated
public class TenantSupplierRelationServiceImpl implements TenantSupplierRelationService {

    @Resource
    private TenantSupplierRelationMapper tenantSupplierRelationMapper;

    @Resource
    private SupplierService supplierService;

    // ========== 基础操作方法 ==========

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTenantSupplierRelation(TenantSupplierRelationDTO createDTO) {
        // 1. 校验供应商是否已开通
        String supplierGuid;
        if (createDTO.getSupplier() != null && StringUtils.isNotBlank(createDTO.getSupplier().getGuid())) {
            supplierGuid = createDTO.getSupplier().getGuid();
            validateTenantSupplierRelationExists(supplierGuid);
        } else {
            supplierGuid = supplierService.createSupplier(createDTO.getSupplier());
        }

        // 2. 插入关联关系
        createDTO.setSupplierGuid(supplierGuid);
        TenantSupplierRelationDO tenantSupplierRelation = TenantSupplierRelationConvert.INSTANCE.convert2DO(createDTO);
        tenantSupplierRelationMapper.insert(tenantSupplierRelation);

        // 3. 创建审批流
        // TODO: 实现审批流创建逻辑

        // 4. 返回
        return tenantSupplierRelation.getId();
    }

    @Override
    public void updateTenantSupplierRelation(TenantSupplierRelationDTO updateDTO) {
        // 1. 校验存在
        validateTenantSupplierRelationExists(updateDTO.getId());
        // 2. 更新
        tenantSupplierRelationMapper.updateById(TenantSupplierRelationConvert.INSTANCE.convert2DO(updateDTO));
    }

    @Override
    public void deleteTenantSupplierRelation(Long id) {
        // 1. 校验存在
        validateTenantSupplierRelationExists(id);
        // 2. 删除
        tenantSupplierRelationMapper.deleteById(id);
    }

    @Override
    public TenantSupplierRelationDTO getTenantSupplierRelation(Long id) {
        // 1. 查询关联关系
        TenantSupplierRelationDO tenantSupplierRelation = tenantSupplierRelationMapper.selectById(id);
        // 2. 转换返回
        return TenantSupplierRelationConvert.INSTANCE.convert2DTO(tenantSupplierRelation);
    }

    // ========== 查询方法 ==========

    @Override
    public PageResult<TenantSupplierRelationDTO> getTenantSupplierRelationPage(
            TenantSupplierRelationPageReqDTO pageReqDTO) {
        // 1. 查询分页
        PageResult<TenantSupplierRelationDO> pageResult = tenantSupplierRelationMapper.selectPage(pageReqDTO);
        // 2. 转换返回
        return TenantSupplierRelationConvert.INSTANCE.convert2DTO(pageResult);
    }

    // ========== 私有方法 ==========

    /**
     * 校验关联关系是否存在
     *
     * @param id 关联关系编号
     */
    private void validateTenantSupplierRelationExists(Long id) {
        if (tenantSupplierRelationMapper.selectById(id) == null) {
            throw exception(TENANT_SUPPLIER_RELATION_NOT_EXISTS);
        }
    }

    /**
     * 校验供应商是否已开通
     *
     * @param supplierGuid 供应商编码
     */
    private void validateTenantSupplierRelationExists(String supplierGuid) {
        if (tenantSupplierRelationMapper.selectBySupplierGuid(supplierGuid) != null) {
            throw exception(TENANT_SUPPLIER_RELATION_EXISTS);
        }
    }

}