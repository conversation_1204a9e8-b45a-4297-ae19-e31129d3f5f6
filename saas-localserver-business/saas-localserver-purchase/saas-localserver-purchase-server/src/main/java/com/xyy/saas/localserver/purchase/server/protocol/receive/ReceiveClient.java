package com.xyy.saas.localserver.purchase.server.protocol.receive;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.localserver.purchase.server.admin.receive.vo.ReceiveBillSaveReqVO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

/**
 * @Desc 收货接口
 * @Date 2025/04/29
 * <AUTHOR>
 */

@HttpExchange(accept = "application/json", contentType = "application/json",url = "${saas-cloud.url}")
public interface ReceiveClient {

	/**
	 * 创建总部收货单
	 *
	 * @param createReqVO 创建请求
	 * @return 编号
	 */
	@PostExchange("saas-cloud/receive-bill/receive")
	CommonResult<Long> createHeadReceive(@RequestBody ReceiveBillSaveReqVO createReqVO);

	/**
	 * （门店）拒收入库
	 *
	 * @param createReqVO 创建请求
	 * @return 拒收结果
	 */
	@PostExchange("saas-cloud/receive-bill/rejectWarehousing")
	CommonResult<Boolean> rejectWarehousing(@RequestBody ReceiveBillSaveReqVO createReqVO);

}
