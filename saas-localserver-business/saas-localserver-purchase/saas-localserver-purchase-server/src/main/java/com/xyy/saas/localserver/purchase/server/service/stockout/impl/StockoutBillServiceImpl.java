package com.xyy.saas.localserver.purchase.server.service.stockout.impl;

import com.xyy.saas.localserver.purchase.api.stockout.dto.StockoutBillDTO;
import com.xyy.saas.localserver.purchase.api.stockout.dto.StockoutBillPageReqDTO;
import com.xyy.saas.localserver.purchase.server.convert.stockout.StockoutBillConvert;
import com.xyy.saas.localserver.purchase.server.convert.stockout.StockoutBillDetailConvert;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.stockout.StockoutBillDO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.stockout.StockoutBillDetailDO;
import com.xyy.saas.localserver.purchase.server.dal.mysql.stockout.StockoutBillDetailMapper;
import com.xyy.saas.localserver.purchase.server.dal.mysql.stockout.StockoutBillMapper;
import com.xyy.saas.localserver.purchase.server.service.stockout.StockoutBillService;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.purchase.enums.ErrorCodeConstants.STOCKOUT_BILL_NOT_EXISTS;

/**
 * 采购-缺货单信息 Service 实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Validated
public class StockoutBillServiceImpl implements StockoutBillService {

    @Resource
    private StockoutBillMapper stockoutBillMapper;

    @Resource
    private StockoutBillDetailMapper stockoutBillDetailMapper;

    // ========== 1. 公共方法 ==========

    /**
     * 创建采购-缺货单信息
     *
     * @param createDTO 创建信息
     * @return 创建结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createStockoutBill(StockoutBillDTO createDTO) {
        // 1. 将 DTO 转换为 DO
        StockoutBillDO stockoutBill = StockoutBillConvert.INSTANCE.convert2DO(createDTO);
        List<StockoutBillDetailDO> stockoutBillDetails = StockoutBillDetailConvert.INSTANCE
                .convert2DO(createDTO.getDetails());
        // 2. 插入
        stockoutBillMapper.insert(stockoutBill);
        stockoutBillDetailMapper.insertBatch(stockoutBillDetails);
        // 3. 返回
        return stockoutBill.getId();
    }

    /**
     * 更新采购-缺货单信息
     *
     * @param updateDTO 更新信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStockoutBill(StockoutBillDTO updateDTO) {
        // 1. 校验存在
        validateStockoutBillExists(updateDTO.getId());
        // 2. 将 DTO 转换为 DO
        StockoutBillDO stockoutBill = StockoutBillConvert.INSTANCE.convert2DO(updateDTO);
        // 3. 更新
        stockoutBillMapper.updateById(stockoutBill);
    }

    /**
     * 删除采购-缺货单信息
     *
     * @param id 编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteStockoutBill(Long id) {
        // 1. 校验存在
        validateStockoutBillExists(id);
        // 2. 删除
        stockoutBillMapper.deleteById(id);
    }

    /**
     * 获得采购-缺货单信息
     *
     * @param id 编号
     * @return 采购-缺货单信息
     */
    @Override
    public StockoutBillDTO getStockoutBill(Long id) {
        // 1. 获取
        StockoutBillDO stockoutBill = stockoutBillMapper.selectById(id);
        // 2. 将 DO 转换为 DTO
        return StockoutBillConvert.INSTANCE.convert2DTO(stockoutBill);
    }

    /**
     * 获得采购-缺货单信息分页
     *
     * @param pageReqDTO 分页查询参数
     * @return 采购-缺货单信息分页
     */
    @Override
    public PageResult<StockoutBillDTO> getStockoutBillPage(StockoutBillPageReqDTO pageReqDTO) {
        // 1. 查询分页
        PageResult<StockoutBillDO> pageResult = stockoutBillMapper.selectPage(pageReqDTO);
        // 2. 将 DO 转换为 DTO
        return StockoutBillConvert.INSTANCE.convert2DTO(pageResult);
    }

    // ========== 2. 辅助方法 ==========

    /**
     * 校验采购-缺货单信息是否存在
     *
     * @param id 编号
     */
    private void validateStockoutBillExists(Long id) {
        if (stockoutBillMapper.selectById(id) == null) {
            throw exception(STOCKOUT_BILL_NOT_EXISTS);
        }
    }
}