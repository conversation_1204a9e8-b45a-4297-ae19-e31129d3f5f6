package com.xyy.saas.localserver.purchase.server.protocol.dispatch;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.localserver.purchase.server.admin.receive.vo.ReceiveBillSaveReqVO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

/**
 * @Desc 总部发货接口
 * @Date 2025/4/29
 * <AUTHOR>
 */

@HttpExchange(accept = "application/json", contentType = "application/json",url = "${saas-cloud.url}")
public interface HeadDispatchClient {

	/**
	 * 入库并发货(调剂用)
	 *
	 * @param createReqVO 创建请求
	 * @return 拒收结果
	 */
	@PostExchange("saas-cloud/head-dispatch/warehousingAndDispatch")
	CommonResult<Boolean> warehousingAndDispatch(@RequestBody ReceiveBillSaveReqVO createReqVO);
}
