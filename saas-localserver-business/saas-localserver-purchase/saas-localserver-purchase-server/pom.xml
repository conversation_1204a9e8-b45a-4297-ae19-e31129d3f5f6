<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.xyy.saas</groupId>
    <artifactId>saas-localserver-purchase</artifactId>
    <version>${revision}</version>
  </parent>

  <artifactId>saas-localserver-purchase-server</artifactId>

  <dependencies>
    <!-- 项目内部依赖 -->
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-localserver-purchase-api</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>
    <!--    <dependency>-->
<!--      <groupId>org.flywaydb</groupId>-->
<!--      <artifactId>flyway-core</artifactId>-->
<!--    </dependency>-->
<!--    <dependency>-->
<!--      <groupId>org.projectlombok</groupId>-->
<!--      <artifactId>lombok</artifactId>-->
<!--    </dependency>-->
  </dependencies>

<!--  <build>-->
<!--    <plugins>-->
<!--      <plugin>-->
<!--        <groupId>org.springframework.boot</groupId>-->
<!--        <artifactId>spring-boot-maven-plugin</artifactId>-->
<!--        <version>${maven-springboot-plugin.version}</version>-->
<!--      </plugin>-->
<!--    </plugins>-->
<!--  </build>-->
</project>