package com.xyy.saas.localserver.inventory.server.service.campon;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.xyy.saas.localserver.inventory.api.campon.dto.InventoryCampOnDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventorySelectItemDTO;
import com.xyy.saas.localserver.inventory.enums.AreaTypeEnum;
import com.xyy.saas.localserver.inventory.enums.InventorySelectEnum;
import com.xyy.saas.localserver.inventory.server.InventoryServerApplication;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@SpringBootTest(classes = InventoryServerApplication.class)
@ActiveProfiles("test")
public class InventoryCampOnServiceImplTest {

    @Resource
    private InventoryCampOnService inventoryCampOnService;
    @Test
    public void testInventoryCampOn() {
        List<InventorySelectItemDTO> items = new ArrayList<>();
        items.add(InventorySelectItemDTO
                .builder()
                .productPref("ZHL21051000")
                .areaType(AreaTypeEnum.QUALIFIED)
                .changeNumber(new BigDecimal("3"))
                .build());

        InventoryCampOnDTO inventoryCampOnDTO = InventoryCampOnDTO
                .builder()
                .billType(1)
                .billNo("PD20250423001")
                .selectMap(Map.of(InventorySelectEnum.FIFO, items))
                .build();
        inventoryCampOnService.campOn(inventoryCampOnDTO);
    }

    @BeforeEach
    public void before() {
        RequestAttributes requestAttributes = new ServletRequestAttributes(new MockHttpServletRequest());
        requestAttributes.setAttribute("login_user_id", 0L, RequestAttributes.SCOPE_REQUEST);
        RequestContextHolder.setRequestAttributes(requestAttributes);
        TenantContextHolder.setTenantId(0L);
    }

}
