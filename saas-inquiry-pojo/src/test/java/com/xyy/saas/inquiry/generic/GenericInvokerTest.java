package com.xyy.saas.inquiry.generic;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xyy.saas.inquiry.generic.model.GenericInvokeRequest;
import com.xyy.saas.inquiry.generic.model.GenericInvokeResponse;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

/**
 * Dubbo 泛化调用测试
 *
 * <AUTHOR>
 */
@SpringBootTest
@TestPropertySource(properties = {
    "inquiry.generic.enabled=true",
    "inquiry.generic.timeout=3000"
})
class GenericInvokerTest {

    // 注意：实际测试需要有可用的 Dubbo 服务提供者

    @Test
    void testGenericInvokeRequestBuilder() {
        GenericInvokeRequest request = GenericInvokeRequest.builder()
                .interfaceName("com.example.UserService")
                .methodName("getUserById")
                .parameterTypes(new String[]{"java.lang.Long"})
                .args(new Object[]{123L})
                .version("1.0.0")
                .group("default")
                .timeout(5000)
                .build();

        assertNotNull(request);
        assertEquals("com.example.UserService", request.getInterfaceName());
        assertEquals("getUserById", request.getMethodName());
        assertEquals("1.0.0", request.getVersion());
        assertEquals("default", request.getGroup());
        assertEquals(Integer.valueOf(5000), request.getTimeout());
        assertArrayEquals(new String[]{"java.lang.Long"}, request.getParameterTypes());
        assertArrayEquals(new Object[]{123L}, request.getArgs());
    }

    @Test
    void testGenericInvokeResponseSuccess() {
        Object result = "test result";
        long duration = 100L;

        GenericInvokeResponse response = GenericInvokeResponse.success(result, duration);

        assertTrue(response.isSuccess());
        assertEquals(result, response.getResult());
        assertEquals(duration, response.getDuration());
        assertNull(response.getErrorMessage());
    }

    @Test
    void testGenericInvokeResponseError() {
        String errorMessage = "调用失败";
        String errorStack = "java.lang.RuntimeException: 调用失败";
        String errorType = "RuntimeException";
        long duration = 50L;

        GenericInvokeResponse response = GenericInvokeResponse.error(errorMessage, errorStack, errorType, duration);

        assertFalse(response.isSuccess());
        assertEquals(errorMessage, response.getErrorMessage());
        assertEquals(errorStack, response.getErrorStack());
        assertEquals(errorType, response.getErrorType());
        assertEquals(duration, response.getDuration());
        assertNull(response.getResult());
    }

    // 模拟测试 - 实际使用时需要真实的 Dubbo 服务
    // @Test
    // void testGenericInvokerService() {
    //     GenericInvokerService service = new GenericInvokerServiceImpl();
    //     
    //     GenericInvokeRequest request = GenericInvokeRequest.builder()
    //             .interfaceName("com.xyy.saas.inquiry.user.api.UserService")
    //             .methodName("getUserById")
    //             .parameterTypes(new String[]{"java.lang.Long"})
    //             .args(new Object[]{123L})
    //             .build();
    //     
    //     GenericInvokeResponse response = service.invoke(request);
    //     
    //     assertNotNull(response);
    //     // 根据实际服务返回验证结果
    // }
}