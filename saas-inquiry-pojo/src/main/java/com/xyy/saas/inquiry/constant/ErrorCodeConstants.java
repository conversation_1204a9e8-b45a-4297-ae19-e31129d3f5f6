package com.xyy.saas.inquiry.constant;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * pojo 基础错误常量类 使用 2-009-000-000 段
 *
 * @Author:chenxia<PERSON>i
 * @Date:2024/09/12 20:47
 */
public interface ErrorCodeConstants {


    ErrorCode EXCEL_DETAIL_DATA_IS_NULL = new ErrorCode(2_009_000_000, "Excel内容不能为空");

    ErrorCode ANALYSIS_IMPORT_EXCEL_ERROR = new ErrorCode(2_009_000_000, "解析Excel失败 {}");

    ErrorCode UPLOAD_EXCEL_URL_IS_EMPTY = new ErrorCode(2_009_000_000, "上传文件链接不能为空");

    ErrorCode UPLOAD_EXCEL_MORE_THAN_LIMIT = new ErrorCode(2_009_000_000, "导入数据超出最大条数:{}条");

    ErrorCode UPLOAD_EXCEL_TEMP_UN_MATCH = new ErrorCode(2_009_000_000, "导入文档与模板不匹配,{},请调整后重新导入");


}
