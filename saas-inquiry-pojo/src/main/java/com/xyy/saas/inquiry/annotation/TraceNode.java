package com.xyy.saas.inquiry.annotation;

import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;

import java.lang.annotation.*;

/**
 * 业务流程链路追踪节点注解
 * 标记在需要追踪的方法上，用于记录方法执行的开始时间、结束时间、执行结果等信息
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface TraceNode {

    /**
     * 节点类型，定义节点的编码、名称和解析器
     */
    TraceNodeEnum node();

    /**
     * 节点类型，定义节点的编码、名称和解析器
     */
    String prefLocation() default "";

    /**
     * 是否异步写入ES，默认为true
     */
    boolean async() default true;
} 