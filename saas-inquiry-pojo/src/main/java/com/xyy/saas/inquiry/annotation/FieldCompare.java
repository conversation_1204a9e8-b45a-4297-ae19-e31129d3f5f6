package com.xyy.saas.inquiry.annotation;

import com.xyy.saas.inquiry.constant.BasicConstant;
import com.xyy.saas.inquiry.constant.BasicConstant.FieldCompareGroup;
import java.lang.annotation.*;

/**
 * 字段比较注解
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface FieldCompare {
    
    /**
     * 字段描述
     */
    String description() default "";
    
    /**
     * 是否忽略null值比较
     */
    boolean ignoreNull() default false;
    
    /**
     * 是否递归比较
     */
    boolean recursive() default false;
    
    /**
     * 比较的字段名,默认使用当前字段名
     */
    String compareField() default "";

    /**
     * 比较的字段名分组
     */
    String[] group() default { FieldCompareGroup.DEFAULT };
} 