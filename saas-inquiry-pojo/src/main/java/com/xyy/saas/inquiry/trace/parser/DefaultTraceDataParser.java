package com.xyy.saas.inquiry.trace.parser;

import org.springframework.stereotype.Component;
import java.lang.reflect.Method;

/**
 * 默认的链路追踪数据解析器
 * 从方法参数中查找第一个字符串类型的参数作为业务单据号
 */
@Component
public class DefaultTraceDataParser extends TraceDataParser {

    public String parseBusinessNo(Method method, Object[] args, Object result ,String prefLocation) {
      return super.defaultParseBusinessNo(method, args, result, prefLocation);
    }

    /**
     * 解析业务数据
     *
     * @param method 被执行的方法
     * @param args   方法参数
     * @return 业务数据，可以是任意对象，会被转换为JSON字符串存储
     */
    public Object parseBusinessData(Method method, Object[] args, Object result) {
        return super.defaultParseBusinessData(method, args, result);
    }
} 