package com.xyy.saas.inquiry.constant;

/**
 * 系统相关常量类
 *
 * @Author:chenxiaoyi
 * @Date:2024/09/06 14:22
 */
public class SystemConstant {

    /**
     * oa相关配置字段
     */
    public static final String OA_CONFIG_DICT = "OA_CONFIG_DICT";

    /**
     * 密码格式校验 请输入8-16位数字、字母大写或小写或符号，至少包含以上三种
     */
    public static final String PASSWORD_PATTERN_REGEXP = "^(?:(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])|(?=.*[0-9])(?=.*[A-Z])(?=.*[!@#$%^*\\\\(\\\\)\\\\{\\\\}|;:'\\\"<>?.,])|(?=.*[0-9])(?=.*[a-z])(?=.*[!@#$%^*\\\\(\\\\)\\\\{\\\\}|;:'\\\"<>?.,])|(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$%^*\\\\(\\\\)\\\\{\\\\}|;:'\\\"<>?.,]))[A-Za-z0-9!@#$%^*\\\\(\\\\)\\\\{\\\\}|;:'\\\"<>?.,]{8,16}$";

    public static final String PASSWORD_PATTERN_REGEXP_MESSAGE = "密码格式不正确,请输入8-16位数字、字母大写或小写或符号，至少包含以上三种";

    /**
     有效期单位
     */
    public static final String VALIDITY_UNIT_DAY = "日" ;
    public static final String VALIDITY_UNIT_MONTH = "月" ;
    public static final String VALIDITY_UNIT_YEAR = "年" ;

    /**
     * 登录失败多少次锁定当前登录账号
     */
    public static final String LOGIN_FAIL_LOCK_COUNT = "login_fail_lock_count";

    /**
     * 登录失败账号锁定多久-单位秒
     */
    public static final String LOGIN_FAIL_LOCK_TIME = "login_fail_lock_time";
}
