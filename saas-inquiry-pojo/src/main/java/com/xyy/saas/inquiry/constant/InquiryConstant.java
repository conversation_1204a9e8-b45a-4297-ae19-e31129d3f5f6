package com.xyy.saas.inquiry.constant;

/**
 * 系统相关常量类
 *
 * @Author:chen<PERSON><PERSON><PERSON>
 * @Date:2024/09/06 14:22
 */
public class InquiryConstant {


    /**
     * 问诊小程序二维码底板背景图地址 eg:<a href="https://upload.ybm100.com/G2/M00/9C/52/CiICX2AaZQmAAoZdAASkBJcp-M4938.png">...</a>
     */
    public static final String INQUIRY_MINI_APP_QR_CODE_BACKGROUND_URL = "inquiry.mini.app.qr.code.background.url";
    /**
     * 问诊小程序二维码扫描跳转路径 eg: pages/index/inquiry
     */
    public static final String INQUIRY_MINI_APP_QR_CODE_PAGES = "inquiry.mini.app.qr.code.pages";

    /**
     * 问诊小程序二维码正式环境体验版TenantIds  eg: 12121515151517
     */
    public static final String INQUIRY_MINI_APP_QR_CODE_TRIAL_TENANT = "inquiry.mini.app.qr.code.trial.tenant";

    /**
     * 问诊公众号二维码底板背景图地址 eg:<a href="https://upload.ybm100.com/G2/M00/9C/52/CiICX2AaZQmAAoZdAASkBJcp-M4938.png">...</a>
     */
    public static final String INQUIRY_MP_QR_CODE_BACKGROUND_URL = "inquiry.mp.qr.code.background.url";

    /**
     * 问诊App二维码
     */
    public static final String INQUIRY_APP_QR_CODE = "inquiry.app.qr.code";
    /**
     * 问诊公众号二维码
     */
    public static final String INQUIRY_MP_QR_CODE = "inquiry.mp.qr.code";

    /**
     * 问诊app二维码下载地址
     */
    public static final String INQUIRY_APP_DOWNLOAD_URL = "inquiry.app.download.url";

    /**
     * 商家查询问诊单范围天数  默认180天
     */
    public static final String TENANT_QUERY_INQUIRY_RANGE_DAY = "tenant.query.inquiry.range.day";

    /**
     * 医生自动抢单量 - 默认 1
     */
    public static final String INQUIRY_DOCTOR_AUTOGRAB_NUM = "inquiry.doctor.autograb.num";

    /**
     * 问诊轮次派单医生数量 - 默认3
     */
    public static final String INQUIRY_DOCTOR_SEND_NUM = "inquiry.doctor.send.num";

    /**
     * 问诊轮次派单间隔时间 - 默认10秒
     */
    public static final String INQUIRY_DISTRIBUTE_POLL_INTERVAL = "inquiry.distribute.poll.interval";

    /**
     * 问诊超时时长 - 默认30分钟
     */
    public static final String INQUIRY_TIME_OUT = "inquiry.time.out";


    /**
     * IM聊天记录归档时间 - 默认5分钟后
     */
    public static final String INQUIRY_IM_PLACE = "inquiry.im.place";

    /**
     * 问诊医生小助问题以及答案
     */
    public static final String INQUIRY_QUESTION_ANSWER = "inquiry.text.qa";

    /**
     * ai问诊间隔真人人数
     */
    public static final String AUTO_INQUIRY_INTERVAL_COUNT = "auto.inquiry.interval.count";

}
