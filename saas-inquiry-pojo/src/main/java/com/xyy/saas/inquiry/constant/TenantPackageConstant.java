package com.xyy.saas.inquiry.constant;

import java.util.Objects;

/**
 * 门店套餐相关常量类
 *
 * @Author:chenxia<PERSON>i
 * @Date:2024/09/06 14:22
 */
public class TenantPackageConstant {

    /**
     * 问诊次数单位
     */
    public static final Long INQUIRY_COST_UNIT = 1L;

    /**
     * 问诊套餐不限额
     */
    public static final Long UN_LIMITED_COST = -1L;

    public static boolean isUnlimitedCost(Long cost) {
        return Objects.equals(cost, UN_LIMITED_COST);
    }

    /**
     * 自定义套餐ID RECHARGE_PACKAGE_ID
     */
    public static final Long RECHARGE_PACKAGE_ID = 0L;

    public static boolean isRechargePackageId(Long packageId) {
        return Objects.equals(packageId, RECHARGE_PACKAGE_ID);
    }


}
