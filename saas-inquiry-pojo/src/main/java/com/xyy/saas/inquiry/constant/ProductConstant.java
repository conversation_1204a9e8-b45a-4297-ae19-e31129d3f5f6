package com.xyy.saas.inquiry.constant;

/**
 * 商品相关常量类
 *
 * @Author:ch<PERSON><PERSON><PERSON><PERSON>
 * @Date:2024/09/06 14:22
 */
public class ProductConstant {

    /**
     * 商品搜索过滤药品类型(第一类精神药品,第二类精神药品,麻醉药品)
     */
    public static final String SEARCH_PRODUCT_NEED_EXCLUDE_BUSINESS_SCOPE_LIST = "search.product.need.exclude.business.scope.list";

    /**
     * 去问诊需要校验身份证必填的药品类型
     */
    public static final String CHECK_ID_CARD_MUST_FILL_BUSINESS_SCOPE_LIST = "check.id.card.must.fill.business.scope.list";

    /**
     * 去问诊需要校验身份证必填的二级分类类型
     */
    public static final String CHECK_ID_CARD_MUST_FILL_SECOND_CATEGORY_LIST = "check.id.card.must.fill.second.category.list";

}
