package com.xyy.saas.inquiry.generic.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * Dubbo 泛化调用请求参数
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GenericInvokeRequest implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 接口名称
     */
    private String interfaceName;

    /**
     * 方法名称
     */
    private String methodName;

    /**
     * 参数类型数组
     */
    private String[] parameterTypes;

    /**
     * 参数值数组
     */
    private Object[] args;

    /**
     * 版本号
     */
    private String version;

    /**
     * 分组
     */
    private String group;

    /**
     * 超时时间(毫秒)
     */
    private Integer timeout;

    /**
     * 重试次数
     */
    private Integer retries;

    /**
     * 附加参数
     */
    private Map<String, Object> attachments;
}