package com.xyy.saas.inquiry.constant;

import com.xyy.saas.inquiry.enums.doctor.DoctorInquiryTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;

/**
 * @Author: xucao
 * @Date: 2024/12/18 15:07
 * @Description: redis 缓存常量类
 */
public class RedisKeyConstants {
    /**********************************医院相关*************************************/
    /**
     * 接诊大厅key
     */
    private final static String RECETION_AREA_KEY = "hospital:recetion:";

    /**
     * 接诊大厅队列key
     *
     * @param hospitalPref   医院编码
     * @param deptPref       科室编码
     * @param autoInquiry    自动开方标识
     * @param inquiryWayType 问诊方式
     * @return 接诊大厅队列key
     */
    public static String getRecetionAreaKey(String hospitalPref, String deptPref, Integer autoInquiry, Integer inquiryWayType) {
        return RECETION_AREA_KEY + hospitalPref + ":" + deptPref + ":" + DoctorInquiryTypeEnum.getPrefixByCode(autoInquiry) + ":" + InquiryWayTypeEnum.getPrefixByCode(inquiryWayType);
    }

    /**
     * 记录问诊单调度了那些接诊大厅
     */
    private final static String INQUIRY_DISPATCH_DEPT_KEY = "inquiry:dispatch:dept:";

    /**
     * 获取问诊单调度了那些接诊大厅的key
     *
     * @param inquiryPref 问诊单号
     * @return
     */
    public static String getInquiryDispatchDeptKey(String inquiryPref) {
        return INQUIRY_DISPATCH_DEPT_KEY + inquiryPref;
    }

    /**********************************医生相关*************************************/

    /**
     * 待调度医生池key
     */
    private final static String DOCTOR_WAIT_POOL_KEY = "doctor:dispatch:";

    /**
     * 获取医生等待池的key
     *
     * @param hospitalPref   医院编码
     * @param deptPref       科室编码
     * @param autoInquiry    自动开方标识
     * @param inquiryWayType 问诊方式
     * @param env            环境
     * @return 医生等待池的key
     */
    public static String getDoctorWaitPoolKey(String hospitalPref, String deptPref, Integer autoInquiry, Integer inquiryWayType, String env) {
        return DOCTOR_WAIT_POOL_KEY + hospitalPref + ":" + deptPref + ":" + DoctorInquiryTypeEnum.getPrefixByCode(autoInquiry) + ":" + InquiryWayTypeEnum.getPrefixByCode(inquiryWayType) + ":" + env;
    }

    /**
     * 医生负载均衡key
     */
    private final static String DOCTOR_LOADBALANC_KEY = "doctor:loadBalanc:";

    /**
     * 获取医生负载均衡key
     *
     * @param hospitalPref   医院编码
     * @param deptPref       科室编码
     * @param autoInquiry    自动开方标识
     * @param inquiryWayType 问诊方式
     * @param env            环境
     * @return 医生等待池的key
     */
    public static String getDoctorLoadbalancKey(String hospitalPref, String deptPref, Integer autoInquiry, Integer inquiryWayType, String env) {
        return DOCTOR_LOADBALANC_KEY + hospitalPref + ":" + deptPref + ":" + DoctorInquiryTypeEnum.getPrefixByCode(autoInquiry) + ":" + InquiryWayTypeEnum.getPrefixByCode(inquiryWayType) + ":" + env;
    }


    /**
     * 获取当前医生可接诊问诊单列表
     */
    private final static String DOCTOR_CAN_RECEPTION_KEY = "doctor:reception:";

    /**
     * 获取医生可接诊列表的key
     *
     * @param doctorPref 医生编码
     * @param env        环境
     * @return 医生可接诊列表的key
     */
    public static String getDoctorCanReceptionKey(String doctorPref, String env) {
        return DOCTOR_CAN_RECEPTION_KEY + doctorPref + ":" + env;
    }


    /**
     * 医生接诊中的问诊单列表缓存key
     */
    private final static String DOCTOR_RECEPTION_KEY = "doctor:room:";

    /**
     * 获取医生接诊中的列表的key
     *
     * @param doctorPref 医生编码
     * @param env        环境
     * @return 医生接诊中的列表的key
     */
    public static String getDoctorReceptionKey(String doctorPref, String env) {
        return DOCTOR_RECEPTION_KEY + doctorPref + ":" + env;
    }


    private final static String DOCTOR_INTERVAL_KEY = "doctor:interval:";

    /**
     * 获取医生接诊中的列表的key
     *
     * @param doctorPref 医生编码
     * @return 医生开方间隔key
     */
    public static String getDoctorIntervalKey(String doctorPref) {
        return DOCTOR_INTERVAL_KEY + doctorPref;
    }

    private final static String DOCTOR_PROVINCE_KEY = "doctor:province:";

    /**
     * 获取医生省份编码的key
     *
     * @param doctorPref 医生编码
     * @return 医生省份编码的key
     */
    public static String getDoctorProvinceCodeKey(String doctorPref) {
        return DOCTOR_PROVINCE_KEY + doctorPref;
    }

    private final static String DOCTOR_AUTOGRAB_KEY = "doctor:autograb:";

    /**
     * 获取自动抢单医生队列的key
     *
     * @param envTag 环境标识
     * @return 环境标识
     */
    public static String getDoctorAutoGrabKey(String envTag) {
        return DOCTOR_AUTOGRAB_KEY + envTag;
    }

    private final static String DOCTOR_CURRENT_AUTOGRAB_KEY = "doctor:current:autograb:";

    /**
     * 获取当前医生自动接诊的问诊队列
     *
     * @param doctorPref 医生编码
     * @param envTag     环境表示
     * @return key
     */
    public static String getDoctorCurrentAutoGrabKey(String doctorPref, String envTag) {
        return DOCTOR_CURRENT_AUTOGRAB_KEY + doctorPref + ":" + envTag;
    }

    /**********************************问诊相关*************************************/

    /**
     * 门店 待接诊 问诊单列表
     */
    private final static String DRUGSTORE_INQUIRY_PRE_KEY = "drugstore:inquiry:";

    /**
     * 获取门店(待接诊)的key
     *
     * @param tenantId 租户id
     * @return 门店待接诊的rediskey
     */
    public static String getDrugstoreInquiryKey(Long tenantId, String env) {
        return DRUGSTORE_INQUIRY_PRE_KEY + tenantId + ":" + env;
    }


    /**
     * 问诊派单医生缓存列表
     */
    private final static String INQUIRY_SEND_DOCTOR_PRE_KEY = "inquiry:send:";

    /**
     * 获取问诊派单医生缓存key
     *
     * @param inquiryPref 问诊单号
     * @return key
     */
    public static String getSendInquiryKey(String inquiryPref) {
        return INQUIRY_SEND_DOCTOR_PRE_KEY + inquiryPref;
    }


    /**
     * 门店 接诊中 问诊单列表
     */
    private final static String DRUGSTORE_CURRENT_INQUIRY_PRE_KEY = "drugstore:current:inquiry:";

    /**
     * 获取门店接诊中的问诊缓存key
     *
     * @param tenantId 租户id
     * @param env      环境
     * @return key
     */
    public static String getDrugstoreCurrentInquiryKey(Long tenantId, String env) {
        return DRUGSTORE_CURRENT_INQUIRY_PRE_KEY + tenantId + ":" + env;
    }

    /**
     * 当前问诊单被哪个医生接诊
     */
    private final static String INQUIRY_CURRENT_DOCTOR_PRE_KEY = "inquiry:current:";

    /**
     * 获取当前问诊单号对应的医生缓存key
     *
     * @param inquiryPref 问诊单号
     * @return key
     */
    public static String getInquiryCurrentDoctorKey(String inquiryPref) {
        return RedisKeyConstants.INQUIRY_CURRENT_DOCTOR_PRE_KEY + inquiryPref;
    }


    /**
     * 具有医保编码的医生队列
     */
    private final static String DOCTOR_MEDICARE_QUEUE_PRE_KEY = "doctor:medicare:";

    /**
     * 获取具有医保编码的医生队列缓存key
     *
     * @param env 环境
     * @return key
     */
    public static String getDoctorMedicareQueueKey(String env) {
        return RedisKeyConstants.DOCTOR_MEDICARE_QUEUE_PRE_KEY + env;
    }



    /**
     * 三方渠道患者问诊lockKey :
     */
    public static final String THIRD_PARTY_PRE_INQUIRY_LOCK_KEY = "patient:thirdParty:inquiry:lockKey:";

    /**
     * 获取三方预问诊锁key
     *
     * @param thirdPartyPreInquiryId 三方预问诊id
     * @return key
     */
    public static String getThirdPartyPreInquiryLockKey(Long thirdPartyPreInquiryId) {
        return RedisKeyConstants.THIRD_PARTY_PRE_INQUIRY_LOCK_KEY + thirdPartyPreInquiryId;
    }

    /**
     * 处方审核锁单号 KEY 格式：pharmacist:prescription:audit:lock:{处方pref} 用于 审核、驳回、超时释放、推送远程审方
     */
    public static final String PRESCRIPTION_AUDIT_LOCK = "pharmacist:prescription:audit:lock:";
}
