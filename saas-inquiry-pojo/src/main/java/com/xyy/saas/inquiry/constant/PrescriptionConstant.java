package com.xyy.saas.inquiry.constant;

/**
 * 处方相关常量
 *
 * @Author:ch<PERSON><PERSON><PERSON><PERSON>
 * @Date:2024/12/18 11:28
 */
public class PrescriptionConstant {

    /**
     * 处方开具超时时间 单位:分钟 默认30min
     */
    public static final String PRESCRIPTION_ISSUE_TIMEOUT = "prescription.issue.timeout";

    /**
     * 处方开具超时提醒通知配置
     */
    public static final String PRESCRIPTION_ISSUE_TIMEOUT_NOTIFY = "prescription.issue.timeout.notify";

    /**
     * 处方签章默认同步的平台 default:1 法大大
     */
    public static final String PRESCRIPTION_SIGNATURE_DEFAULT_SYNC_PLATFORM = "prescription.signature.default.sync.platform";

    /**
     * 远程审方水印图片地址
     */
    public static final String REMOTE_PRESCRIPTION_SIGNATURE_URL = "signature.remote.audit.prescription.watermark";

    /**
     * 远程审方方式水印图片地址
     */
    public static final String REMOTE_WAY_PRESCRIPTION_SIGNATURE_URL = "signature.remote.way.audit.prescription.watermark";

    /**
     * 远程审方驳回水印图片地址
     */
    public static final String REMOTE_PRESCRIPTION_AUDIT_REJECT_SIGNATURE_URL = "signature.remote.audit.reject.prescription.watermark";

    /**
     * 远程审方文字图片地址 eg:远程审方药师签字:
     */
    public static final String REMOTE_PRESCRIPTION_AUDIT_TEXT_SIGNATURE_URL = "signature.remote.audit.prescription.text.watermark";

    /**
     * 远程审方水印便宜量
     */
    public static final String REMOTE_PRESCRIPTION_SIGNATURE_OFFSET = "signature.remote.audit.prescription.offset";

    /**
     * 门店信息变更批量远程审方的页大小 -默认100
     */
    public static final String REMOTE_PRESCRIPTION_BATCH_CANCEL_PAGE_SIZE = "remote.prescription.batch.cancel.page.size";

    /**
     * 门店信息变更批量取消远程审方的间隔时长-单位毫秒  -默认500
     */
    public static final String REMOTE_PRESCRIPTION_BATCH_CANCEL_INTERVAL = "remote.prescription.batch.cancel.interval";

    /**
     * 处方类型
     */
    public static final String PRESCRIPTION_TYPE = "prescription_type";

}
