package com.xyy.saas.inquiry.annotation.forcemaster;

import org.apache.ibatis.builder.StaticSqlSource;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import java.lang.reflect.Method;

/**
 * @Author: xucao
 * @DateTime: 2025/4/23 21:39
 * @Description: mysql 强制查主库拦截器
 **/
@Intercepts({
    @Signature(type = Executor.class,
        method = "query",
        args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
    @Signature(type = Executor.class,
        method = "update",
        args = {MappedStatement.class, Object.class})
})
public class ForceMasterInterceptor implements Interceptor {

    private static final String FORCE_MASTER_HINT = "/*FORCE_MASTER*/";

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object[] args = invocation.getArgs();
        MappedStatement ms = (MappedStatement) args[0];

        try {
            Method method = getMethod(ms);
            if (method != null && method.isAnnotationPresent(ForceMaster.class)) {
                Object parameter = args.length > 1 ? args[1] : null;
                BoundSql boundSql = ms.getBoundSql(parameter);

                // 构建带强制主库提示的SQL
                String modifiedSql = FORCE_MASTER_HINT + " " + boundSql.getSql();
                SqlSource newSqlSource = new StaticSqlSource(ms.getConfiguration(), modifiedSql, boundSql.getParameterMappings());

                // 创建新MappedStatement
                MappedStatement newMs = copyFromMappedStatement(ms, newSqlSource);
                args[0] = newMs;
            }
        } catch (Exception e) {
            // 异常处理逻辑
        }

        return invocation.proceed();
    }

    /**
     * 根据MappedStatement获取对应Method对象[1,5](@ref)
     */
    private Method getMethod(MappedStatement ms) throws ClassNotFoundException, NoSuchMethodException {
        String id = ms.getId();
        int lastDotIndex = id.lastIndexOf('.');
        String className = id.substring(0, lastDotIndex);
        String methodName = id.substring(lastDotIndex + 1);

        Class<?> mapperInterface = Class.forName(className);
        for (Method method : mapperInterface.getMethods()) {
            if (method.getName().equals(methodName)) {
                return method;
            }
        }
        throw new NoSuchMethodException(methodName);
    }

    /**
     * 复制并创建新的MappedStatement[5](@ref)
     */
    private MappedStatement copyFromMappedStatement(MappedStatement ms, SqlSource newSqlSource) {
        return new MappedStatement.Builder(ms.getConfiguration(), ms.getId(), newSqlSource, ms.getSqlCommandType())
            .resource(ms.getResource())
            .fetchSize(ms.getFetchSize())
            .statementType(ms.getStatementType())
            .keyGenerator(ms.getKeyGenerator())
            .keyProperty(arrayToString(ms.getKeyProperties()))
            .keyColumn(arrayToString(ms.getKeyColumns()))
            .databaseId(ms.getDatabaseId())
            .lang(ms.getLang())
            .resultOrdered(ms.isResultOrdered())
            .resultSets(arrayToString(ms.getResultSets()))
            .resultMaps(ms.getResultMaps())
            .resultSetType(ms.getResultSetType())
            .timeout(ms.getTimeout())
            .parameterMap(ms.getParameterMap())
            .cache(ms.getCache())
            .flushCacheRequired(ms.isFlushCacheRequired())
            .useCache(ms.isUseCache())
            .build();
    }

    private String arrayToString(String[] array) {
        return array != null && array.length > 0 ? String.join(",", array) : null;
    }

}
