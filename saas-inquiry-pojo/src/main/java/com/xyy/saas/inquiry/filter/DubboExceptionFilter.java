package com.xyy.saas.inquiry.filter;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.Invocation;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.Result;
import org.apache.dubbo.rpc.filter.ExceptionFilter;

/**
 * @Author: xucao
 * @DateTime: 2025/6/23 16:47
 * @Description: dubbo 异常过滤器，防止ServiceException被包装导致SkyWalking监控异常
 **/
@Activate(group = {CommonConstants.PROVIDER}, order = -10000)
public class DubboExceptionFilter extends ExceptionFilter {
    @Override
    public void onResponse(Result appResponse, Invoker<?> invoker, Invocation invocation) {
        // 添加对 ServiceException 的特殊处理，确保SkyWalking可以正确忽略
        if (appResponse.hasException()) {
            Throwable exception = appResponse.getException();
            
            // 直接检查是否为ServiceException或其子类
            if (exception instanceof ServiceException serviceException) {
                appResponse.setException(serviceException);
                return;
            }
            
            // 检查是否为包装的ServiceException（如RuntimeException包装的ServiceException）
            if (exception.getCause() instanceof ServiceException serviceException) {
                appResponse.setException(serviceException);
                return;
            }
        }
        // 其他异常按默认逻辑处理
        super.onResponse(appResponse, invoker, invocation);
    }
}
