package com.xyy.saas.inquiry.generic.example;

import com.xyy.saas.inquiry.generic.model.GenericInvokeRequest;
import com.xyy.saas.inquiry.generic.model.GenericInvokeResponse;
import com.xyy.saas.inquiry.generic.service.GenericInvokerService;
import com.xyy.saas.inquiry.generic.util.GenericInvokerUtil;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * Dubbo 泛化调用使用示例
 * 
 * <AUTHOR>
 */
@Slf4j
public class GenericInvokerExample {

    @Setter
    private GenericInvokerService genericInvokerService;

    /**
     * 示例1：使用服务注入方式调用
     */
    public void exampleWithService() {
        // 构建请求参数
        GenericInvokeRequest request = GenericInvokeRequest.builder()
                .interfaceName("com.xyy.saas.inquiry.user.api.UserService")
                .methodName("getUserById")
                .parameterTypes(new String[]{"java.lang.Long"})
                .args(new Object[]{123L})
                .version("1.0.0")
                .timeout(3000)
                .build();

        // 执行调用
        GenericInvokeResponse response = genericInvokerService.invoke(request);

        // 处理结果
        if (response.isSuccess()) {
            log.info("调用成功，结果: {}, 耗时: {}ms", response.getResult(), response.getDuration());
        } else {
            log.error("调用失败: {}", response.getErrorMessage());
        }
    }

    /**
     * 示例2：使用工具类静态方法调用
     */
    public void exampleWithUtil() {
        // 简化调用
        GenericInvokeResponse response = GenericInvokerUtil.invoke(
                "com.xyy.saas.inquiry.user.api.UserService",
                "getUserById",
                new String[]{"java.lang.Long"},
                new Object[]{123L}
        );

        // 处理结果
        if (response.isSuccess()) {
            // 直接获取结果并转换类型
            Object user = GenericInvokerUtil.getResult(response, Object.class);
            log.info("用户信息: {}", user);
        } else {
            log.error("调用失败: {}", response.getErrorMessage());
        }
    }

    /**
     * 示例3：异步调用
     */
    public void exampleAsync() {
        GenericInvokerUtil.invokeAsync(
                "com.xyy.saas.inquiry.user.api.UserService",
                "getUserById",
                new String[]{"java.lang.Long"},
                new Object[]{123L}
        ).thenAccept(response -> {
            if (response.isSuccess()) {
                log.info("异步调用成功: {}", response.getResult());
            } else {
                log.error("异步调用失败: {}", response.getErrorMessage());
            }
        }).exceptionally(throwable -> {
            log.error("异步调用异常", throwable);
            return null;
        });
    }

    /**
     * 示例4：无参数方法调用
     */
    public void exampleNoParams() {
        GenericInvokeResponse response = GenericInvokerUtil.invoke(
                "com.xyy.saas.inquiry.user.api.UserService",
                "getAllUsers"
        );

        if (response.isSuccess()) {
            log.info("获取所有用户成功: {}", response.getResult());
        }
    }

    /**
     * 示例5：单参数方法调用
     */
    public void exampleSingleParam() {
        GenericInvokeResponse response = GenericInvokerUtil.invoke(
                "com.xyy.saas.inquiry.user.api.UserService",
                "getUserByName",
                "java.lang.String",
                "张三"
        );

        if (response.isSuccess()) {
            log.info("根据姓名查询用户成功: {}", response.getResult());
        }
    }
}