//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.xyy.saas.inquiry.exception;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import lombok.Data;
import org.apache.skywalking.apm.toolkit.trace.IgnoredException;

@Data
@IgnoredException
public final class MqConsumerLaterException extends RuntimeException {

    private Integer code;
    private String message;

    public MqConsumerLaterException() {
    }

    public MqConsumerLaterException(ErrorCode errorCode) {
        this.code = errorCode.getCode();
        this.message = errorCode.getMsg();
    }

    public MqConsumerLaterException(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public MqConsumerLaterException(String message) {
        this.code = -1;
        this.message = message;
    }


}
