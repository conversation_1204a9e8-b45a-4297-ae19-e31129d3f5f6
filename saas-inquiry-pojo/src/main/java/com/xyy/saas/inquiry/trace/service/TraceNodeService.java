package com.xyy.saas.inquiry.trace.service;

import com.xyy.saas.inquiry.trace.model.TraceNodeData;

import java.util.List;

/**
 * 链路追踪服务接口
 */
public interface TraceNodeService {

    /**
     * 保存链路追踪数据
     *
     * @param traceNodeData 链路追踪数据
     */
    void saveTraceNode(TraceNodeData traceNodeData);

    /**
     * 异步保存链路追踪数据
     *
     * @param traceNodeData 链路追踪数据
     */
    void saveTraceNodeAsync(TraceNodeData traceNodeData);

    /**
     * 批量保存链路追踪数据
     * 🔥 新增：真正的ES批量写入，一次HTTP请求写入多条记录
     *
     * @param traceNodeDataList 链路追踪数据列表
     * @return 成功保存的数量
     */
    int batchSaveTraceNode(List<TraceNodeData> traceNodeDataList);

    /**
     * 根据条件分页查询链路追踪数据
     *
     * @param query 查询条件
     * @return 分页结果
     */
    PageResult<TraceNodeData> findByCondition(Object query);

    /**
     * 分页查询结果
     *
     * @param <T> 结果类型
     */
    class PageResult<T> {
        private List<T> content;
        private long total;

        public PageResult(List<T> content, long total) {
            this.content = content;
            this.total = total;
        }

        public List<T> getContent() {
            return content;
        }

        public void setContent(List<T> content) {
            this.content = content;
        }

        public long getTotal() {
            return total;
        }

        public void setTotal(long total) {
            this.total = total;
        }
    }
} 