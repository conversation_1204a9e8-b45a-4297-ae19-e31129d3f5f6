package com.xyy.saas.inquiry.generic.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * Dubbo 泛化调用响应结果
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GenericInvokeResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 调用是否成功
     */
    private boolean success;

    /**
     * 返回结果
     */
    private Object result;

    /**
     * 异常信息
     */
    private String errorMessage;

    /**
     * 异常堆栈
     */
    private String errorStack;

    /**
     * 异常类型
     */
    private String errorType;

    /**
     * 调用耗时(毫秒)
     */
    private long duration;

    /**
     * 创建成功响应
     */
    public static GenericInvokeResponse success(Object result, long duration) {
        return GenericInvokeResponse.builder()
                .success(true)
                .result(result)
                .duration(duration)
                .build();
    }

    /**
     * 创建失败响应
     */
    public static GenericInvokeResponse error(String errorMessage, String errorStack, String errorType, long duration) {
        return GenericInvokeResponse.builder()
                .success(false)
                .errorMessage(errorMessage)
                .errorStack(errorStack)
                .errorType(errorType)
                .duration(duration)
                .build();
    }
}