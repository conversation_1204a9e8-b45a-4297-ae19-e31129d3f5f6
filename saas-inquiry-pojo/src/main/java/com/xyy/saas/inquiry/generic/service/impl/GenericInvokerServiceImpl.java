package com.xyy.saas.inquiry.generic.service.impl;

import com.xyy.saas.inquiry.generic.config.GenericInvokerProperties;
import com.xyy.saas.inquiry.generic.exception.GenericInvokerException;
import com.xyy.saas.inquiry.generic.model.GenericInvokeRequest;
import com.xyy.saas.inquiry.generic.model.GenericInvokeResponse;
import com.xyy.saas.inquiry.generic.service.GenericInvokerService;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.apache.dubbo.config.context.ConfigManager;
import org.apache.dubbo.rpc.model.ApplicationModel;
import org.apache.dubbo.rpc.service.GenericService;
import org.springframework.beans.factory.annotation.Value;

/**
 * Dubbo 泛化调用服务实现
 *
 * <AUTHOR>
 */
@Slf4j
public class GenericInvokerServiceImpl implements GenericInvokerService {

    @Value("${spring.application.name:inquiry-generic-invoker}")
    private String applicationName;

    @Value("${dubbo.registry.address:}")
    private String registryAddress;

    @Value("${dubbo.registry.protocol:nacos}")
    private String registryProtocol;

    @Setter
    private GenericInvokerProperties genericInvokerProperties;

    private final ConcurrentHashMap<String, GenericService> serviceCache = new ConcurrentHashMap<>();

    @Override
    public GenericInvokeResponse invoke(GenericInvokeRequest request) {
        long startTime = System.currentTimeMillis();
        
        try {
            validateRequest(request);
            
            GenericService genericService = getOrCreateGenericService(request);
            
            Object result = genericService.$invoke(
                request.getMethodName(),
                request.getParameterTypes(),
                request.getArgs()
            );
            
            long duration = System.currentTimeMillis() - startTime;
            log.debug("泛化调用成功: {}.{}, 耗时: {}ms", request.getInterfaceName(), request.getMethodName(), duration);
            
            return GenericInvokeResponse.success(result, duration);
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("泛化调用失败: {}.{}, 耗时: {}ms", request.getInterfaceName(), request.getMethodName(), duration, e);
            
            return GenericInvokeResponse.error(
                e.getMessage(),
                getStackTrace(e),
                e.getClass().getSimpleName(),
                duration
            );
        }
    }

    @Override
    public CompletableFuture<GenericInvokeResponse> invokeAsync(GenericInvokeRequest request) {
        return CompletableFuture.supplyAsync(() -> invoke(request));
    }

    @Override
    public GenericInvokeResponse invoke(String interfaceName, String methodName, String[] parameterTypes, Object[] args) {
        GenericInvokeRequest request = GenericInvokeRequest.builder()
                .interfaceName(interfaceName)
                .methodName(methodName)
                .parameterTypes(parameterTypes)
                .args(args)
                .build();
        return invoke(request);
    }

    private GenericService getOrCreateGenericService(GenericInvokeRequest request) {
        String cacheKey = buildCacheKey(request);
        
        return serviceCache.computeIfAbsent(cacheKey, key -> {
            try {
                return createGenericService(request);
            } catch (Exception e) {
                throw new GenericInvokerException("创建泛化服务失败: " + request.getInterfaceName(), e);
            }
        });
    }

    private GenericService createGenericService(GenericInvokeRequest request) {
        // 1. 获取或创建应用模型
        ApplicationModel applicationModel = ApplicationModel.defaultModel();

        // 2. 配置全局应用设置 (替代 setApplication)
        ApplicationConfig application = new ApplicationConfig();
        application.setName(applicationName);

        // 将应用配置添加到ConfigManager
        ConfigManager configManager = applicationModel.getApplicationConfigManager();
        configManager.setApplication(application);

        // 3. 配置注册中心 (如果存在)
        if (StringUtils.isNotBlank(registryAddress)) {
            RegistryConfig registry = new RegistryConfig();
            registry.setAddress(registryAddress);
            registry.setProtocol(registryProtocol);

            // 添加到ConfigManager
            configManager.addRegistry(registry);
        }

        // 4. 创建ReferenceConfig时关联ModuleModel
        ReferenceConfig<GenericService> reference = new ReferenceConfig<>(applicationModel.getDefaultModule());

        // 5. 设置接口相关配置
        reference.setInterface(request.getInterfaceName());
        reference.setGeneric("true");

        // 6. 设置版本和分组
        if (StringUtils.isNotBlank(request.getVersion())) {
            reference.setVersion(request.getVersion());
        }
        if (StringUtils.isNotBlank(request.getGroup())) {
            reference.setGroup(request.getGroup());
        }
        
        // 设置超时和重试
        int timeout = Optional.ofNullable(request.getTimeout()).orElseGet(genericInvokerProperties::getTimeout);
        int retries = Optional.ofNullable(request.getRetries()).orElseGet(genericInvokerProperties::getRetries);
        reference.setTimeout(timeout);
        reference.setRetries(retries);
        
        // 设置过滤器
        // reference.setFilter(genericInvokerProperties.getFilter());
        
        // 设置检查
        reference.setCheck(false);
        
        log.info("创建泛化服务引用: {}, version: {}, group: {}, timeout: {}ms, retries: {}",
                request.getInterfaceName(), request.getVersion(), request.getGroup(), timeout, retries);
        
        return reference.get();
    }

    private String buildCacheKey(GenericInvokeRequest request) {
        StringBuilder keyBuilder = new StringBuilder(request.getInterfaceName());
        if (StringUtils.isNotBlank(request.getVersion())) {
            keyBuilder.append(":").append(request.getVersion());
        }
        if (StringUtils.isNotBlank(request.getGroup())) {
            keyBuilder.append(":").append(request.getGroup());
        }
        return keyBuilder.toString();
    }

    private void validateRequest(GenericInvokeRequest request) {
        if (request == null) {
            throw new GenericInvokerException("调用请求不能为空");
        }
        if (StringUtils.isBlank(request.getInterfaceName())) {
            throw new GenericInvokerException("接口名称不能为空");
        }
        if (StringUtils.isBlank(request.getMethodName())) {
            throw new GenericInvokerException("方法名称不能为空");
        }
        if (request.getParameterTypes() == null) {
            request.setParameterTypes(new String[0]);
        }
        if (request.getArgs() == null) {
            request.setArgs(new Object[0]);
        }
        if (request.getParameterTypes().length != request.getArgs().length) {
            throw new GenericInvokerException("参数类型和参数值数量不匹配");
        }
    }

    private String getStackTrace(Throwable e) {
        try {
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return sw.toString();
        } catch (Exception ex) {
            return "获取堆栈信息失败: " + ex.getMessage();
        }
    }
}