package com.xyy.saas.inquiry.generic.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Dubbo 泛化调用配置属性
 * 
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "inquiry.generic")
public class GenericInvokerProperties {

    /**
     * 是否启用泛化调用
     */
    private boolean enabled = true;

    /**
     * 默认超时时间(毫秒)
     */
    private int timeout = 10000;

    /**
     * 默认重试次数
     */
    private int retries = 0;

    /**
     * 过滤器列表
     */
    private String filter = "dubboTenantFilter,exception";

}