package com.xyy.saas.inquiry.generic.config;

import com.xyy.saas.inquiry.generic.example.GenericInvokerExample;
import com.xyy.saas.inquiry.generic.service.GenericInvokerService;
import com.xyy.saas.inquiry.generic.service.impl.GenericInvokerServiceImpl;
import com.xyy.saas.inquiry.generic.util.GenericInvokerUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Dubbo 泛化调用自动配置
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
@ConditionalOnClass(org.apache.dubbo.rpc.service.GenericService.class)
@ConditionalOnProperty(prefix = "inquiry.generic", name = "enabled", havingValue = "true")
@EnableConfigurationProperties(GenericInvokerProperties.class)
public class GenericInvokerAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public GenericInvokerService genericInvokerService(GenericInvokerProperties genericInvokerProperties) {
        log.info("初始化 Dubbo 泛化调用服务");
        GenericInvokerServiceImpl genericInvokerService = new GenericInvokerServiceImpl();
        genericInvokerService.setGenericInvokerProperties(genericInvokerProperties);

        log.info("初始化 Dubbo 泛化调用工具类（服务注入）");
        GenericInvokerUtil.setGenericInvokerService(genericInvokerService);
        return genericInvokerService;
    }

    @Bean
    @ConditionalOnMissingBean
    public GenericInvokerExample genericInvokerExample(GenericInvokerService genericInvokerService) {
        log.info("初始化 Dubbo 泛化调用示例");
        GenericInvokerExample example = new GenericInvokerExample();
        example.setGenericInvokerService(genericInvokerService);
        return example;
    }
}