package com.xyy.saas.inquiry.constant;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;

import java.util.List;
import java.util.Objects;

/**
 * 门店相关常量类
 *
 * @Author:chenxiaoyi
 * @Date:2024/09/06 14:22
 */
public class TenantConstant {

    /**
     * 默认用户
     */
    public static final String DEFAULT_USER = "0";


    /**
     * 默认用户
     */
    public static final String FREQUENT_TENANT_PREFIX = "frequent_tenant_";

    /**
     * 默认系统门店id
     */
    public static final Long DEFAULT_TENANT_ID = 1L;

    public static boolean isSystemTenant() {
        return Objects.equals(DEFAULT_TENANT_ID, TenantContextHolder.getTenantId());
    }

    public static boolean isDefaultTenant() {
        return TenantContextHolder.isIgnore() || Objects.equals(DEFAULT_TENANT_ID, TenantContextHolder.getTenantId());
    }

    /**
     * 门店id常量字段
     */
    public static final String TENANT_ID = "tenant_id";

    public static final String TENANT_IGNORE = "tenant_ignore";

    public static final String THIRD_APP_ID = "third_app_id";

    /**
     * 租户上下文信息key：tenantDto
     * <pre>使用说明：{@code TenantDto tenantDto = TenantContextHolder.getTenantContextInfo(TENANT_CONTEXT_KEY_TENANT_DTO);}</pre>
     */
    public static final String TENANT_CONTEXT_KEY_TENANT_DTO = "tenantDto";


    /**
     * 系统默认字典 + 租户自定义字典
     * @param tenantId
     * @return
     */
    public static List<Long> getTenantIds(Long tenantId) {
        if (tenantId == null || tenantId.equals(DEFAULT_TENANT_ID)) {
            return List.of(DEFAULT_TENANT_ID);
        }
        return List.of(tenantId, DEFAULT_TENANT_ID);
    }
}
