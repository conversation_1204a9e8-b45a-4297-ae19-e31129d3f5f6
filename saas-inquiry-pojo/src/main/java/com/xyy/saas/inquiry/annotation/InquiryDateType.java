package com.xyy.saas.inquiry.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 问诊日期类型转换注解
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.METHOD})
public @interface InquiryDateType {

    /**
     * 转换的原属性字段 value
     */
    String value() default "";

}
