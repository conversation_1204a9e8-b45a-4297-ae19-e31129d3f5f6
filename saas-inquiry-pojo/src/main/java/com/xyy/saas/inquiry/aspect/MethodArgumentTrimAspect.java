package com.xyy.saas.inquiry.aspect;

import cn.hutool.core.util.ReflectUtil;
import com.xyy.saas.inquiry.annotation.MethodArgumentTrim;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Parameter;
import java.util.Collection;
import java.util.Objects;

/**
 * 请求参数String类型字段去除首尾空格处理器
 */
@Slf4j
@Aspect
@Component
public class MethodArgumentTrimAspect {

    @Around("execution(* *(.., @com.xyy.saas.inquiry.annotation.MethodArgumentTrim (*), ..))")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        Object[] args = point.getArgs();
        if (args == null || args.length == 0) {
            return point.proceed();
        }

        // 获取方法参数
        MethodSignature signature = (MethodSignature) point.getSignature();
        Parameter[] parameters = signature.getMethod().getParameters();
        Annotation[][] parameterAnnotations = signature.getMethod().getParameterAnnotations();

        // 处理参数
        for (int i = 0; i < parameters.length; i++) {
            MethodArgumentTrim annotation = getRequestTrimAnnotation(parameterAnnotations[i]);
            if (annotation == null || args[i] == null) {
                continue;
            }
            args[i] = processObject(args[i], annotation.nullToEmpty());
        }

        return point.proceed(args);
    }

    /**
     * 获取RequestTrim注解
     */
    private MethodArgumentTrim getRequestTrimAnnotation(Annotation[] annotations) {
        if (annotations == null) {
            return null;
        }
        for (Annotation annotation : annotations) {
            if (annotation instanceof MethodArgumentTrim) {
                return (MethodArgumentTrim) annotation;
            }
        }
        return null;
    }

    /**
     * 处理对象
     *
     * @param obj 对象
     * @param nullToEmpty 是否将null转为空字符串
     * @return 处理后的对象
     */
    private Object processObject(Object obj, boolean nullToEmpty) {
        if (obj == null) {
            return null;
        }

        Class<?> clazz = obj.getClass();
        // 处理字符串
        if (String.class.isAssignableFrom(clazz)) {
            return processString((String) obj, nullToEmpty);
        }

        // 处理集合
        if (Collection.class.isAssignableFrom(clazz)) {
            ((Collection<?>) obj).forEach(item -> processObject(item, nullToEmpty));
            return obj;
        }

        // 处理数组
        if (clazz.isArray()) {
            Object[] array = (Object[]) obj;
            for (int i = 0; i < array.length; i++) {
                array[i] = processObject(array[i], nullToEmpty);
            }
            return obj;
        }

        // 处理对象字段
        Field[] fields = ReflectUtil.getFields(clazz);
        for (Field field : fields) {
            if (!String.class.isAssignableFrom(field.getType())) {
                continue;
            }
            try {
                field.setAccessible(true);
                Object fieldValue = field.get(obj);
                if (fieldValue instanceof String) {
                    String processed = processString((String) fieldValue, nullToEmpty);
                    if (!Objects.equals(processed, fieldValue)) {
                        field.set(obj, processed);
                    }
                }
            } catch (Exception e) {
                log.warn("Process field [{}] failed", field.getName(), e);
            }
        }
        return obj;
    }

    /**
     * 处理字符串
     *
     * @param str 字符串
     * @param nullToEmpty 是否将null转为空字符串
     * @return 处理后的字符串
     */
    private String processString(String str, boolean nullToEmpty) {
        if (str == null) {
            return nullToEmpty ? "" : null;
        }
        return StringUtils.trim(str);
    }
} 