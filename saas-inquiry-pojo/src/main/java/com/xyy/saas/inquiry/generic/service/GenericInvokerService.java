package com.xyy.saas.inquiry.generic.service;

import com.xyy.saas.inquiry.generic.model.GenericInvokeRequest;
import com.xyy.saas.inquiry.generic.model.GenericInvokeResponse;
import java.util.concurrent.CompletableFuture;

/**
 * Dubbo 泛化调用服务接口
 *
 * <AUTHOR>
 */
public interface GenericInvokerService {

    /**
     * 执行泛化调用
     *
     * @param request 调用请求参数
     * @return 调用响应结果
     */
    GenericInvokeResponse invoke(GenericInvokeRequest request);

    /**
     * 异步执行泛化调用
     *
     * @param request 调用请求参数
     * @return 调用响应结果的Future
     */
    CompletableFuture<GenericInvokeResponse> invokeAsync(GenericInvokeRequest request);

    /**
     * 简化的泛化调用
     *
     * @param interfaceName 接口名
     * @param methodName 方法名
     * @param parameterTypes 参数类型
     * @param args 参数值
     * @return 调用响应结果
     */
    GenericInvokeResponse invoke(String interfaceName, String methodName, String[] parameterTypes, Object[] args);
}