package com.xyy.saas.inquiry.generic.exception;

import java.io.Serial;

/**
 * Dubbo 泛化调用异常
 * 
 * <AUTHOR>
 */
public class GenericInvokerException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = 1L;

    public GenericInvokerException(String message) {
        super(message);
    }

    public GenericInvokerException(String message, Throwable cause) {
        super(message, cause);
    }

    public GenericInvokerException(Throwable cause) {
        super(cause);
    }
}