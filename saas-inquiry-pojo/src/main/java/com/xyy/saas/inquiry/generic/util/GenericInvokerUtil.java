package com.xyy.saas.inquiry.generic.util;

import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.xyy.saas.inquiry.generic.model.GenericInvokeRequest;
import com.xyy.saas.inquiry.generic.model.GenericInvokeResponse;
import com.xyy.saas.inquiry.generic.service.GenericInvokerService;
import java.text.SimpleDateFormat;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;

/**
 * Dubbo 泛化调用工具类
 * 
 * <AUTHOR>
 */
@Slf4j
public class GenericInvokerUtil {

    private static GenericInvokerService genericInvokerService;
    private static ObjectMapper objectMapper;

    public static void setGenericInvokerService(GenericInvokerService genericInvokerService) {
        GenericInvokerUtil.genericInvokerService = genericInvokerService;

        objectMapper = new ObjectMapper();
        // 配置反序列化选项
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
        objectMapper.configure(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL, true);

        // 忽略循环引用
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNRESOLVED_OBJECT_IDS, false);

        // 注册Java 8日期时间模块
        objectMapper.registerModule(new JavaTimeModule());

        // 禁用日期转时间戳（使用ISO-8601格式）
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);

        // 配置序列化选项
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    /**
     * 静态方法调用
     */
    public static GenericInvokeResponse invoke(String interfaceName, String methodName, String[] parameterTypes, Object[] args) {
        return genericInvokerService.invoke(interfaceName, methodName, parameterTypes, args);
    }

    /**
     * 静态方法调用（带版本）
     */
    public static GenericInvokeResponse invoke(String interfaceName, String methodName, String[] parameterTypes, Object[] args, String version) {
        GenericInvokeRequest request = GenericInvokeRequest.builder()
                .interfaceName(interfaceName)
                .methodName(methodName)
                .parameterTypes(parameterTypes)
                .args(args)
                .version(version)
                .build();
        return genericInvokerService.invoke(request);
    }

    /**
     * 静态方法调用（带版本和分组）
     */
    public static GenericInvokeResponse invoke(String interfaceName, String methodName, String[] parameterTypes, Object[] args, String version, String group) {
        GenericInvokeRequest request = GenericInvokeRequest.builder()
                .interfaceName(interfaceName)
                .methodName(methodName)
                .parameterTypes(parameterTypes)
                .args(args)
                .version(version)
                .group(group)
                .build();
        return genericInvokerService.invoke(request);
    }

    /**
     * 静态异步调用
     */
    public static CompletableFuture<GenericInvokeResponse> invokeAsync(String interfaceName, String methodName, String[] parameterTypes, Object[] args) {
        GenericInvokeRequest request = GenericInvokeRequest.builder()
                .interfaceName(interfaceName)
                .methodName(methodName)
                .parameterTypes(parameterTypes)
                .args(args)
                .build();
        return genericInvokerService.invokeAsync(request);
    }

    /**
     * 无参数调用
     */
    public static GenericInvokeResponse invoke(String interfaceName, String methodName) {
        return invoke(interfaceName, methodName, new String[0], new Object[0]);
    }

    /**
     * 单参数调用
     */
    public static GenericInvokeResponse invoke(String interfaceName, String methodName, String parameterType, Object arg) {
        return invoke(interfaceName, methodName, new String[]{parameterType}, new Object[]{arg});
    }

    /**
     * 获取调用结果并转换类型（不支持泛型）
     */
    public static <T> T getResult(GenericInvokeResponse response, Class<T> resultType) {
        if (!response.isSuccess()) {
            throw new RuntimeException("调用失败: " + response.getErrorMessage());
        }
        
        Object result = response.getResult();
        if (result == null) {
            return null;
        }

        // 如果结果类型与期望类型匹配，直接返回
        if (resultType.isInstance(result)) {
            @SuppressWarnings("unchecked")
            T castedResult = (T) result;
            return castedResult;
        }
        
        // 如果类型不匹配，可以尝试JSON转换等其他转换方式
        if (result instanceof String stringResult) {
            // 如果结果是字符串，尝试解析为目标类型
            return JsonUtils.parseObject(stringResult, resultType);
        }

        throw new ClassCastException(
                String.format("结果类型不匹配，期望: %s, 实际: %s", resultType.getSimpleName(), result.getClass().getSimpleName()));
    }

    /**
     * 获取调用结果并转换类型（泛型支持）
     */
    public static <T> T getResult(GenericInvokeResponse response, TypeReference<T> typeReference) {
        if (!response.isSuccess()) {
            throw new RuntimeException("调用失败: " + response.getErrorMessage());
        }

        Object result = response.getResult();
        if (result == null) {
            return null;
        }

        return objectMapper.convertValue(result, typeReference);
    }
}