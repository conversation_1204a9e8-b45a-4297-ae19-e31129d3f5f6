# Dubbo 泛化调用使用说明

## 概述

本模块在 `saas-inquiry-pojo` 中实现了 Dubbo 泛化调用功能，其他模块只需依赖 pojo 模块即可使用泛化调用，无需直接依赖 Dubbo API。

## 特性

- ✅ 封装了 Dubbo GenericService，提供简化的泛化调用接口
- ✅ 支持同步和异步调用
- ✅ 集成现有的租户上下文和链路追踪
- ✅ 完善的异常处理机制
- ✅ Spring Boot 自动配置，开箱即用
- ✅ 提供便捷的静态工具类
- ✅ 支持服务缓存，提高调用性能

## 配置

在 `application.yml` 中添加以下配置：

```yaml
# Dubbo 基础配置（必须）
dubbo:
  application:
    name: your-app-name
  registry:
    address: nacos://127.0.0.1:8848
    protocol: nacos

# 泛化调用配置（可选）
inquiry:
  generic:
    enabled: true        # 是否启用，默认 true
    timeout: 5000        # 默认超时时间，默认 5000ms
    retries: 0           # 重试次数，默认 0
    max-cache-size: 100  # 服务缓存最大数量，默认 100
    async-enabled: true  # 是否启用异步调用，默认 true
```

## 使用方式

### 方式一：注入服务使用

```java
@Autowired
private GenericInvokerService genericInvokerService;

public void callService() {
    GenericInvokeRequest request = GenericInvokeRequest.builder()
        .interfaceName("com.xyy.saas.inquiry.user.api.UserService")
        .methodName("getUserById")
        .parameterTypes(new String[]{"java.lang.Long"})
        .args(new Object[]{123L})
        .version("1.0.0")
        .timeout(3000)
        .build();

    GenericInvokeResponse response = genericInvokerService.invoke(request);
    
    if (response.isSuccess()) {
        System.out.println("调用成功：" + response.getResult());
    } else {
        System.err.println("调用失败：" + response.getErrorMessage());
    }
}
```

### 方式二：静态工具类使用（推荐）

```java
// 基础调用
GenericInvokeResponse response = GenericInvokerUtil.invoke(
    "com.xyy.saas.inquiry.user.api.UserService",
    "getUserById",
    new String[]{"java.lang.Long"},
    new Object[]{123L}
);

// 带版本的调用
GenericInvokeResponse response = GenericInvokerUtil.invoke(
    "com.xyy.saas.inquiry.user.api.UserService",
    "getUserById",
    new String[]{"java.lang.Long"},
    new Object[]{123L},
    "1.0.0"
);

// 无参数调用
GenericInvokeResponse response = GenericInvokerUtil.invoke(
    "com.xyy.saas.inquiry.user.api.UserService",
    "getAllUsers"
);

// 单参数调用
GenericInvokeResponse response = GenericInvokerUtil.invoke(
    "com.xyy.saas.inquiry.user.api.UserService",
    "getUserByName",
    "java.lang.String",
    "张三"
);
```

### 方式三：异步调用

```java
// 异步调用
CompletableFuture<GenericInvokeResponse> future = GenericInvokerUtil.invokeAsync(
    "com.xyy.saas.inquiry.user.api.UserService",
    "getUserById",
    new String[]{"java.lang.Long"},
    new Object[]{123L}
);

// 处理异步结果
future.thenAccept(response -> {
    if (response.isSuccess()) {
        System.out.println("异步调用成功：" + response.getResult());
    } else {
        System.err.println("异步调用失败：" + response.getErrorMessage());
    }
}).exceptionally(throwable -> {
    System.err.println("异步调用异常：" + throwable.getMessage());
    return null;
});
```

## 类型转换

```java
// 获取结果并转换类型
GenericInvokeResponse response = GenericInvokerUtil.invoke(...);
if (response.isSuccess()) {
    // 方式1：直接转换
    User user = GenericInvokerUtil.getResult(response, User.class);
    
    // 方式2：手动处理
    Object result = response.getResult();
    // 根据需要进行类型转换
}
```

## 异常处理

```java
try {
    GenericInvokeResponse response = GenericInvokerUtil.invoke(...);
    if (!response.isSuccess()) {
        // 处理业务异常
        log.error("调用失败：{}", response.getErrorMessage());
        // 可以根据 errorType 进行不同的处理
    }
} catch (GenericInvokerException e) {
    // 处理泛化调用框架异常
    log.error("泛化调用异常", e);
}
```

## 注意事项

1. **参数类型**：参数类型必须使用完整的类名，如 `java.lang.String`、`java.lang.Long` 等
2. **服务发现**：确保 Dubbo 注册中心配置正确，服务提供者已注册
3. **版本匹配**：如果服务有版本号，需要指定正确的版本
4. **超时配置**：根据实际服务的响应时间合理设置超时时间
5. **异常处理**：始终检查调用结果的 success 状态
6. **性能考虑**：服务引用会被缓存，首次调用可能较慢

## 集成特性

- **租户上下文**：自动传递租户信息（通过现有的 DubboTenantFilter）
- **链路追踪**：自动传递 TraceId（通过现有的 DubboTenantFilter）
- **异常过滤**：统一的异常处理（通过现有的 DubboExceptionFilter）

## 依赖说明

其他模块使用时，只需在 `pom.xml` 中添加：

```xml
<dependency>
    <groupId>com.xyy.saas</groupId>
    <artifactId>saas-inquiry-pojo</artifactId>
    <version>${revision}</version>
</dependency>
```

无需额外添加 Dubbo 相关依赖。