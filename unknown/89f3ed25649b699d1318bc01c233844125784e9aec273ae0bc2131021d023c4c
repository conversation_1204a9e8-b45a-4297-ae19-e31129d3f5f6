package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack;


import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.pojo.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "管理后台 - 门店-开通服务包关系 Response VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TenantServicePackRelationRespVO extends BaseDto {

    private Long tenantId;

    @Schema(description = "门店编码", example = "MD100001")
    @ExcelProperty("门店编码")
    private String pref;

    @Schema(description = "门店名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("门店名")
    private String name;

    @Schema(description = "省", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("省")
    private String province;

    @Schema(description = "省", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("省code")
    private String provinceCode;

    @Schema(description = "市", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("市")
    private String city;

    @Schema(description = "市", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("市code")
    private String cityCode;

    @Schema(description = "区", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("区")
    private String area;

    @Schema(description = "区", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("区code")
    private String areaCode;

    @Schema(description = "医保")
    private Integer medicalInsuranceStatus;

    @Schema(description = "药监")
    private Integer drugSupervisionStatus;

    @Schema(description = "互联网医院监管")
    private Integer internetHospitalStatus;

    @Schema(description = "erp")
    private Integer erpStatus;

    @Schema(description = "his")
    private Integer hisStatus;

    @Schema(description = "服务包名称")
    private List<String> servicePackNames;

}