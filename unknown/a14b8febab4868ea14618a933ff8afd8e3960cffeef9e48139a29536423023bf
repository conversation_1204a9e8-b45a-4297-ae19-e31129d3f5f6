package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentRelationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentRelationRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentRelationSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDepartmentRelationDO;
import com.xyy.saas.inquiry.hospital.server.service.hospital.InquiryHospitalDepartmentRelationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 医院科室信息")
@RestController
@RequestMapping("/hospital/inquiry-hospital-department-relation")
@Validated
public class InquiryHospitalDepartmentRelationController {

    @Resource
    private InquiryHospitalDepartmentRelationService inquiryHospitalDepartmentRelationService;

    @PostMapping("/create")
    @Operation(summary = "创建医院科室信息")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-hospital-department-relation:create')")
    public CommonResult<Long> createInquiryHospitalDepartmentRelation(@Valid @RequestBody InquiryHospitalDepartmentRelationSaveReqVO createReqVO) {
        return success(inquiryHospitalDepartmentRelationService.createInquiryHospitalDepartmentRelation(createReqVO));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除医院科室信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-hospital-department-relation:delete')")
    public CommonResult<Boolean> deleteInquiryHospitalDepartmentRelation(@RequestParam("id") Long id) {
        inquiryHospitalDepartmentRelationService.deleteInquiryHospitalDepartmentRelation(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得医院科室信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-hospital-department-relation:query')")
    public CommonResult<InquiryHospitalDepartmentRelationRespVO> getInquiryHospitalDepartmentRelation(@RequestParam("id") Long id) {
        InquiryHospitalDepartmentRelationDO inquiryHospitalDepartmentRelation = inquiryHospitalDepartmentRelationService.getInquiryHospitalDepartmentRelation(id);
        return success(BeanUtils.toBean(inquiryHospitalDepartmentRelation, InquiryHospitalDepartmentRelationRespVO.class));
    }

    @GetMapping("/getByHospitalId")
    @Operation(summary = "根据医院ID查询医院科室信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-hospital-department-relation:query')")
    public CommonResult<List<Long>> getByHospitalId(@RequestParam("hospitalId") Long hospitalId) {
        return success(inquiryHospitalDepartmentRelationService.getDeptIdByHospitalId(hospitalId));
    }

    @GetMapping("/getByHospitalPref")
    @Operation(summary = "根据医院编码查询医院科室信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-hospital-department-relation:query')")
    public CommonResult<List<InquiryHospitalDepartmentRelationRespVO>> getByHospitalPref(@RequestParam("hospitalPref") String hospitalPref) {
        return success(inquiryHospitalDepartmentRelationService.getDeptByHospitalPref(hospitalPref));
    }


    @PostMapping("/editHospitalDept")
    @Operation(summary = "医院科室维护")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-hospital-department-relation:create')")
    public CommonResult<Boolean> editHospitalDept(@Valid @RequestBody InquiryHospitalDepartmentRelationSaveReqVO createReqVO) {
        return success(inquiryHospitalDepartmentRelationService.editHospitalDept(createReqVO));
    }

    @GetMapping("/page")
    @Operation(summary = "获得医院科室信息分页")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-hospital-department-relation:query')")
    public CommonResult<PageResult<InquiryHospitalDepartmentRelationRespVO>> getInquiryHospitalDepartmentRelationPage(@Valid InquiryHospitalDepartmentRelationPageReqVO pageReqVO) {
        PageResult<InquiryHospitalDepartmentRelationDO> pageResult = inquiryHospitalDepartmentRelationService.getInquiryHospitalDepartmentRelationPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InquiryHospitalDepartmentRelationRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出医院科室信息 Excel")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-hospital-department-relation:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInquiryHospitalDepartmentRelationExcel(@Valid InquiryHospitalDepartmentRelationPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InquiryHospitalDepartmentRelationDO> list = inquiryHospitalDepartmentRelationService.getInquiryHospitalDepartmentRelationPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "医院科室信息.xls", "数据", InquiryHospitalDepartmentRelationRespVO.class,
            BeanUtils.toBean(list, InquiryHospitalDepartmentRelationRespVO.class));
    }

}