package com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Schema(description = "管理后台 - 医生问诊评价 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DoctorReviewsRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "4994")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "问诊单pref", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("问诊单pref")
    private String inquiryPref;

    @Schema(description = "处方编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("处方编号")
    private String prescriptionPref;

    @Schema(description = "医师编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("医师编码")
    private String doctorPref;

    @Schema(description = "满意度评分", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("满意度评分")
    private BigDecimal satisfactionScore;

    @Schema(description = "满意的点", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("满意的点")
    private List<String> satisfactionItem;

    @Schema(description = "评论内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("评论内容")
    private String reviewsContent;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}