package com.xyy.saas.inquiry.hospital.server.doctor;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.xyy.saas.inquiry.hospital.server.BaseIntegrationTest;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorAuditedRecordPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorAuditedRecordSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorAuditedRecordDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.DoctorAuditedRecordMapper;
import com.xyy.saas.inquiry.hospital.server.service.doctor.DoctorAuditedRecordServiceImpl;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.DOCTOR_AUDITED_RECORD_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link DoctorAuditedRecordServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(DoctorAuditedRecordServiceImpl.class)
public class DoctorAuditedRecordServiceImplTest extends BaseIntegrationTest {

    @Resource
    private DoctorAuditedRecordServiceImpl doctorAuditedRecordService;

    @Resource
    private DoctorAuditedRecordMapper doctorAuditedRecordMapper;


    @BeforeEach
    public void before() {
        RequestAttributes requestAttributes = new ServletRequestAttributes(new MockHttpServletRequest());
        requestAttributes.setAttribute("login_user_id", 0L, RequestAttributes.SCOPE_REQUEST);
        RequestContextHolder.setRequestAttributes(requestAttributes);
//        TenantContextHolder.setTenantId();
    }

    @Test
    public void testCreateDoctorAuditedRecord_success() {
        // 准备参数
        DoctorAuditedRecordSaveReqVO createReqVO = randomPojo(DoctorAuditedRecordSaveReqVO.class);
        // 调用
        Long doctorAuditedRecordId = doctorAuditedRecordService.createDoctorAuditedRecord(createReqVO);
        // 断言
        assertNotNull(doctorAuditedRecordId);
        // 校验记录的属性是否正确
        DoctorAuditedRecordDO doctorAuditedRecord = doctorAuditedRecordMapper.selectById(doctorAuditedRecordId);
        assertPojoEquals(createReqVO, doctorAuditedRecord, "id");
    }

    @Test
    public void testUpdateDoctorAuditedRecord_success() {
        // mock 数据
        DoctorAuditedRecordDO dbDoctorAuditedRecord = randomPojo(DoctorAuditedRecordDO.class);
        doctorAuditedRecordMapper.insert(dbDoctorAuditedRecord);// @Sql: 先插入出一条存在的数据
        // 准备参数
        DoctorAuditedRecordSaveReqVO updateReqVO = randomPojo(DoctorAuditedRecordSaveReqVO.class, o -> {
        });

        // 调用
        doctorAuditedRecordService.updateDoctorAuditedRecord(updateReqVO);
        // 校验是否更新正确
    }

    @Test
    public void testUpdateDoctorAuditedRecord_notExists() {
        // 准备参数
        DoctorAuditedRecordSaveReqVO updateReqVO = randomPojo(DoctorAuditedRecordSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> doctorAuditedRecordService.updateDoctorAuditedRecord(updateReqVO), DOCTOR_AUDITED_RECORD_NOT_EXISTS);
    }

    @Test
    public void testDeleteDoctorAuditedRecord_success() {
        // mock 数据
        DoctorAuditedRecordDO dbDoctorAuditedRecord = randomPojo(DoctorAuditedRecordDO.class);
        doctorAuditedRecordMapper.insert(dbDoctorAuditedRecord);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbDoctorAuditedRecord.getId();

        // 调用
        doctorAuditedRecordService.deleteDoctorAuditedRecord(id);
        // 校验数据不存在了
        assertNull(doctorAuditedRecordMapper.selectById(id));
    }

    @Test
    public void testDeleteDoctorAuditedRecord_notExists() {
        // 准备参数

        // 调用, 并断言异常
//        assertServiceException(() -> doctorAuditedRecordService.deleteDoctorAuditedRecord(id), DOCTOR_AUDITED_RECORD_NOT_EXISTS);
    }

    @Test
    public void testGetDoctorAuditedRecordPage() {
        // mock 数据
        DoctorAuditedRecordDO dbDoctorAuditedRecord = randomPojo(DoctorAuditedRecordDO.class, o -> { // 等会查询到
            o.setAuditorName("测试");
//            o.setAuditorId("10086");
//            o.setAuditType(2);
//            o.setAuditStatus(1);
            o.setAuditTime(LocalDateTime.now());
            o.setCreateTime(LocalDateTime.now());
        });
        doctorAuditedRecordMapper.insert(dbDoctorAuditedRecord);
        // 测试 guid 不匹配
        // 测试 auditorName 不匹配
        doctorAuditedRecordMapper.insert(cloneIgnoreId(dbDoctorAuditedRecord, o -> o.setAuditorName(null)));
        // 测试 auditorId 不匹配
        doctorAuditedRecordMapper.insert(cloneIgnoreId(dbDoctorAuditedRecord, o -> o.setAuditorId(null)));
        // 测试 auditType 不匹配
//        doctorAuditedRecordMapper.insert(cloneIgnoreId(dbDoctorAuditedRecord, o -> o.setAuditType(3)));
        // 测试 auditStatus 不匹配
//        doctorAuditedRecordMapper.insert(cloneIgnoreId(dbDoctorAuditedRecord, o -> o.setAuditStatus(2)));
        // 测试 auditTime 不匹配
        doctorAuditedRecordMapper.insert(cloneIgnoreId(dbDoctorAuditedRecord, o -> o.setAuditTime(null)));
        // 测试 createTime 不匹配
        doctorAuditedRecordMapper.insert(cloneIgnoreId(dbDoctorAuditedRecord, o -> o.setCreateTime(null)));
        // 准备参数
        DoctorAuditedRecordPageReqVO reqVO = new DoctorAuditedRecordPageReqVO();
        reqVO.setAuditType(2);

        // 调用
        PageResult<DoctorAuditedRecordDO> pageResult = doctorAuditedRecordService.getDoctorAuditedRecordPage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        // assertPojoEquals(dbDoctorAuditedRecord, pageResult.getList().get(0));
    }

}