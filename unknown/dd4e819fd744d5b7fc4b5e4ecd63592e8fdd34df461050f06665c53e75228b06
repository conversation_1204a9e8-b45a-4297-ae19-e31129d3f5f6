package cn.iocoder.yudao.module.system.controller.admin.auth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

@Schema(description = "管理后台 - 三方授权登录 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class AuthLoginApiReqVO {


    @Schema(description = "应用标识", example = "erp_key")
    @NotEmpty(message = "应用标识不能为空")
    private String appKey;
    @Schema(description = "时间戳", example = "123456789")
    @NotNull(message = "时间戳不能为空")
    private Long timestamp;
    @Schema(description = "随机字符串", example = "abc")
    @Length(min = 8, max = 36, message = "随机字符串长度为 8-36 位")
    private String nonce;
    @Schema(description = "签名", example = "aaa")
    @NotEmpty(message = "签名不能为空")
    private String sign;


}