package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 科室诊断关联 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InquiryDiagnosisDepartmentRelationRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "9003")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "诊断编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("诊断编码")
    private String diagnosisCode;

    @Schema(description = "诊断名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("诊断名称")
    private String diagnosisName;

    @Schema(description = "展示诊断名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("展示诊断名称")
    private String showName;

    @Schema(description = "医院科室id", requiredMode = Schema.RequiredMode.REQUIRED, example = "18581")
    @ExcelProperty("医院科室id")
    private Long deptId;

    @Schema(description = "科室编码,eg:101", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("科室编码,eg:101")
    private String deptPref;

    @Schema(description = "科室名称,eg:内科", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("科室名称,eg:内科")
    private String deptName;

    @Schema(description = "是否禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否禁用")
    private Boolean disable;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}