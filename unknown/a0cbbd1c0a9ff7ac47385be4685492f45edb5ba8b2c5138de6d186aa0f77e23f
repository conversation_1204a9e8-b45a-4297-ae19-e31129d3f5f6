package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 科室字典分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InquiryHospitalDepartmentPageReqVO extends PageParam {

    @Schema(description = "科室编码,eg:101")
    private String pref;

    @Schema(description = "科室名称,eg:内科", example = "赵六")
    private String deptName;

    @Schema(description = "父级科室id", example = "5502")
    private Integer deptParentId;


    @Schema(description = "科室序号")
    private Integer deptOrder;

    @Schema(description = "当前科室状态 0 启用 1 禁用", example = "2")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}