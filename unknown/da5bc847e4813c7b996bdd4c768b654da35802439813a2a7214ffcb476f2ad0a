package com.xyy.saas.inquiry.annotation;

import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * @Author: xucao
 * @Date: 2025/01/24 16:59
 * @Description: LocalDateTime 序列化
 */
public class LocalDateTimeDeserializer  extends JsonDeserializer<LocalDateTime> {
    @Override
    public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException , JacksonException{
        long timestampInSeconds = p.getLongValue();
        // Convert seconds to milliseconds
        long timestampInMillis = timestampInSeconds * 1000L;
        // Convert milliseconds to Instant
        Instant instant = Instant.ofEpochMilli(timestampInMillis);
        // Convert Instant to LocalDateTime in the system default time zone
        return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }

}
