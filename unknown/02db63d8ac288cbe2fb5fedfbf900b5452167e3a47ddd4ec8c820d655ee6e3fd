package cn.iocoder.yudao.module.system.controller.admin.oa;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.system.controller.admin.oa.vo.OaWhiteListPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.oa.vo.OaWhiteListRespVO;
import cn.iocoder.yudao.module.system.controller.admin.oa.vo.OaWhiteListSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.oa.OaWhiteListDO;
import cn.iocoder.yudao.module.system.service.oa.OaWhiteListService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - OA用户白名单")
@RestController
@RequestMapping("/system/oa-white-list")
@Validated
public class OaWhiteListController {

    @Resource
    private OaWhiteListService oaWhiteListService;

    @PostMapping("/create")
    @Operation(summary = "创建OA用户白名单")
    @PreAuthorize("@ss.hasPermission('saas:oa-white-list:create')")
    public CommonResult<Long> createOaWhiteList(@Valid @RequestBody OaWhiteListSaveReqVO createReqVO) {
        return success(oaWhiteListService.createOaWhiteList(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新OA用户白名单")
    @PreAuthorize("@ss.hasPermission('saas:oa-white-list:update')")
    public CommonResult<Boolean> updateOaWhiteList(@Valid @RequestBody OaWhiteListSaveReqVO updateReqVO) {
        oaWhiteListService.updateOaWhiteList(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除OA用户白名单")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:oa-white-list:delete')")
    public CommonResult<Boolean> deleteOaWhiteList(@RequestParam("id") Long id) {
        oaWhiteListService.deleteOaWhiteList(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得OA用户白名单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:oa-white-list:query')")
    public CommonResult<OaWhiteListRespVO> getOaWhiteList(@RequestParam("id") Long id) {
        OaWhiteListDO oaWhiteList = oaWhiteListService.getOaWhiteList(id);
        return success(BeanUtils.toBean(oaWhiteList, OaWhiteListRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得OA用户白名单分页")
    @PreAuthorize("@ss.hasPermission('saas:oa-white-list:query')")
    public CommonResult<PageResult<OaWhiteListRespVO>> getOaWhiteListPage(@Valid OaWhiteListPageReqVO pageReqVO) {
        PageResult<OaWhiteListDO> pageResult = oaWhiteListService.getOaWhiteListPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, OaWhiteListRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出OA用户白名单 Excel")
    @PreAuthorize("@ss.hasPermission('saas:oa-white-list:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportOaWhiteListExcel(@Valid OaWhiteListPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<OaWhiteListDO> list = oaWhiteListService.getOaWhiteListPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "OA用户白名单.xls", "数据", OaWhiteListRespVO.class,
            BeanUtils.toBean(list, OaWhiteListRespVO.class));
    }

}