package com.xyy.saas.inquiry.user.server.dal.dataobject.tenant;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 门店三方应用配置 DO
 */
@TableName("saas_tenant_third_app")
@KeySequence("saas_tenant_third_app_seq") 
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TenantThirdAppDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 租户编号
     */
    private Long tenantId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 应用标识（AK）
     */
    private String appKey;

    /**
     * 应用密钥（SK）
     */
    private String appSecret;

    /**
     * ERP对接机构id
     */
    private Integer transmissionOrganId;

    /**
     * 状态：0启用，1禁用
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否删除
     */
    @TableLogic
    private Boolean deleted;

} 