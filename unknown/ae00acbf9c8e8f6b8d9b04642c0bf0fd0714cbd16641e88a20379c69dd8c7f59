package com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 医生问诊评价分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DoctorReviewsPageReqVO extends PageParam {

    @Schema(description = "问诊单pref")
    private String inquiryPref;

    @Schema(description = "处方编号")
    private String prescriptionPref;

    @Schema(description = "医师编码")
    private String doctorPref;

    @Schema(description = "满意度评分")
    private BigDecimal satisfactionScore;

    @Schema(description = "满意的点")
    private String satisfactionItem;

    @Schema(description = "评论内容")
    private String reviewsContent;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}