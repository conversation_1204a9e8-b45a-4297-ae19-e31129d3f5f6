<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.InquiryHospitalDeptDoctorMapper">

  <!--
      一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
      无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
      代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
      文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
   -->
  <delete id="deleteByDoctorPref" parameterType="java.lang.String">
    DELETE
    FROM saas_inquiry_hospital_dept_doctor_relation
    WHERE doctor_pref = #{doctorPref}
  </delete>

  <delete id="deleteByIds">
    DELETE FROM saas_inquiry_hospital_dept_doctor_relation WHERE id in
    <foreach collection="ids" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
  </delete>

  <delete id="deleteById">
    DELETE
    FROM saas_inquiry_hospital_dept_doctor_relation
    WHERE id = #{id}
  </delete>
  <select id="selectDoctorHospitalList" resultType="java.lang.String">
    select distinct hospital_pref
    from saas_inquiry_hospital_dept_doctor_relation
    where doctor_pref = #{doctorPref}
  </select>

  <delete id="deleteByHospitalDoctorDept">
    DELETE FROM saas_inquiry_hospital_dept_doctor_relation
    WHERE
    doctor_pref = #{doctorPref}
    <if test="hospitalPref != null and hospitalPref != ''">
      and hospital_pref = #{hospitalPref}
    </if>
    <if test="deptPrefs != null and deptPrefs.size() > 0">
      AND dept_pref IN
      <foreach collection="deptPrefs" open="(" item="deptPref" separator="," close=")">
        #{deptPref}
      </foreach>
    </if>
  </delete>

</mapper>