<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.hospital.server.dal.mysql.prescription.InquiryPrescriptionMapper">

  <!--
      一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
      无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
      代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
      文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
   -->


  <update id="distributePharmacist">
    update saas_inquiry_prescription
    set distribute_status  = ${@<EMAIL>()},
        distribute_user_id = #{userId}
    where id = #{id}
      and status = #{status}
      and distribute_status = ${@<EMAIL>()}
      and distribute_user_id is null
  </update>

  <update id="releasePharmacist">
    update saas_inquiry_prescription
    set distribute_status  = ${@<EMAIL>()},
        distribute_user_id = null
    where id = #{id}
      and status = #{status}
      and distribute_status = ${@<EMAIL>()}
      and distribute_user_id = #{userId}
  </update>

  <select id="getPrescriptionPatientList" resultType="com.xyy.saas.inquiry.pojo.patient.PatientSimpleDTO">
    SELECT DISTINCT patient_pref as pref, patient_name as name
    FROM saas_inquiry_prescription
    WHERE pharmacist_pref = #{reqVo.pharmacistPref}
    AND audit_prescription_time BETWEEN #{reqVo.startTime} AND #{reqVo.endTime}
    <if test="reqVo.patientName != null and reqVo.patientName != ''">
      AND patient_name LIKE CONCAT('%', #{reqVo.patientName}, '%')
    </if>
    <if test="reqVo.tenantId != null">
      AND tenant_id = #{reqVo.tenantId}
    </if>
    ORDER BY pref desc
  </select>
</mapper>