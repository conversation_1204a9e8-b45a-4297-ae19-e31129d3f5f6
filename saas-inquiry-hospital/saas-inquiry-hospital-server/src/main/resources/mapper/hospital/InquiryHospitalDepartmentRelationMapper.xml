<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.hospital.server.dal.mysql.hospital.InquiryHospitalDepartmentRelationMapper">

  <!--
      一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
      无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
      代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
      文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
   -->
  <delete id="deleteByHospitalId" parameterType="java.lang.Long">
    DELETE
    FROM saas_inquiry_hospital_department_relation
    WHERE hospital_id = #{hospitalId}
  </delete>

  <select id="selectHospitalDeptPage" parameterType="com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentRelationPageReqVO"
    resultType="com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDeptDoctorGroupVO">
    SELECT
    r.id AS id,
    r.hospital_pref AS hospitalPref,
    r.hospital_name AS hospitalName,
    r.dept_pref AS deptPref,
    r.dept_name AS deptName,
    r.disabled AS disabled,
    COUNT(DISTINCT d.doctor_pref) AS doctorCount
    FROM saas_inquiry_hospital_department_relation r
    LEFT JOIN saas_inquiry_hospital_dept_doctor_relation d ON r.id = d.hospital_dept_relation_id and d.deleted = 0
    where r.deleted = 0
    <if test="pageReqVO.hospitalPref != null">
      and r.hospital_pref = #{pageReqVO.hospitalPref}
    </if>
    <if test="pageReqVO.deptPref != null">
      and r.dept_pref = #{pageReqVO.deptPref}
    </if>
    group by r.hospital_pref,r.dept_pref
    order by doctorCount desc
  </select>
</mapper>