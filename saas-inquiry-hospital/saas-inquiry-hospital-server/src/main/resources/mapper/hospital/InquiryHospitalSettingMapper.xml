<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.hospital.server.dal.mysql.hospital.InquiryHospitalSettingMapper">

  <!--
      一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
      无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
      代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
      文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
   -->

  <insert id="insertOrUpdateBatch" parameterType="com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalSettingDO">
    INSERT INTO saas_inquiry_hospital_setting (hospital_id, param_type, param_name, param_value, description, ext, deleted)
    VALUES
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.hospitalId}, #{item.paramType}, #{item.paramName}, #{item.paramValue}, #{item.description}, #{item.ext}, #{item.deleted})
    </foreach>
    ON DUPLICATE KEY UPDATE
    param_name = values(param_name),
    param_value = values(param_value),
    description = values(description),
    ext = values(ext),
    deleted = values(deleted)
  </insert>

  <select id="selectPresTempUsedHospitalIdList" resultType="java.lang.Long">
    SELECT DISTINCT hospital_id
    FROM saas_inquiry_hospital_setting
    WHERE deleted = 0
      AND param_name IN
      <foreach collection="@com.xyy.saas.inquiry.hospital.enums.HospitalSettingTypeEnum@defaultPrescriptionTemplateField()" item="item" index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>
      AND param_value IN
      <foreach collection="presTempIdList" item="item" index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>
    UNION ALL
    SELECT DISTINCT hospital_id
    FROM saas_inquiry_hospital_setting
    WHERE deleted = 0
      AND param_name IN
      <foreach collection="@com.xyy.saas.inquiry.hospital.enums.HospitalSettingTypeEnum@specialPrescriptionTemplateField()" item="item" index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>
      AND
      <foreach collection="presTempIdList" item="item" index="index" open="(" close=")" separator=" OR ">
        JSON_CONTAINS(ext -> '$.specificPrescriptionTemplates[*].prescriptionTemplateId', CAST(#{item} AS CHAR) )
      </foreach>
  </select>

</mapper>