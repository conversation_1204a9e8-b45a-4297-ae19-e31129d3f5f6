<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.hospital.server.dal.mysql.diagnosis.InquiryDiagnosisDepartmentRelationMapper">

  <!--
      一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
      无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
      代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
      文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
   -->
  <insert id="insertOrUpdateBatch" parameterType="com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalSettingDO">
    INSERT INTO saas_inquiry_diagnosis_department_relation (diagnosis_code, diagnosis_name, dept_id, dept_pref, dept_name)
    VALUES
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.diagnosisCode}, #{item.diagnosisName}, #{item.deptId}, #{item.deptPref}, #{item.deptName})
    </foreach>
    ON DUPLICATE KEY UPDATE
    diagnosis_code = values(diagnosis_code),
    dept_pref = values(dept_pref)
  </insert>
</mapper>