package com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;
import lombok.Data;

@Schema(description = "管理后台 - 科室诊断关联新增/修改 Request VO")
@Data
public class InquiryDiagnosisSimpleVO implements Serializable {


    @Schema(description = "诊断编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "诊断编码不能为空")
    private String diagnosisCode;

    @Schema(description = "诊断名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "诊断名称不能为空")
    private String diagnosisName;


}