package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisSimpleVO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import lombok.Data;

@Schema(description = "管理后台 - 科室诊断关联新增/修改 Request VO")
@Data
public class InquiryDiagnosisDepartmentRelationSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "9003")
    private Long id;


    @Schema(description = "科室编码,eg:101", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "科室编码,eg:101不能为空")
    private String deptPref;


    @Schema(description = "诊断信息")
    @NotEmpty(message = "诊断信息不可为空")
    private List<InquiryDiagnosisSimpleVO> diagnosisVOList;


}