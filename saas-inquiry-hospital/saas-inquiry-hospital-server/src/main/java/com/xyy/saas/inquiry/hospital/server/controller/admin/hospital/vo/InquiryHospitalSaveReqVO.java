package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Schema(description = "管理后台 - 医院信息新增/修改 Request VO")
@Data
public class InquiryHospitalSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "9640")
    private Long id;

    @Schema(description = "医院名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "医院名称不能为空")
    @Size(max = 256, message = "医院名称长度不能超过256个字符")
    private String name;

    @Schema(description = "医疗机构编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "H404040")
    @Size(max = 32, message = "医疗机构编码长度不能超过32个字符")
    private String institutionCode;

    @Schema(description = "医院等级", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "医院等级不能为空")
    private Integer level;

    @Schema(description = "医院地址")
    @Size(max = 512, message = "医院地址长度不能超过256个字符")
    private String address;

    @Schema(description = "联系电话")
    @Size(max = 32, message = "联系电话长度不能超过32个字符")
    private String phone;

    @Schema(description = "电子邮件")
    @Size(max = 64, message = "电子邮件长度不能超过64个字符")
    private String email;

    @Schema(description = "官方网站")
    @Size(max = 256, message = "官方网站长度不能超过256个字符")
    private String website;

    @Schema(description = "是否有医保资质", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否有医保资质不能为空")
    private Integer hasMedicare;

    @Schema(description = "医院配置信息", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotNull(message = "医院配置信息不能为空")
    private InquiryHospitalSettingVO setting;

    @Schema(description = "是否禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer disable;

}