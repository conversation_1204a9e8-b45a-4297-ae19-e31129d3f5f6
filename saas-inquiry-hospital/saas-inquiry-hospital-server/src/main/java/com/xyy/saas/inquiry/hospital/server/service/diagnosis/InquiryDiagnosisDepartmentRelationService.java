package com.xyy.saas.inquiry.hospital.server.service.diagnosis;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDepartmentRelationReqDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryDiagnosisDepartmentRelationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryDiagnosisDepartmentRelationSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisSimpleVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.diagnosis.InquiryDiagnosisDepartmentRelationDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 科室诊断关联 Service 接口
 *
 * <AUTHOR>
 */
public interface InquiryDiagnosisDepartmentRelationService {

    /**
     * 创建科室诊断关联
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    void createInquiryDiagnosisDepartmentRelation(@Valid InquiryDiagnosisDepartmentRelationSaveReqVO createReqVO);

    /**
     * 更新科室诊断关联
     *
     * @param updateReqVO 更新信息
     */
    void updateInquiryDiagnosisDepartmentRelation(@Valid InquiryDiagnosisDepartmentRelationSaveReqVO updateReqVO);

    /**
     * 删除科室诊断关联
     *
     * @param id 编号
     */
    void deleteInquiryDiagnosisDepartmentRelation(Long id);

    /**
     * 获得科室诊断关联
     *
     * @param id 编号
     * @return 科室诊断关联
     */
    InquiryDiagnosisDepartmentRelationDO getInquiryDiagnosisDepartmentRelation(Long id);

    /**
     * 获得科室诊断关联分页
     *
     * @param pageReqVO 分页查询
     * @return 科室诊断关联分页
     */
    PageResult<InquiryDiagnosisDepartmentRelationDO> getInquiryDiagnosisDepartmentRelationPage(InquiryDiagnosisDepartmentRelationPageReqVO pageReqVO);

    /**
     * 根据条件查询关联诊断科室
     *
     * @param req
     * @return
     */
    List<InquiryDiagnosisDepartmentRelationDO> queryDiagnosisDepartmentRelation(InquiryDiagnosisDepartmentRelationReqDto req);

    /**
     * 根据科室id查已有诊断信息
     *
     * @param deptId 科室id
     * @return
     */
    List<InquiryDiagnosisSimpleVO> queryDiagnosisByOfficeId(Long deptId);
}