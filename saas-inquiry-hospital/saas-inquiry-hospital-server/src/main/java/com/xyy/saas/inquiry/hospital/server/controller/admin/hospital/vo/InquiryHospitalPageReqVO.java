package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 医院信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InquiryHospitalPageReqVO extends PageParam {

    @Schema(description = "医院ID", example = "4346")
    private Long id;

    @Schema(description = "医院名称", example = "赵六")
    private String name;

    @Schema(description = "医疗机构编码", example = "H404040")
    private String institutionCode;

    @Schema(description = "医院编码", example = "1211254")
    private String pref;

    @Schema(description = "医院等级")
    private Integer level;

    @Schema(description = "医院地址")
    private String address;

    @Schema(description = "联系电话")
    private String phone;

    @Schema(description = "电子邮件")
    private String email;

    @Schema(description = "官方网站")
    private String website;

    @Schema(description = "是否有医保资质")
    private Boolean hasMedicare;

    @Schema(description = "西成药问诊默认科室")
    private Long defaultInquiryWesternMedicineDeptId;

    @Schema(description = "中草药问诊默认科室")
    private Long defaultInquiryChineseMedicineDeptId;

    @Schema(description = "默认处方笺模板（西成药）")
    private Long defaultWesternPrescriptionTemplate;

    @Schema(description = "默认处方笺模板（中草药）")
    private Long defaultChinesePrescriptionTemplate;

    @Schema(description = "医院配置信息")
    private InquiryHospitalSettingVO setting;

    @Schema(description = "是否禁用")
    private Boolean disable;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "搜索关键字,根据医院编码或名称搜索", example = "王五", requiredMode = Schema.RequiredMode.REQUIRED)
    private String searchKey;

    @Schema(description = "不绑定的医院编码", example = "H404040")
    private List<String> unbindHospitalPrefs;

}