package com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

@Schema(description = "管理后台 - 处方记录详情 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InquiryPrescriptionDetailRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "2494")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "处方编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("处方编号")
    private String prescriptionPref;

    @Schema(description = "问诊单pref", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("问诊单pref")
    private String inquiryPref;

    @Schema(description = "租户名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("租户名称")
    private String tenantName;

    @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品编码")
    private String productPref;

    @Schema(description = "标准库id", requiredMode = Schema.RequiredMode.REQUIRED, example = "8400")
    @ExcelProperty("标准库id")
    private String standardId;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("商品名称")
    private String productName;

    @Schema(description = "通用名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("通用名称")
    private String commonName;

    @Schema(description = "用药方法eg:口服", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("用药方法eg:口服")
    private String directions;

    @Schema(description = "单次剂量 eg:1", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单次剂量 eg:1")
    private String singleDose;

    @Schema(description = "单次剂量单位 eg:片", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单次剂量单位 eg:片")
    private String singleUnit;

    @Schema(description = "使用频率 eg:一日三次", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("使用频率 eg:一日三次")
    private String useFrequency;

    @Schema(description = "药品类型：0西药，1中药", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("药品类型：0西药，1中药")
    private Integer medicineType;

    @Schema(description = "数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("数量")
    private BigDecimal quantity;

    @Schema(description = "商品规格", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品规格")
    private String attributeSpecification;

    @Schema(description = "生产厂家", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("生产厂家")
    private String manufacturer;

    @Schema(description = "产地", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("产地")
    private String producingArea;

    @Schema(description = "批准文号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("批准文号")
    private String approvalNumber;

    @Schema(description = "包装单位名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("包装单位名称")
    private String packageUnit;

    @Schema(description = "商品系统类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("商品系统类型")
    private Integer productSystemType;

    @Schema(description = "是否处方药:0否，1是")
    @ExcelProperty("是否处方药:0否，1是")
    private Integer prescriptionYn;

    @Schema(description = "商品价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "8211")
    @ExcelProperty("商品价格")
    private BigDecimal productPrice;

    @Schema(description = "实收金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("实收金额")
    private BigDecimal actualAmount;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}