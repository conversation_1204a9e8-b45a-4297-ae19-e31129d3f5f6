package com.xyy.saas.inquiry.hospital.server.controller.app.doctor;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo.DoctorQuickReplyMsgGroupRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo.DoctorQuickReplyMsgPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo.DoctorQuickReplyMsgRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo.DoctorQuickReplyMsgSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorQuickReplyMsgDO;
import com.xyy.saas.inquiry.hospital.server.service.doctor.DoctorQuickReplyMsgService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "APP + PC - 医生快捷回复语")
@RestController
@RequestMapping(value = {"/admin-api/kernel/hospital/doctor-quick-reply-msg", "/app-api/kernel/hospital/doctor-quick-reply-msg"})
@Validated
public class DoctorQuickReplyMsgController {

    @Resource
    private DoctorQuickReplyMsgService doctorQuickReplyMsgService;

    @PostMapping("/create")
    @Operation(summary = "创建医生快捷回复语")
    public CommonResult<Long> createDoctorQuickReplyMsg(@Valid @RequestBody DoctorQuickReplyMsgSaveReqVO createReqVO) {
        return success(doctorQuickReplyMsgService.createDoctorQuickReplyMsg(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新医生快捷回复语")
    public CommonResult<Boolean> updateDoctorQuickReplyMsg(@Valid @RequestBody DoctorQuickReplyMsgSaveReqVO updateReqVO) {
        doctorQuickReplyMsgService.updateDoctorQuickReplyMsg(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除医生快捷回复语")
    @Parameter(name = "ids", description = "编号集合", required = true)
    public CommonResult<Boolean> deleteDoctorQuickReplyMsg(@RequestParam("ids") Long[] ids) {
        doctorQuickReplyMsgService.batchDeleteDoctorQuickReplyMsg(ids);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得医生快捷回复语")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<DoctorQuickReplyMsgRespVO> getDoctorQuickReplyMsg(@RequestParam("id") Long id) {
        DoctorQuickReplyMsgDO doctorQuickReplyMsg = doctorQuickReplyMsgService.getDoctorQuickReplyMsg(id);
        return success(BeanUtils.toBean(doctorQuickReplyMsg, DoctorQuickReplyMsgRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得医生快捷回复语列表")
    public CommonResult<List<DoctorQuickReplyMsgGroupRespVO>> getDoctorQuickReplyMsgList(@Valid DoctorQuickReplyMsgPageReqVO pageReqVO) {
        return success(doctorQuickReplyMsgService.getDoctorQuickReplyMsgList(pageReqVO));
    }


}