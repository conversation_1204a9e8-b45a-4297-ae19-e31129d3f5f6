package com.xyy.saas.inquiry.hospital.server.service.prescription.external;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception0;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.SAAS_PRESCRIPTION_EXTERNAL_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.enums.transmitter.RequestStatusEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.transmission.PrescriptionExternalTransmissionRespDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.medical.vo.MedicalRegistrationRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.external.vo.SaasPrescriptionExternalPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.external.vo.SaasPrescriptionExternalRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.external.vo.SaasPrescriptionExternalSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionRespVO;
import com.xyy.saas.inquiry.hospital.server.convert.prescription.external.PrescriptionExternalConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.external.SaasPrescriptionExternalDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.prescription.external.SaasPrescriptionExternalMapper;
import com.xyy.saas.inquiry.hospital.server.mq.producer.prescription.PrescriptionSupervisionEndProducer;
import com.xyy.saas.inquiry.hospital.server.service.medical.MedicalRegistrationService;
import com.xyy.saas.inquiry.hospital.server.service.prescription.InquiryPrescriptionService;
import com.xyy.saas.inquiry.hospital.server.service.prescription.impl.PrescriptionCompletedServiceImpl;
import com.xyy.saas.inquiry.mq.prescription.dto.PrescriptionMqCommonMessage;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.patient.api.patient.PatientApi;
import com.xyy.saas.inquiry.patient.api.patient.dto.InquiryPatientInfoRespDTO;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.transmitter.internet.PrescriptionSupervisionConditionTransmitterDTO;
import com.xyy.saas.inquiry.pojo.transmitter.internet.PrescriptionTransmitterDTO;
import com.xyy.saas.inquiry.util.PrefUtil;
import com.xyy.saas.transmitter.api.task.TransmissionTaskApi;
import com.xyy.saas.transmitter.api.transmission.TransmissionApi;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionConfigReqDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionReqDTO;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 外配(电子)处方记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class SaasPrescriptionExternalServiceImpl implements SaasPrescriptionExternalService {

    @Resource
    private SaasPrescriptionExternalMapper saasPrescriptionExternalMapper;

    @Resource
    private InquiryPrescriptionService inquiryPrescriptionService;

    @Resource
    private MedicalRegistrationService medicalRegistrationService;

    @DubboReference
    private InquiryApi inquiryApi;

    @DubboReference
    private PatientApi patientApi;

    @DubboReference(retries = 0)
    private TransmissionApi transmissionApi;

    @DubboReference(retries = 0)
    private TransmissionTaskApi transmissionTaskApi;

    @DubboReference
    private TenantApi tenantApi;

    @Resource
    private PrescriptionCompletedServiceImpl prescriptionCompletedService;

    /**
     * 上传监管完成 - 回更就诊登记状态 - 暂无
     */
    @Resource
    private PrescriptionSupervisionEndProducer prescriptionSupervisionEndProducer;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SaasPrescriptionExternalDO saveSaasPrescriptionExternal(SaasPrescriptionExternalSaveReqVO createReqVO) {
        SaasPrescriptionExternalDO externalDO = saasPrescriptionExternalMapper.selectOneByCondition(SaasPrescriptionExternalPageReqVO.builder().bizId(createReqVO.getBizId()).bizType(createReqVO.getBizType()).build());
        SaasPrescriptionExternalDO saasPrescriptionExternal = PrescriptionExternalConvert.INSTANCE.convert(createReqVO);
        if (externalDO == null) {
            saasPrescriptionExternal.setPref(PrefUtil.getExternalPref());
            saasPrescriptionExternalMapper.insert(saasPrescriptionExternal);
        } else {
            saasPrescriptionExternal.setId(externalDO.getId());
            saasPrescriptionExternalMapper.updateById(saasPrescriptionExternal);
        }
        return saasPrescriptionExternal;
    }

    private SaasPrescriptionExternalDO validateSaasPrescriptionExternalExists(Long id) {
        SaasPrescriptionExternalDO saasPrescriptionExternalDO = saasPrescriptionExternalMapper.selectById(id);
        if (saasPrescriptionExternalDO == null) {
            throw exception(SAAS_PRESCRIPTION_EXTERNAL_NOT_EXISTS);
        }
        return saasPrescriptionExternalDO;
    }

    @Override
    public SaasPrescriptionExternalDO getSaasPrescriptionExternal(Long id) {
        return saasPrescriptionExternalMapper.selectById(id);
    }


    @Override
    public void deleteSaasPrescriptionExternal(Long id) {
        // 校验存在
        validateSaasPrescriptionExternalExists(id);
        // 删除
        saasPrescriptionExternalMapper.deleteById(id);
    }


    @Override
    public PageResult<SaasPrescriptionExternalRespVO> getSaasPrescriptionExternalPage(SaasPrescriptionExternalPageReqVO pageReqVO) {

        PageResult<SaasPrescriptionExternalDO> externalDOPageResult = saasPrescriptionExternalMapper.selectPage(pageReqVO);

        PageResult<SaasPrescriptionExternalRespVO> pageResult = PrescriptionExternalConvert.INSTANCE.convertPage(externalDOPageResult);

        if (CollUtil.isNotEmpty(pageResult.getList())) {
            // 查询患者信息
            Map<String, InquiryPatientInfoRespDTO> patientInfoRespDTOMap = patientApi.getPatientInfos(pageResult.getList().stream().map(SaasPrescriptionExternalRespVO::getPatientPref).toList())
                .stream().collect(Collectors.toMap(InquiryPatientInfoRespDTO::getPref, Function.identity(), (oldValue, newValue) -> newValue));
            // 查询处方信息
            List<String> prefs = pageResult.getList().stream().map(SaasPrescriptionExternalRespVO::getBizId).toList();
            Map<String, InquiryPrescriptionDO> prescriptionDOMap = CollUtil.isEmpty(prefs) ? Map.of() : inquiryPrescriptionService.queryListByPrefs(prefs)
                .stream().collect(Collectors.toMap(InquiryPrescriptionDO::getPref, Function.identity(), (oldValue, newValue) -> newValue));

            pageResult.getList().forEach(prescriptionExternalRespVO -> {

                Optional.ofNullable(prescriptionDOMap.get(prescriptionExternalRespVO.getBizId())).ifPresent(prescriptionDO -> {
                    prescriptionExternalRespVO.setInquiryStartTime(prescriptionDO.getInquiryStartTime());
                });

                Optional.ofNullable(patientInfoRespDTOMap.get(prescriptionExternalRespVO.getPatientPref())).ifPresent(patientInfoRespDTO -> {
                    prescriptionExternalRespVO.setPatientSex(patientInfoRespDTO.getSex());
                    prescriptionExternalRespVO.setPatientAge(patientInfoRespDTO.getAge());
                });
            });
        }

        return pageResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void retryUpload(Long id) {
        SaasPrescriptionExternalDO prescriptionExternalDO = validateSaasPrescriptionExternalExists(id);

        if (prescriptionExternalDO.extGet().getTaskId() == null) {
            PrescriptionTransmitterDTO transmitterDTO = getPrescriptionTransmitterDTO(prescriptionExternalDO.getBizId());
            if (transmitterDTO == null) {
                return;
            }
            Optional.ofNullable(prescriptionExternalDO.extGet().getPrescriptionPdfUrl()).ifPresent(transmitterDTO::setPrescriptionPdfUrl);
            // 上传电子处方
            uploadElePrescription(transmitterDTO, true);
            return;
        }
        // 由原有任务重传数据
        CommonResult<PrescriptionExternalTransmissionRespDto> resp = transmissionTaskApi.retryTask(prescriptionExternalDO.extGet().getTaskId(), PrescriptionExternalTransmissionRespDto.class);
        // 更新外配处方数据
        if (resp.isSuccess() && resp.getData() != null) {
            PrescriptionExternalConvert.INSTANCE.fillPrescriptionExternal(prescriptionExternalDO, resp.getData());
        }
        prescriptionExternalDO.extGet().setRemark(resp.isSuccess() ? "" : resp.getMsg());
        prescriptionExternalDO.setRequestStatus(resp.isSuccess() ? RequestStatusEnum.SUCCESS.getCode() : RequestStatusEnum.FAILED.getCode());
        prescriptionExternalDO.setUploadTime(LocalDateTime.now());
        saasPrescriptionExternalMapper.updateById(prescriptionExternalDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void externalSupervision(String prescriptionPref) {
        InquiryPrescriptionRespVO prescriptionRespVO = inquiryPrescriptionService.queryByCondition(InquiryPrescriptionQueryDTO.builder().pref(prescriptionPref).build());
        if (prescriptionRespVO == null) {
            return;
        }
        // 1.过滤基础判断条件
        PrescriptionSupervisionConditionTransmitterDTO dto = PrescriptionExternalConvert.INSTANCE.convertSupervisionCondition(prescriptionRespVO);
        TransmissionConfigReqDTO configReqDTO = TransmissionConfigReqDTO.builder().tenantId(prescriptionRespVO.getTenantId()).nodeType(NodeTypeEnum.INTERNET_SUPERVISION_CONDITION).build();
        TransmissionReqDTO transmissionReqDTO = TransmissionReqDTO.buildReq(configReqDTO, dto);

        CommonResult<Boolean> baseLogic = transmissionApi.validateBusinessLogic(transmissionReqDTO);
        log.info("电子处方监管-基础条件判断:prescriptionPref:{},:baseLogic:{}", prescriptionPref, baseLogic);
        if (baseLogic.isError() || !BooleanUtil.isTrue(baseLogic.getData())) {
            return;
        }
        TenantDto tenant = tenantApi.getTenant(prescriptionRespVO.getTenantId());
        // 2. 处方挂号
        InquiryRecordDetailDto inquiryRecordDetail = inquiryApi.getInquiryRecordDetail(prescriptionRespVO.getInquiryPref());
        PrescriptionTransmitterDTO transmitterDTO = PrescriptionExternalConvert.INSTANCE.convertPrescriptionTransmission(prescriptionRespVO, inquiryRecordDetail, tenant);

        // 电子处方监管-就诊挂号初始节点
        externalSupervisionPrescription(transmitterDTO,
            NodeTypeEnum.INTERNET_SUPERVISION_REGISTRATION_FEEDBACK,
            PrescriptionExternalTransmissionRespDto.class, null, null);
    }

    @Override
    public void externalSupervision1(PrescriptionMqCommonMessage msg) {
        // 构建传输数据
        PrescriptionTransmitterDTO transmitterDTO = getPrescriptionTransmitterDTO(msg.getPrescriptionPref());
        if (transmitterDTO == null) {
            return;
        }
        Optional.ofNullable(msg.getPrescriptionPdfUrl()).ifPresent(transmitterDTO::setPrescriptionPdfUrl);
        // 处方挂号
        TenantUtils.execute(transmitterDTO.getTenantId(), () -> prescriptionCompletedService.medicareVisitRegistration(transmitterDTO));

        // 在线处方信息上报
        uploadInternetSupervisionPrescriptionReport(transmitterDTO);

        // 上传电子处方
        uploadElePrescription(transmitterDTO);
    }

    private PrescriptionTransmitterDTO getPrescriptionTransmitterDTO(String prescriptionPref) {
        InquiryPrescriptionRespVO prescriptionRespVO = inquiryPrescriptionService.queryByCondition(InquiryPrescriptionQueryDTO.builder().pref(prescriptionPref).build());
        if (prescriptionRespVO == null) {
            return null;
        }
        TenantDto tenant = tenantApi.getTenant(prescriptionRespVO.getTenantId());
        InquiryRecordDetailDto inquiryRecordDetail = inquiryApi.getInquiryRecordDetail(prescriptionRespVO.getInquiryPref());
        return PrescriptionExternalConvert.INSTANCE.convertPrescriptionTransmission(prescriptionRespVO, inquiryRecordDetail, tenant);
    }


    private void uploadInternetSupervisionPrescriptionReport(PrescriptionTransmitterDTO transmitterDTO) {
        // 在线处方信息上报
        externalSupervisionPrescription(transmitterDTO,
            NodeTypeEnum.INTERNET_SUPERVISION_ONLINE_PRESCRIPTION_INFORMATION_REPORT,
            PrescriptionExternalTransmissionRespDto.class, null, null);
    }


    private void uploadElePrescription(PrescriptionTransmitterDTO transmitterDTO, boolean... isThrow) {
        // 上传电子处方
        externalSupervisionPrescription(transmitterDTO,
            NodeTypeEnum.MEDICAL_ELE_PRESCRIPTION_UPLOAD,
            PrescriptionExternalTransmissionRespDto.class,
            () -> {
                // 获取挂号信息 保存电子处方记录
                MedicalRegistrationRespVO medicalRegistrationInfo = medicalRegistrationService.getMedicalRegistrationInfo(BizTypeEnum.HYWZ, transmitterDTO.getInquiryPref());
                saveSaasPrescriptionExternal(PrescriptionExternalConvert.INSTANCE.convertSaveVo(transmitterDTO, medicalRegistrationInfo));
            },
            (resp, taskId) -> {
                // 更新外配处方数据
                SaasPrescriptionExternalDO prescriptionExternalDO = saasPrescriptionExternalMapper.selectOne(SaasPrescriptionExternalDO::getBizId, transmitterDTO.getPref(), SaasPrescriptionExternalDO::getBizType,
                    BizTypeEnum.HYWZ.getCode());
                if (prescriptionExternalDO == null) {
                    return;
                }
                PrescriptionExternalConvert.INSTANCE.fillPrescriptionExternal(prescriptionExternalDO, resp.isSuccess() ? resp.getData() : new PrescriptionExternalTransmissionRespDto());
                prescriptionExternalDO.extGet().setTaskId(taskId);
                prescriptionExternalDO.extGet().setRemark(resp.isSuccess() ? null : resp.getMsg());
                prescriptionExternalDO.setRequestStatus(resp.isSuccess() ? RequestStatusEnum.SUCCESS.getCode() : RequestStatusEnum.FAILED.getCode());
                prescriptionExternalDO.setUploadTime(LocalDateTime.now());
                saasPrescriptionExternalMapper.updateById(prescriptionExternalDO);
            }, isThrow);
    }

    /**
     * 陕西省监管平台-在线处方信息上报 当在线处方审核通过后调用该接口，将在线处方信息上报至陕西省互联网医院监管平台
     *
     * @param transmitterDTO 处方信息
     */
    @SuppressWarnings("SameParameterValue")
    private <T> void externalSupervisionPrescription(@Nonnull PrescriptionTransmitterDTO transmitterDTO,
        @Nonnull NodeTypeEnum nodeType,
        @Nonnull Class<T> clazz,
        Runnable postProcessorBeforeInvoke,
        BiConsumer<CommonResult<T>, Long> postProcessorAfterInvoke, boolean... isThrow) {

        String prescriptionPref = transmitterDTO.getPref();
        Long tenantId = transmitterDTO.getTenantId();
        try {
            // 构建传输数据
            TransmissionConfigReqDTO configReqDTO = TransmissionConfigReqDTO.builder()
                .tenantId(tenantId)
                .nodeType(nodeType)
                .build();
            TransmissionReqDTO transmissionReqDTO = TransmissionReqDTO.buildReq(configReqDTO, transmitterDTO);

            CommonResult<Boolean> businessLogic = transmissionApi.validateBusinessLogic(transmissionReqDTO);
            log.info("externalSupervisionPrescription-{} 业务逻辑验证，prescriptionPref: {}, businessLogic: {}", nodeType.getDesc(), prescriptionPref, businessLogic);

            if (businessLogic.isSuccess() && businessLogic.getData()) {

                if (postProcessorBeforeInvoke != null) {
                    postProcessorBeforeInvoke.run();
                }

                log.info("start-{} 节点，prescriptionPref: {}", nodeType.getDesc(), prescriptionPref);
                CommonResult<T> result = transmissionApi.contractInvoke(transmissionReqDTO, clazz);
                log.info("externalSupervisionPrescription-{} 节点，prescriptionPref: {}, result: {}", nodeType.getDesc(), prescriptionPref, result);

                if (postProcessorAfterInvoke != null) {
                    postProcessorAfterInvoke.accept(result, transmissionReqDTO.getTaskId());
                }
                if (isThrow.length > 0 && isThrow[0] && result.isError()) {
                    throw exception0(result.getCode(), result.getMsg());
                }
            }
        } catch (Exception e) {
            log.error("externalSupervisionPrescription-error-{} 节点，prescriptionPref: {}", nodeType.getDesc(), prescriptionPref, e);
            if (isThrow.length > 0 && isThrow[0]) {
                throw e;
            }
        }
    }

}