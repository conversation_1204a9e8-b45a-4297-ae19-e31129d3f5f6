package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 问诊职业(医生药师)证件信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InquiryProfessionIdentificationRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "26450")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "医生GUID", requiredMode = Schema.RequiredMode.REQUIRED, example = "30951")
    @ExcelProperty("医生GUID")
    private String guid;

    @Schema(description = "类型,1医生,2药师", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("类型,1医生,2药师")
    private Integer doctorType;

    @Schema(description = "证件类型 1、头像 2、查证结果 3、职称证明 4、执业证 5、资格证", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("证件类型 1、头像 2、查证结果 3、职称证明 4、执业证 5、资格证")
    private Integer certificateType;

    @Schema(description = "证件名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("证件名称")
    private String certificateName;

    @Schema(description = "证件号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("证件号")
    private String certificateNo;

    @Schema(description = "证件地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("证件地址")
    private String certificateImgUrl;

    @Schema(description = "注册发证日期")
    @ExcelProperty("注册发证日期")
    private LocalDateTime registerTime;

    @Schema(description = "有效期")
    @ExcelProperty("有效期")
    private LocalDateTime validTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}