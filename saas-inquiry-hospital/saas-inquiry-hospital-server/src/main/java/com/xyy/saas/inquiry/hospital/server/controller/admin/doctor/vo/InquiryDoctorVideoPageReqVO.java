package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 医生录屏记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InquiryDoctorVideoPageReqVO extends PageParam {

    @Schema(description = "录屏编号")
    private String pref;

    @Schema(description = "医生pref")
    private String doctorPref;

    @Schema(description = "医生端问诊视频url地址", example = "https://www.iocoder.cn")
    private String videoUrl;

    @Schema(description = "视频md5")
    private String md5;

    @Schema(description = "启用：0是，1否 ", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}