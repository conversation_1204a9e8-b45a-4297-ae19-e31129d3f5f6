package com.xyy.saas.inquiry.hospital.server.controller.admin.medical;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.inquiry.hospital.server.controller.admin.medical.vo.MedicalInsuranceOrderPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.medical.vo.MedicalInsuranceOrderRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.medical.vo.MedicalInsuranceOrderSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.medical.MedicalInsuranceOrderDO;
import com.xyy.saas.inquiry.hospital.server.service.medical.MedicalInsuranceOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "管理后台 - 医保订单信息")
@RestController
@RequestMapping("/hospital/medical-insurance-order")
@Validated
public class MedicalInsuranceOrderController {

    @Resource
    private MedicalInsuranceOrderService medicalInsuranceOrderService;

    @PostMapping("/create")
    @Operation(summary = "创建医保订单信息")
    @PreAuthorize("@ss.hasPermission('hospital:medical-insurance-order:create')")
    public CommonResult<Long> createMedicalInsuranceOrder(@Valid @RequestBody MedicalInsuranceOrderSaveReqVO createReqVO) {
        return success(medicalInsuranceOrderService.createMedicalInsuranceOrder(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新医保订单信息")
    @PreAuthorize("@ss.hasPermission('hospital:medical-insurance-order:update')")
    public CommonResult<Boolean> updateMedicalInsuranceOrder(@Valid @RequestBody MedicalInsuranceOrderSaveReqVO updateReqVO) {
        medicalInsuranceOrderService.updateMedicalInsuranceOrder(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除医保订单信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hospital:medical-insurance-order:delete')")
    public CommonResult<Boolean> deleteMedicalInsuranceOrder(@RequestParam("id") Long id) {
        medicalInsuranceOrderService.deleteMedicalInsuranceOrder(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得医保订单信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hospital:medical-insurance-order:query')")
    public CommonResult<MedicalInsuranceOrderRespVO> getMedicalInsuranceOrder(@RequestParam("id") Long id) {
        MedicalInsuranceOrderDO medicalInsuranceOrder = medicalInsuranceOrderService.getMedicalInsuranceOrder(id);
        return success(BeanUtils.toBean(medicalInsuranceOrder, MedicalInsuranceOrderRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得医保订单信息分页")
    @PreAuthorize("@ss.hasPermission('hospital:medical-insurance-order:query')")
    public CommonResult<PageResult<MedicalInsuranceOrderRespVO>> getMedicalInsuranceOrderPage(@Valid MedicalInsuranceOrderPageReqVO pageReqVO) {
        PageResult<MedicalInsuranceOrderDO> pageResult = medicalInsuranceOrderService.getMedicalInsuranceOrderPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MedicalInsuranceOrderRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出医保订单信息 Excel")
    @PreAuthorize("@ss.hasPermission('hospital:medical-insurance-order:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMedicalInsuranceOrderExcel(@Valid MedicalInsuranceOrderPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MedicalInsuranceOrderDO> list = medicalInsuranceOrderService.getMedicalInsuranceOrderPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "医保订单信息.xls", "数据", MedicalInsuranceOrderRespVO.class,
            BeanUtils.toBean(list, MedicalInsuranceOrderRespVO.class));
    }

}