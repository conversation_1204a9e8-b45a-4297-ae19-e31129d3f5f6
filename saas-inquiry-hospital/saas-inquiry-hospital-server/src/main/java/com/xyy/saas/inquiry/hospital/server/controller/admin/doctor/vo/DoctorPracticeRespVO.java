package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 医生执业信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DoctorPracticeRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "32594")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "医生GUID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12774")
    @ExcelProperty("医生GUID")
    private String guid;

    @Schema(description = "第一执业机构名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("第一执业机构名称")
    private String firstPracticeName;

    @Schema(description = "第一执业机构等级，例如：0=三甲, 1=三乙 ...", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("第一执业机构等级，例如：0=三甲, 1=三乙 ...")
    private Integer firstPracticeLevel;

    @Schema(description = "第一执业机构科室GUID", requiredMode = Schema.RequiredMode.REQUIRED, example = "204")
    @ExcelProperty("第一执业机构科室GUID")
    private String deptGuid;

    @Schema(description = "专业职称代码，例如：2", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("专业职称代码，例如：2")
    private String titleCode;

    @Schema(description = "专业职称名称，例如：副主任医师", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("专业职称名称，例如：副主任医师")
    private String titleName;

    @Schema(description = "专业职称证书编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("专业职称证书编号")
    private String titleNo;

    @Schema(description = "专业职称证书取得时间，例如：2016-03-20")
    @ExcelProperty("专业职称证书取得时间，例如：2016-03-20")
    private LocalDateTime titleTime;

    @Schema(description = "开始执业时间，例如：2021-03-20")
    @ExcelProperty("开始执业时间，例如：2021-03-20")
    private LocalDateTime startPracticeTime;

    @Schema(description = "执业结束时间，例如：2029-03-20")
    @ExcelProperty("执业结束时间，例如：2029-03-20")
    private LocalDateTime endPracticeDate;

    @Schema(description = "执业证书号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("执业证书号")
    private String professionalNo;

    @Schema(description = "执业证书取得时间，例如：2016-03-20")
    @ExcelProperty("执业证书取得时间，例如：2016-03-20")
    private LocalDateTime professionalTime;

    @Schema(description = "资格证书号")
    @ExcelProperty("资格证书号")
    private String qualificationNo;

    @Schema(description = "资格证书取得时间，例如：2016-03-20")
    @ExcelProperty("资格证书取得时间，例如：2016-03-20")
    private LocalDateTime qualificationTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}