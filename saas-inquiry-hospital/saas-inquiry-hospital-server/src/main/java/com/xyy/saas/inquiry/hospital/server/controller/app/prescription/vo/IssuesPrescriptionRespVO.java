package com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * @Author: xucao
 * @Date: 2025/02/06 10:53
 * @Description: 开具处方返回结果
 */
@Data
public class IssuesPrescriptionRespVO {

    @Schema(description = "处方编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1517")
    private String pref;

    @Schema(description = "租户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1517")
    private Long tenantId;

    @Schema(description = "租户名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "1517")
    private String tenantName;

    @Schema(description = "互联网医院编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "21843")
    private String hospitalPref;

    @Schema(description = "科室编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String deptPref;

    @Schema(description = "科室名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    private String deptName;

    @Schema(description = "问诊编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "32287")
    private String inquiryPref;

    @Schema(description = "患者编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "30337")
    private String patientPref;

    @Schema(description = "患者姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String patientName;

    @Schema(description = "患者性别：1 男 2 女", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer patientSex;

    @Schema(description = "患者年龄", requiredMode = Schema.RequiredMode.REQUIRED)
    private String patientAge;

    @Schema(description = "患者手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String patientMobile;

    @Schema(description = "医生编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "20472")
    private String doctorPref;

    @Schema(description = "医师姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String doctorName;

    @Schema(description = "用药类型：0西药，1中药", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer medicineType;

    @Schema(description = "问诊开始时间")
    private LocalDateTime inquiryStartTime;

    @Schema(description = "问诊结束时间")
    private LocalDateTime inquiryEndTime;

    @Schema(description = "医师出方时间")
    private LocalDateTime outPrescriptionTime;

    @Schema(description = "药师审方时间")
    private LocalDateTime auditPrescriptionTime;

    @Schema(description = "处方笺图片url", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    private String prescriptionImgUrl;

    @Schema(description = "处方笺PDFurl", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    private String prescriptionPdfUrl;

    @Schema(description = "问诊方式  1、图文问诊  2、视频问诊  3、电话问诊", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer inquiryWayType;
}
