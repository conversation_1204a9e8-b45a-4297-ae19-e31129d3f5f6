package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @Author: xucao
 * @DateTime: 2025/7/23 20:45
 * @Description: 医院绑定请求VO
 **/
@Schema(description = "管理后台 - 医院绑定请求VO")
@Data
@ToString(callSuper = true)
public class HospitalBindReqVO {

    @Schema(description = "医院编码", example = "H001")
    @NotEmpty(message = "医院编码不能为空")
    private String hospitalPref;

    @Schema(description = "userId", example = "1")
    @NotNull(message = "绑定用户为空")
    private Long userId;
}
