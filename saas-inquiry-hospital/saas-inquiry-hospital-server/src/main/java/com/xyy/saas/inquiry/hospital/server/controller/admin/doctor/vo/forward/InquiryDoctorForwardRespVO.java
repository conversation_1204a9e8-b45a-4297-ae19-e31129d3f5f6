package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.forward;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Schema(description = "管理后台 - 医生信息 Response VO")
@Data
public class InquiryDoctorForwardRespVO implements Serializable {

    /**
     * 编码
     */
    private Long id;

    /**
     * 医生guid
     */
    private String guid;

    /**
     * 医生姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String telephone;
    /**
     * 科室
     */
    private String docDeptName;

    /**
     * 职称
     */
    private String titleName;

    /**
     * 第一职业机构
     */
    private String workInstName;
    /**
     * 机构等级
     */
    private String orgGrade;
    /**
     * 医生类型：0：兼职医生 1：全职
     */
    private Integer docJobType;

    /**
     * 合作状态
     */
    private Byte cooperationStatus;

    /**
     * 注销状态
     */
    private Integer accountStatus;
    /**
     * 基础信息状态
     */
    private Integer basicStatus;
    /**
     * 资质审核状态
     */
    private Integer qualificationStatus;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 在线状态 是否出诊：0 否 1 是
     */
    private Byte ynInquiry;
    /**
     * 出诊时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date onLineTime;
    /**
     * 停诊时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date offLineTime;
    /**
     * 实名认证状态
     */
    private Integer fddCertifyStatus;
    /**
     * 免验证签
     */
    private Integer visaFreeStatus;

}