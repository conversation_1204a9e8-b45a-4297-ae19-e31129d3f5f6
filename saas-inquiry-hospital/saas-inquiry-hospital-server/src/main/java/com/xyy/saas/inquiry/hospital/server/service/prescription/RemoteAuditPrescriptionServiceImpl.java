package com.xyy.saas.inquiry.hospital.server.service.prescription;

import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.prescription.InquiryPrescriptionMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * @Author: xucao
 * @DateTime: 2025/4/24 14:48
 * @Description: 远程审方服务实现类
 **/
@Service
public class RemoteAuditPrescriptionServiceImpl implements RemoteAuditPrescriptionService{

    @Resource
    private InquiryPrescriptionMapper inquiryPrescriptionMapper;

    /**
     * 保存远程问诊处方
     *
     * @param prescriptionDO
     */
    @Override
    public void saveRemotePrescription(InquiryPrescriptionDO prescriptionDO) {
        inquiryPrescriptionMapper.insert(prescriptionDO);
    }

    /**
     * 删除远程问诊处方
     *
     * @param prescriptionDO
     */
    @Override
    public void delRemotePrescription(InquiryPrescriptionDO prescriptionDO) {
        inquiryPrescriptionMapper.deleteById(prescriptionDO.getId());
    }

    /**
     * 根据处方单号查询处方信息
     *
     * @param inquiryPref
     * @return
     */
    @Override
    public InquiryPrescriptionDO selectByInquiryPref(String inquiryPref) {
        return inquiryPrescriptionMapper.selectOne(InquiryPrescriptionDO::getInquiryPref, inquiryPref);
    }

    /**
     * 查询当前门店所有待审核的远程审方问诊单号
     *
     * @param tenantId 租户id
     */
    @Override
    public List<String> selectRemoteAuditInquriyPrefList(Long tenantId) {
        return inquiryPrescriptionMapper.selectRemoteAuditInquriyPrefList(tenantId);
    }

    /**
     * @param prescriptions
     */
    @Override
    public void batchDelRemotePrescription(List<InquiryPrescriptionDO> prescriptions) {
        inquiryPrescriptionMapper.deleteByIds(prescriptions.stream().map(InquiryPrescriptionDO::getId).toList());
    }
}
