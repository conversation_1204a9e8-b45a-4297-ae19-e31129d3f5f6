package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 医生出诊状态关系分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InquiryDoctorStatusPageReqVO extends PageParam {

    @Schema(description = "医生编码", example = "11224")
    private String doctorPref;

    @Schema(description = "审方类型 1:图文 2:视频 3:电话", example = "1")
    private Integer inquiryWayType;

    @Schema(description = "问诊业务类型 1:药店问诊 2:远程审方", example = "2")
    private Integer inquiryBizType;

    @Schema(description = "方类型：0手动开方  1自动开方", example = "2")
    private Integer inquiryType;

    @Schema(description = "出诊状态：0闭诊 1出诊", example = "2")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}