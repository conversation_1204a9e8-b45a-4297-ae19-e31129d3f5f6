package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import com.xyy.saas.inquiry.pojo.condition.ConditionGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 医院信息新增/修改 特定处方笺 VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class InquiryHospitalSpecificPrescriptionTemplateVO extends ConditionGroup {

    @Schema(description = "处方笺模板id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "处方笺模板id不能为空")
    private Long prescriptionTemplateId;

    @Schema(description = "处方笺模板名称")
    private String prescriptionTemplateName;

}