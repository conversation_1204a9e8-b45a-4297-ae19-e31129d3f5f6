package com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo;

import com.xyy.saas.inquiry.constant.ValidateGroup.Delete;
import com.xyy.saas.inquiry.constant.ValidateGroup.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

@Schema(description = "管理后台 - 处方废弃 VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class InquiryPrescriptionAbandonVO {

    @Schema(description = "处方ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "处方ID不能为空", groups = {Delete.class})
    private Long id;

    @NotNull(message = "处方ids不能为空", groups = {Update.class})
    private List<Long> ids;

    @Schema(description = "处方废弃操作人")
    private String abandonUser;

    @Schema(description = "处方废弃原因")
    @NotEmpty(message = "处方废弃原因不能为空", groups = {Delete.class})
    @Length(max = 100, message = "处方废弃原因长度不能超过100", groups = {Delete.class})
    private String abandonReason;


}