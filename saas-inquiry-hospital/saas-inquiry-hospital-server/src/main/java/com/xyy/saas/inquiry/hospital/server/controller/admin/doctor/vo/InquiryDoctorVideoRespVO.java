package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

@Schema(description = "管理后台 - 医生录屏记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InquiryDoctorVideoRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "28737")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "录屏编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("录屏编号")
    private String pref;

    @Schema(description = "医生pref")
    @ExcelProperty("医生pref")
    private String doctorPref;

    @Schema(description = "医生端问诊视频url地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("医生端问诊视频url地址")
    private String videoUrl;

    @Schema(description = "视频md5")
    @ExcelProperty("视频md5")
    private String md5;

    @Schema(description = "启用：0是，1否 ", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("启用：0是，1否 ")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}