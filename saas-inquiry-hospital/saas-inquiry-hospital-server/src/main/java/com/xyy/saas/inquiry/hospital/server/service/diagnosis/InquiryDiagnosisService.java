package com.xyy.saas.inquiry.hospital.server.service.diagnosis;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisExcelVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.diagnosis.InquiryDiagnosisDO;
import com.xyy.saas.inquiry.pojo.excel.ImportResultDto;
import com.xyy.saas.inquiry.product.api.search.dto.InquiryDiagnosticsSearchReqDto;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 问诊诊断信息 Service 接口
 *
 * <AUTHOR>
 */
public interface InquiryDiagnosisService {

    /**
     * 创建问诊诊断信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInquiryDiagnosis(@Valid InquiryDiagnosisSaveReqVO createReqVO);

    /**
     * 更新问诊诊断信息
     *
     * @param updateReqVO 更新信息
     */
    void updateInquiryDiagnosis(@Valid InquiryDiagnosisSaveReqVO updateReqVO);

    /**
     * 删除问诊诊断信息
     *
     * @param id 编号
     */
    void deleteInquiryDiagnosis(Long id);

    /**
     * 获得问诊诊断信息
     *
     * @param id 编号
     * @return 问诊诊断信息
     */
    InquiryDiagnosisDO getInquiryDiagnosis(Long id);

    /**
     * 获得问诊诊断信息分页
     *
     * @param pageReqVO 分页查询
     * @return 问诊诊断信息分页
     */
    PageResult<InquiryDiagnosisDO> getInquiryDiagnosisPage(InquiryDiagnosisPageReqVO pageReqVO);


    PageResult<InquiryDiagnosisDO> getDiagnosisPage(InquiryDiagnosisPageReqVO pageReqVO);

    /**
     * 获取诊断信息列表
     *
     * @param reqVO
     * @return
     */
    List<InquiryDiagnosisRespVO> queryInquiryDiagnosis(InquiryDiagnosisDto diagnosisDto);

    /**
     * 药品推荐诊断
     *
     * @param reqDto
     * @return
     */
    List<InquiryDiagnosisRespVO> recommendDiagnosis(InquiryDiagnosticsSearchReqDto reqDto);

    /**
     * 常用诊断
     *
     * @param pageReqVO
     * @return
     */
    List<InquiryDiagnosisDO> commonDiagnosis(InquiryDiagnosisPageReqVO pageReqVO);

    /**
     * 诊断数据导入
     *
     * @param list          诊断列表
     * @param updateSupport 是否支持更新
     * @return
     */
    ImportResultDto importDiagnosisList(List<InquiryDiagnosisExcelVO> list, Boolean updateSupport);

    /**
     * 根据名称查询诊断信息
     *
     * @param showName
     * @return
     */
    List<InquiryDiagnosisRespVO> queryInquiryDiagnosisByShowName(String showName, Integer status);
}