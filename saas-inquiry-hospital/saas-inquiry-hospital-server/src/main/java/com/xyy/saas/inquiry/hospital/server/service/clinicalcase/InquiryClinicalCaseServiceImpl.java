package com.xyy.saas.inquiry.hospital.server.service.clinicalcase;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.xyy.saas.inquiry.drugstore.api.option.InquiryOptionConfigApi;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigRespDto;
import com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.hospital.api.clinicalcase.dto.InquiryClinicalCaseQueryDto;
import com.xyy.saas.inquiry.hospital.server.controller.app.clinicalcase.vo.InquiryClinicalCaseRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.clinicalcase.vo.InquiryClinicalCaseSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.convert.clinicalcase.InquiryClinicalCaseConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.clinicalcase.InquiryClinicalCaseDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.clinicalcase.InquiryClinicalCaseMapper;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorService;
import com.xyy.saas.inquiry.hospital.server.service.hospital.InquiryHospitalDetpDoctorRelationService;
import com.xyy.saas.inquiry.hospital.server.service.message.DoctorImService;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.hospital.api.medicare.MedicalRegistrationApi;
import com.xyy.saas.inquiry.hospital.api.medicare.dto.MedicalRegistrationDto;
import com.xyy.saas.inquiry.signature.api.signature.InquiryUserSignatureInformationApi;
import com.xyy.saas.inquiry.util.PrefUtil;
import com.xyy.saas.transmitter.api.transmission.TransmissionApi;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 门诊病例 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class InquiryClinicalCaseServiceImpl implements InquiryClinicalCaseService {

    @Resource
    private InquiryClinicalCaseMapper inquiryClinicalCaseMapper;

    @Resource
    private InquiryDoctorService inquiryDoctorService;

    @Resource
    private InquiryHospitalDetpDoctorRelationService inquiryHospitalDetpDoctorRelationService;

    @DubboReference
    private InquiryApi inquiryApi;

    @DubboReference
    private InquiryUserSignatureInformationApi inquiryUserSignatureInformationApi;

    @DubboReference
    private TransmissionApi transmissionApi;

    @DubboReference
    private MedicalRegistrationApi medicalRegistrationApi;

    @Resource
    private InquiryOptionConfigApi inquiryOptionConfigApi;

    @Resource
    private TenantApi tenantApi;

    @Resource
    private DoctorImService doctorImService;


    private InquiryClinicalCaseServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }


    @Override
    public String saveInquiryClinicalCase(InquiryClinicalCaseSaveReqVO createReqVO) {
        // 获取问诊单 构建病例信息
        InquiryRecordDto inquiryRecordDto = inquiryApi.getInquiryRecord(createReqVO.getInquiryPref());
        InquiryRecordDetailDto recordDetailDto = inquiryApi.getInquiryRecordDetail(createReqVO.getInquiryPref());

        InquiryClinicalCaseDO clinicalCaseDO = InquiryClinicalCaseConvert.INSTANCE.convert(createReqVO, inquiryRecordDto, recordDetailDto);
        getSelf().saveClinicalCase(clinicalCaseDO, inquiryRecordDto);
        return clinicalCaseDO.getPref();
    }


    @Override
    public void autoInquiryCreateClinicalCase(String inquiryPref) {
        try {
            // 获取问诊单
            InquiryRecordDto inquiryRecordDto = inquiryApi.getInquiryRecord(inquiryPref);
            InquiryOptionConfigRespDto optionConfig = inquiryOptionConfigApi.getInquiryOptionConfig(tenantApi.getTenant(inquiryRecordDto.getTenantId()), InquiryOptionTypeEnum.PROC_CASE_RECORD);
            if (optionConfig == null || !BooleanUtil.isTrue(optionConfig.getProcCaseRecord())) {
                return;   // 未开启病历书写 跳过
            }
            // 获取问诊单详情,构建病例信息
            InquiryRecordDetailDto inquiryRecordDetail = inquiryApi.getInquiryRecordDetail(inquiryPref);
            InquiryClinicalCaseDO saveCaseDo = InquiryClinicalCaseConvert.INSTANCE.convert(inquiryRecordDto, inquiryRecordDetail);
            getSelf().saveClinicalCase(saveCaseDo, inquiryRecordDto);
        } catch (Exception e) {
            log.error("自动开方创建门诊病例失败,inquiryPref:{}", inquiryPref, e);
        }
    }

    /**
     * 真*保存病例书写 并执行后续动作 eg:推送病例消息,上传第三方等
     *
     * @param clinicalCaseDO
     */
    // @Transactional(rollbackFor = Exception.class)
    public void saveClinicalCase(InquiryClinicalCaseDO clinicalCaseDO, InquiryRecordDto inquiryRecordDto) {
        // 修改病例
        InquiryClinicalCaseDO inquiryClinicalCaseDO = inquiryClinicalCaseMapper.selectOne(InquiryClinicalCaseQueryDto.builder().inquiryPref(clinicalCaseDO.getInquiryPref()).build());
        if (inquiryClinicalCaseDO != null) {
            clinicalCaseDO.setId(inquiryClinicalCaseDO.getId()).setPref(null);
            inquiryClinicalCaseMapper.updateById(clinicalCaseDO);
            return;
        }
        // 保存病例
        clinicalCaseDO.setPref(PrefUtil.getClinicalCasePref());
        InquiryDoctorDO doctor = inquiryDoctorService.getInquiryDoctorByDoctorPref(inquiryRecordDto.getDoctorPref());
        clinicalCaseDO.extGet().setDoctorSignatureUrl(inquiryUserSignatureInformationApi.getInquiryUserSignatureUrl(doctor.getUserId(), SignaturePlatformEnum.FDD));
        inquiryClinicalCaseMapper.insert(clinicalCaseDO);
        // 推送门诊病例卡片
        doctorImService.sendClinicalCaseImMessage(clinicalCaseDO, inquiryRecordDto);

        // 上传第三方 Q341病例上传
        // TransmissionConfigReqDTO configReqDTO = TransmissionConfigReqDTO.builder().tenantId(clinicalCaseDO.getTenantId()).nodeType(NodeTypeEnum.INTERNET_SUPERVISION_UPLOAD_OUT_PATIENT_CASE).build();
        // boolean protocolConfig = transmissionApi.validProtocolConfig(configReqDTO);
        // log.info("病例上传-对接互联网监管:inquiryPref:{},{}", inquiryRecordDto.getPref(), protocolConfig);
        // if (protocolConfig) {
        //     OutPatientCaseTransmitterDTO outPatientCaseTransmitterDTO = InquiryClinicalCaseConvert.INSTANCE.convertTransmissionDTO(getInquiryClinicalCaseRespVO(clinicalCaseDO));
        //     transmissionApi.contractInvoke(TransmissionReqDTO.buildReq(configReqDTO, outPatientCaseTransmitterDTO).setAsync(true), CommonResult.class);
        // }
    }

    @Override
    public InquiryClinicalCaseRespVO getInquiryClinicalCase(String inquiryPref) {
        InquiryClinicalCaseDO caseDO = inquiryClinicalCaseMapper.selectOne(InquiryClinicalCaseQueryDto.builder().inquiryPref(inquiryPref).build());
        if (caseDO == null) {
            return null;
        }
        return getInquiryClinicalCaseRespVO(caseDO);
    }

    private InquiryClinicalCaseRespVO getInquiryClinicalCaseRespVO(InquiryClinicalCaseDO caseDO) {
        InquiryClinicalCaseRespVO clinicalCaseRespVO = InquiryClinicalCaseConvert.INSTANCE.convertVo(caseDO);
        // 问诊信息
        InquiryRecordDto inquiryRecord = inquiryApi.getInquiryRecord(caseDO.getInquiryPref());
        clinicalCaseRespVO.setStartTime(inquiryRecord.getStartTime());
        clinicalCaseRespVO.setMedicineType(inquiryRecord.getMedicineType());
        // 医生信息
        clinicalCaseRespVO.setDoctorSignatureUrl(clinicalCaseRespVO.getExt().getDoctorSignatureUrl());
        String doctorHospitalPref = inquiryHospitalDetpDoctorRelationService.getDoctorHospitalPref(caseDO.getDoctorPref(), caseDO.getHospitalPref());
        clinicalCaseRespVO.setDoctorHospitalPref(doctorHospitalPref);
        // 挂号信息 判断是否有挂号记录 取门诊号,如果没有取患者编号
        MedicalRegistrationDto medicalRegistrationInfo = medicalRegistrationApi.getMedicalRegistrationInfo(BizTypeEnum.HYWZ, caseDO.getInquiryPref());
        clinicalCaseRespVO.setIptOtpNo(medicalRegistrationInfo == null || StringUtils.isBlank(medicalRegistrationInfo.getIptOtpNo()) ? caseDO.getPatientPref() : medicalRegistrationInfo.getIptOtpNo());
        clinicalCaseRespVO.setBizVisitId(medicalRegistrationInfo == null ? null : medicalRegistrationInfo.getBizVisitId());
        return clinicalCaseRespVO;
    }


}