package com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionStatusEnum;
import com.xyy.saas.inquiry.util.excel.validator.ValidDateFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

@EqualsAndHashCode(callSuper = true)
@Schema(description = "管理后台 - 刷处方 VO")
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
public class InquiryPrescriptionFlushReqVO extends PageParam {

    /**
     * 处方编号
     */
    private List<String> prefs;

    /**
     * 机构编号
     */
    private Long tenantId;

    /**
     * 处方状态
     */
    @InEnum(value = PrescriptionStatusEnum.class)
    private Integer status;


    private Integer inquiryBizType;

    /**
     * 医生开方时间
     */
    @ValidDateFormat(pattern = "yyyy-MM-dd HH:mm:ss", message = "医生开方时间startTime格式错误 yyyy-MM-dd HH:mm:ss")
    private String startTime;

    /**
     * 医生开方时间
     */
    @ValidDateFormat(pattern = "yyyy-MM-dd HH:mm:ss", message = "医生开方时间endTime格式错误 yyyy-MM-dd HH:mm:ss")
    private String endTime;

    private Long maxId = 0L;

    /**
     * 是否填充签名人
     */
    private boolean fillSignUser;

    public LocalDateTime[] getOutPrescriptionTime() {
        if (StringUtils.isNoneBlank(startTime, endTime)) {
            LocalDateTime start = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime end = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return new LocalDateTime[]{start, end};
        }
        return null;
    }


    public void paramValid() {
        if (getOutPrescriptionTime() == null) {
            throw new IllegalArgumentException("刷数据时间不能为空");
        }
    }


}