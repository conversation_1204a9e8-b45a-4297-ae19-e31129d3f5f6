package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 医院医生关系新增/修改 Request VO")
@Data
public class InquiryHospitalDoctorSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "1734")
    private Long id;

    @Schema(description = "医院ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2229")
    @NotNull(message = "医院ID不能为空")
    private Long hospitalId;

    @Schema(description = "医生ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11682")
    @NotNull(message = "医生ID不能为空")
    private Long doctorId;

    @Schema(description = "合作状态：0未合作 1 合作中 2禁用合作 3过期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "合作状态：0未合作 1 合作中 2禁用合作 3过期不能为空")
    private Integer cooperation;

    @Schema(description = "自动开方：0自动开方 1人工开方", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "自动开方：0自动开方 1人工开方不能为空")
    private Integer autoInquiry;

}