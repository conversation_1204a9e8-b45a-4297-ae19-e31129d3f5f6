package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 科室字典 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InquiryHospitalDepartmentRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "3483")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "科室编码,eg:101", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("科室编码")
    private String pref;

    @Schema(description = "科室名称,eg:内科", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("科室名称")
    private String deptName;

    @Schema(description = "父级科室id", requiredMode = Schema.RequiredMode.REQUIRED, example = "5502")
    @ExcelProperty("父级科室id")
    private Long deptParentId;


    @Schema(description = "科室序号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("科室序号")
    private Integer deptOrder;

    @Schema(description = "当前科室状态 0 启用 1 禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("科室状态")
    private Integer status;


    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}