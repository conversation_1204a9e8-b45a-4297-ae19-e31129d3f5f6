package com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

/**
 * @Author: xucao
 * @Date: 2024/12/30 14:11
 * @Description: 必须描述类做什么事情, 实现什么功能
 */
@Data
public class DoctorOperatFloorRespVO {

    @Schema(description = "患者编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "10001")
    @ExcelProperty("问诊单号")
    private String inquiryPref;

    @Schema(description = "患者编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "HZ10001")
    @ExcelProperty("患者编号")
    private String patientPref;

    @Schema(description = "患者姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李雷")
    @ExcelProperty("患者姓名")
    private String patientName;

    @Schema(description = "患者性别 1、男   2、女", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("患者性别")
    private Integer patientSex;

    @Schema(description = "患者年龄", requiredMode = Schema.RequiredMode.REQUIRED, example = "50")
    @ExcelProperty("患者年龄")
    private String patientAge;

    @Schema(description = "问诊方式  1、图文问诊  2、视频问诊  3、电话问诊 ", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("问诊方式")
    private Integer inquiryWayType;

    @Schema(description = "接诊状态：0 排队中 1、患者取消问诊 2 问诊中 3问诊结束 4医生取消开方 5问诊超时取消 ", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @ExcelProperty("接诊状态")
    private Integer inquiryStatus;

    @Schema(description = "是否派单 0、否 1、是", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("是否派单")
    private Integer isAutoGrabInquiry;


    @Schema(description = "过敏史", requiredMode = Schema.RequiredMode.REQUIRED, example = "头孢")
    @ExcelProperty("过敏史")
    private List<String> allergic;


    @Schema(description = "肝肾功能异常  0、无  1、肝功能异常  2、肾功能异常  3、肝肾功能异常", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @ExcelProperty("肝肾功能异常")
    private Integer liverKidneyValue;


    @Schema(description = "主诉", requiredMode = Schema.RequiredMode.REQUIRED, example = "头疼")
    @ExcelProperty("主诉")
    private List<String> mainSuit;

}
