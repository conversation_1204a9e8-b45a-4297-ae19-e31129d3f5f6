package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 医生工作履历记录新增/修改 Request VO")
@Data
public class DoctorWorkRecordSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "14539")
    private Long id;

    @Schema(description = "医生GUID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20902")
    private String guid;

    @Schema(description = "工作单位名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @Size(max = 128, message = "工作单位名称不能超过128个字符")
    private String workUnitName;

    @Schema(description = "职位", requiredMode = Schema.RequiredMode.REQUIRED)
    @Size(max = 32, message = "职位不能超过32个字符")
    private String jobPosition;

    @Schema(description = "证明人", requiredMode = Schema.RequiredMode.REQUIRED)
    @Size(max = 32, message = "证明人不超过32个字符")
    private String prover;

    @Schema(description = "开始时间,eg:2021-03-20")
    private LocalDateTime startDate;

    @Schema(description = "结束时间,eg:2029-03-20")
    private LocalDateTime endDate;

}