package com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.external.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.enums.transmitter.RequestStatusEnum;
import com.xyy.saas.inquiry.pojo.prescription.PrescriptionExternalExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

@Schema(description = "管理后台 - 外配(电子)处方记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SaasPrescriptionExternalRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "10157")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "外配处方编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("外配处方编号")
    private String pref;

    @Schema(description = "业务id", requiredMode = Schema.RequiredMode.REQUIRED, example = "19126")
    @ExcelProperty("业务id")
    private String bizId;

    @Schema(description = "业务类型 0-问诊,1-智慧脸...", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("业务类型 0-问诊,1-智慧脸...")
    private Integer bizType;

    @Schema(description = "三方业务渠道 0荷叶,1智慧脸 2海典erp 3众康云", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("三方业务渠道 0荷叶,1智慧脸 2海典erp 3众康云")
    private Integer bizChannelType;

    @Schema(description = "外配处方类型 0.电子处方、1.纸质处方", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("外配处方类型 0.电子处方、1.纸质处方")
    private Integer externalType;

    @Schema(description = "外部系统处方号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("外部系统处方号")
    private String externalRxPref;

    @Schema(description = "定点医疗机构编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("定点医疗机构编号")
    private String fixMedicalInstitutionsCode;

    @Schema(description = "定点医疗机构名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("定点医疗机构名称")
    private String fixMedicalInstitutionsName;

    @Schema(description = "开方时间")
    @ExcelProperty("开方时间")
    private LocalDateTime rxOutTime;

    @Schema(description = "有效截止时间")
    @ExcelProperty("有效截止时间")
    private LocalDateTime rxExpireTime;

    @Schema(description = "科室编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("科室编码")
    private String deptPref;

    @Schema(description = "科室名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("科室名称")
    private String deptName;

    @Schema(description = "医生编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("医生编号")
    private String doctorPref;

    @Schema(description = "医生姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("医生姓名")
    private String doctorName;

    @Schema(description = "药师编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("药师编码")
    private String pharmacistPref;

    @Schema(description = "药师姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("药师姓名")
    private String pharmacistName;

    @Schema(description = "患者编号")
    private String patientPref;

    @Schema(description = "患者姓名")
    private String patientName;

    @Schema(description = "患者身份证号")
    private String patientIdCard;

    @Schema(description = "患者性别：1 男 2 女")
    private Integer patientSex;

    @Schema(description = "患者年龄")
    private String patientAge;

    /**
     * 问诊开始时间
     */
    private LocalDateTime inquiryStartTime;


    @Schema(description = "上传时间")
    private LocalDateTime uploadTime;

    /**
     * {@link RequestStatusEnum}
     */
    @Schema(description = "上传状态")
    private Integer requestStatus;

    @Schema(description = "医保就诊id")
    private String medicalVisitId;

    @Schema(description = "处方类别")
    @ExcelProperty("处方类别")
    private Integer rxCategory;

    @Schema(description = "长期处方标识")
    @ExcelProperty("长期处方标识")
    private Integer longTerm;

    @Schema(description = "电子处方平台流水号")
    @ExcelProperty("电子处方平台流水号")
    private String electronicRxSn;

    @Schema(description = "电子处方外流状态 0待外流", example = "1")
    @ExcelProperty("电子处方外流状态 0待外流")
    private Integer outFlowStatus;

    @Schema(description = "电子处方签名验签流水号")
    @ExcelProperty("电子处方签名验签流水号")
    private String rxSignVerifySn;

    @Schema(description = "电子处方审核业务流水号")
    @ExcelProperty("电子处方审核业务流水号")
    private String rxChkBizSn;

    @Schema(description = "电子处方审核状态", example = "2")
    @ExcelProperty("电子处方审核状态")
    private Integer rxChkStatus;

    @Schema(description = "电子处方审核时间")
    @ExcelProperty("电子处方审核时间")
    private LocalDateTime rxChkTime;

    @Schema(description = "医保处方号")
    @ExcelProperty("医保处方号")
    private String medicareRxNo;

    @Schema(description = "医保处方追溯码")
    @ExcelProperty("医保处方追溯码")
    private String medicareRxTraceCode;

    @Schema(description = "医保处方状态 0初始,1有效 2失效", example = "2")
    @ExcelProperty("医保处方状态 0初始,1有效 2失效")
    private Integer medicareRxStatus;

    @Schema(description = "外配处方扩展字段")
    private PrescriptionExternalExtDto ext;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}