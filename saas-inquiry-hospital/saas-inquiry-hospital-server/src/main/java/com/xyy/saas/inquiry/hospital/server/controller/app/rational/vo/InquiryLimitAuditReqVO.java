package com.xyy.saas.inquiry.hospital.server.controller.app.rational.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 合理用药VO
 */
@Data
public class InquiryLimitAuditReqVO implements Serializable {

    @Schema(description = "当前诊断")
    private String birthday;

//    private String organSign;

    @Schema(description = "当前诊断")
    private String age;

    @Schema(description = "性别 1-男 2-女 0-不限")
    private int sex;

    @Schema(description = "1.肝功能异常限制、2.肾功能异常限制、3.肝肾功能异常限制")
    private Integer healthSituation;

    @Schema(description = "1.妊娠期限制、2.哺乳期限制、3.妊娠哺乳期限制")
    private Integer womenSituation;

    @Schema(description = "过敏成分名称集合")
    private List<String> integerList;

    @Schema(description = "诊断集合")
    private List<String> diagnosisNames;

    @Schema(description = "诊断编码集合")
    private List<String> diagnosisCodes;

    @Schema(description = "药品通用名集合")
    private List<String> drugs;

    @Schema(description = "药品集合")
    private List<RationalDrugVO> drugDtoList;

    @Schema(description = "药品类型")
    private Integer medicineType;

    @Schema(description = "租户id")
    private Long tenantId;

}
