package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName：InquiryHospitalDeptDoctorSaveReqVO
 * @Author: xucao
 * @Date: 2024/11/18 18:52
 * @Description: 医院科室添加医生
 */
@Schema(description = "管理后台 - 医院科室新增医生 Request VO")
@Data
public class InquiryHospitalDeptDoctorSaveReqVO implements Serializable {

    @Schema(description = "医院科室关系id", requiredMode = Schema.RequiredMode.REQUIRED, example = "23333")
    @NotNull(message = "医院科室关系id不能为空")
    private Long hospitalDeptRelationId;

    @Schema(description = "医生编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "23333")
    @NotEmpty(message = "医生编码不能为空")
    private String doctorPref;

    @Schema(description = "医生在当前医院的编码", example = "23333")
    private String doctorHospitalPref;

    /**
     * {@link com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum}
     */
    @Schema(description = "接诊方式", requiredMode = Schema.RequiredMode.REQUIRED, example = "23333")
    @NotNull(message = "接诊方式不能为空")
    private List<Integer> inquiryWayType;

    /**
     * {@link com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum}
     */
    @Schema(description = "自动接诊方式", requiredMode = Schema.RequiredMode.REQUIRED, example = "23333")
    private List<Integer> autoInquiryWayType;

    /**
     * 自动接诊时段， 隔开  eg:08:30-12:00，13:00-18:00
     */
    private List<String> autoInquiryTime;
}
