package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import com.xyy.saas.inquiry.pojo.HospitalDeptDto.Dept;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * @Author:chenxiaoyi
 * @Date:2024/09/30 10:46
 */
@Data
@Accessors(chain = true)
public class InquiryHospitalSettingVO implements Serializable {

    @Schema(description = "西成药问诊默认科室")
    private List<Dept> defaultInquiryWesternMedicineDept;

    @Schema(description = "中草药问诊默认科室")
    private List<Dept> defaultInquiryChineseMedicineDept;

    @Schema(description = "默认处方笺模板（西成药）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "默认处方模板（西成药）不能为空")
    private Long defaultWesternPrescriptionTemplate;

    @Schema(description = "默认处方笺模板（西成药）名称")
    private String defaultWesternPrescriptionTemplateName;

    @Schema(description = "默认处方笺模板（中草药）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "默认处方模板（中草药）不能为空")
    private Long defaultChinesePrescriptionTemplate;

    @Schema(description = "默认处方笺模板（中草药）名称")
    private String defaultChinesePrescriptionTemplateName;

    @Schema(description = "扩展配置")
    private InquiryHospitalSettingExtVO extend;

}
