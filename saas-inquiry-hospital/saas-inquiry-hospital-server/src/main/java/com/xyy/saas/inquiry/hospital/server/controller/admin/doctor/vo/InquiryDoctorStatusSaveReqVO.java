package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Data;

@Schema(description = "管理后台 - 医生出诊状态关系新增/修改 Request VO")
@Data
public class InquiryDoctorStatusSaveReqVO {

    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11224")
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @Schema(description = "自动抢单状态： 0、关闭 1、自动抢单", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "自动抢单状态不能为空")
    private Integer autoGrabStatus;

    @Schema(description = "接诊方式 1:图文 2:视频 3:电话", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "接诊方式不能为空")
    @Size(min = 1, message = "请至少选择一种接诊方式")
    private List<Integer> inquiryWayTypeItems;

}