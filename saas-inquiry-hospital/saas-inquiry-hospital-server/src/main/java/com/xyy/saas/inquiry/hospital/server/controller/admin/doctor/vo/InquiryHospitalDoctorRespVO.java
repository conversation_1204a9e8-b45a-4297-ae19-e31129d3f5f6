package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 医院医生关系 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InquiryHospitalDoctorRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "1734")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "医院ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2229")
    @ExcelProperty("医院ID")
    private Long hospitalId;

    @Schema(description = "医生GUID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11682")
    @ExcelProperty("医生GUID")
    private Long doctorId;

    @Schema(description = "合作状态：0未合作 1 合作中 2禁用合作 3过期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("合作状态：0未合作 1 合作中 2禁用合作 3过期")
    private Integer cooperation;

    @Schema(description = "自动开方：0自动开方 1人工开方", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("自动开方：0自动开方 1人工开方")
    private Integer autoInquiry;
}