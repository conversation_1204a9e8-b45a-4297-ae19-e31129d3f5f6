package com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 用户 Excel 导入 VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免用户导入有问题
public class InquiryDiagnosisExcelVO {


    @Schema(description = "诊断编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("诊断编码")
    private String diagnosisCode;

    @Schema(description = "诊断名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("诊断名称")
    private String diagnosisName;

    @Schema(description = "诊断类型：0-默认(西医),1-中医", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("诊断类型")
    private Integer diagnosisType;

    @Schema(description = "展示诊断名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("展示诊断名称")
    private String showName;

    @Schema(description = "状态 0启用 1禁用", example = "2")
    @ExcelProperty("状态")
    private Integer status;

    @Schema(description = "性别限制：0无限制,1限男,2限女", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("性别限制")
    private Integer sexLimit;

    // 失败原因
    private String errMsg;


}
