package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalBindReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalBindRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalEmployeeRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalUserSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalUserPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalAvailableRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalAvailablePageReqVO;
import com.xyy.saas.inquiry.hospital.server.service.hospital.HospitalEmployeeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;



import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;


/**
 * 医院员工管理控制器
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 医院员工管理")
@RestController
@RequestMapping("/hospital/employee")
@Validated
@Slf4j
public class HospitalEmployeeController {


    @Resource
    private HospitalEmployeeService hospitalEmployeeService;

    @PostMapping("/create")
    @Operation(summary = "创建医院员工")
    public CommonResult<Long> createHospitalEmployee(@Valid @RequestBody HospitalUserSaveReqVO createReqVO) {
        Long userId = hospitalEmployeeService.createHospitalEmployee(createReqVO);
        return CommonResult.success(userId);
    }


    @GetMapping("/page")
    @Operation(summary = "获得医院员工分页")
    public CommonResult<PageResult<HospitalEmployeeRespVO>> getEmployeesByHospitalPref(@Valid HospitalUserPageReqVO pageReqVO) {
        PageResult<HospitalEmployeeRespVO> pageResult = hospitalEmployeeService.getHospitalEmployeePage(pageReqVO);
        return success(pageResult);
    }



    @GetMapping("/bind-hospitals")
    @Operation(summary = "分页查询用户绑定的医院列表")
    public CommonResult<PageResult<HospitalBindRespVO>> getBindHospitalsByUserId(@Valid HospitalAvailablePageReqVO pageReqVO) {
        PageResult<HospitalBindRespVO> pageResult = hospitalEmployeeService.getBindHospitalsByUserId(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/available-hospitals")
    @Operation(summary = "分页查询当前用户可绑定的医院列表")
    public CommonResult<PageResult<HospitalAvailableRespVO>> getAvailableHospitalsByUserId(@Valid HospitalAvailablePageReqVO pageReqVO) {
        PageResult<HospitalAvailableRespVO> pageResult = hospitalEmployeeService.getAvailableHospitalsByUserId(pageReqVO);
        return success(pageResult);
    }



    @PostMapping("/bind")
    @Operation(summary = "绑定员工到医院")
    public CommonResult<Long> bindEmployeeToHospital(@Valid @RequestBody HospitalBindReqVO bindReqVO) {
        Long relationId = hospitalEmployeeService.bindEmployeeToHospital(bindReqVO);
        return CommonResult.success(relationId);
    }

    @DeleteMapping("/unbind")
    @Operation(summary = "解绑员工与医院关系")
    public CommonResult<Boolean> unbindEmployeeFromHospital(@RequestParam("bindId") Long bindId) {
        hospitalEmployeeService.unbindEmployeeFromHospital(bindId);
        return CommonResult.success(true);
    }



}