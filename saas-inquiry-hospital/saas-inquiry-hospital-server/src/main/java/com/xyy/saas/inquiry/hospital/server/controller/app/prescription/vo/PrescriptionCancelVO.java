package com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 处方取消VO
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/17 14:42
 */
@Data
public class PrescriptionCancelVO implements Serializable {

    @Schema(description = "问诊单号")
    @NotEmpty(message = "问诊单号不可为空")
    private String inquiryPref;

    @Schema(description = "无需开方原因")
    @NotEmpty(message = "无需开方原因不可为空")
    @Length(max = 255, message = "无需开方原因最大长度255")
    private String cancelReason;

    @Schema(description = "客户端渠类型 0、app  1、pc  2、小程序 ", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer clientChannelType;

    @Schema(description = "客户端系统类型 eg : ios  ,  android", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String clientOsType;
}
