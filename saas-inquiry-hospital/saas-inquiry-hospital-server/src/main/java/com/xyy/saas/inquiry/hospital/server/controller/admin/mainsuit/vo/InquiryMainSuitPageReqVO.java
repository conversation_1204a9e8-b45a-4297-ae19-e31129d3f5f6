package com.xyy.saas.inquiry.hospital.server.controller.admin.mainsuit.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 主诉信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InquiryMainSuitPageReqVO extends PageParam {

    @Schema(description = "主诉名称", example = "芋艿")
    private String mainSuitName;

    @Schema(description = "状态 0启用 1禁用", example = "2")
    private Integer status;

    @Schema(description = "性别限制：0无限制,1限男,2限女", example = "1")
    private Integer sexLimit;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}