package com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import com.xyy.saas.inquiry.enums.diagnosis.SexLimitEnum;
import com.xyy.saas.inquiry.enums.diagnosis.DiagnosisTypeEnum;
import com.xyy.saas.inquiry.enums.system.DictDataTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Schema(description = "管理后台 - 问诊诊断信息新增/修改 Request VO")
@Data
public class InquiryDiagnosisSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "5037")
    private Long id;

    @Schema(description = "诊断编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "诊断编码不能为空")
    @Length(max = 64, message = "诊断编码长度不能超过64")
    private String diagnosisCode;

    @Schema(description = "诊断名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "诊断名称不能为空")
    @Length(max = 64, message = "诊断名称长度不能超过64")
    private String diagnosisName;

    @Schema(description = "诊断类型：0-默认(西医),1-中医", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "诊断类型不能为空")
    @InEnum(value = DiagnosisTypeEnum.class)
    private Integer diagnosisType;

    @Schema(description = "展示诊断名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "展示诊断名称不能为空")
    @Length(max = 64, message = "展示诊断名称长度不能超过64")
    private String showName;

    @Schema(description = "状态 0启用 1禁用", example = "2")
    @NotNull(message = "状态不能为空")
    @InEnum(CommonStatusEnum.class)
    private Integer status;

    @Schema(description = "性别限制：0无限制,1限男,2限女", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "性别限制不能为空")
    @InEnum(value = SexLimitEnum.class)
    private Integer sexLimit;

    @Schema(description = "据类型：1-常规, 2-系统默认 3-推荐", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @InEnum(value = DictDataTypeEnum.class)
    private Integer dataType;

}