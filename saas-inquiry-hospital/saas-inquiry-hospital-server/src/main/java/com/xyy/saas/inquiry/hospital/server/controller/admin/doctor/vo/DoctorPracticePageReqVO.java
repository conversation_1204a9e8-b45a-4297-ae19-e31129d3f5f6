package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 医生执业信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DoctorPracticePageReqVO extends PageParam {

    @Schema(description = "医生ID", example = "12774")
    private Long doctorId;

    @Schema(description = "第一执业机构名称", example = "王五")
    private String firstPracticeName;

    @Schema(description = "第一执业机构等级，例如：0=三甲, 1=三乙 ...")
    private Integer firstPracticeLevel;

    @Schema(description = "第一执业机构科室id", example = "204")
    private Long deptId;

    @Schema(description = "科室id集合,包含子集节点", example = "204")
    private List<Long> deptIds;

    @Schema(description = "专业职称代码，例如：2")
    private String titleCode;

    @Schema(description = "专业职称名称，例如：副主任医师", example = "芋艿")
    private String titleName;

    @Schema(description = "专业职称证书编号")
    private String titleNo;

    @Schema(description = "专业职称证书取得时间，例如：2016-03-20")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] titleTime;

    @Schema(description = "开始执业时间，例如：2021-03-20")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] startPracticeTime;

    @Schema(description = "执业结束时间，例如：2029-03-20")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] endPracticeDate;

    @Schema(description = "执业证书号")
    private String professionalNo;

    @Schema(description = "执业证书取得时间，例如：2016-03-20")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] professionalTime;

    @Schema(description = "资格证书号")
    private String qualificationNo;

    @Schema(description = "资格证书取得时间，例如：2016-03-20")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] qualificationTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}