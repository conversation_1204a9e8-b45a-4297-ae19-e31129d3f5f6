package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 医生审核记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DoctorAuditedRecordRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "26750")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "审核人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("审核人姓名")
    private String auditorName;


    @Schema(description = "审核结果  1、审核通过  2、审核驳回", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("审核结果")
    private Integer auditResult;

    @Schema(description = "审核备注", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("审核备注")
    private String diffReason;

    @Schema(description = "审核时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("审核时间")
    private LocalDateTime auditTime;

}