package com.xyy.saas.inquiry.hospital.server.controller.app.doctor;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo.DoctorReviewsRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo.DoctorReviewsSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorReviewsDO;
import com.xyy.saas.inquiry.hospital.server.service.doctor.DoctorReviewsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "APP + PC - 接诊医生评价相关接口")
@RestController
@RequestMapping(value = {"/admin-api/kernel/hospital/doctor-reviews", "/app-api/kernel/hospital/doctor-reviews"})
@Validated
public class DoctorReviewsController {

    @Resource
    private DoctorReviewsService doctorReviewsService;

    @PostMapping("/create")
    @Operation(summary = "创建医生问诊评价")
    public CommonResult<Boolean> createDoctorReviews(@Valid @RequestBody DoctorReviewsSaveReqVO createReqVO) {
        return success(doctorReviewsService.createDoctorReviews(createReqVO));
    }


    @GetMapping("/get")
    @Operation(summary = "获得医生问诊评价")
    @Parameter(name = "inquiryPref", description = "问诊编码", required = true, example = "110335")
    public CommonResult<DoctorReviewsRespVO> getDoctorReviews(@RequestParam("inquiryPref") String inquiryPref) {
        DoctorReviewsDO doctorReviews = doctorReviewsService.getDoctorReviewsByInquiryPref(inquiryPref);
        return success(BeanUtils.toBean(doctorReviews, DoctorReviewsRespVO.class));
    }

}