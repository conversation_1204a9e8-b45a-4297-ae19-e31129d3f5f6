package com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.external.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.xyy.saas.inquiry.enums.transmitter.RequestStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 外配(电子)处方记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SaasPrescriptionExternalPageReqVO extends PageParam {

    @Schema(description = "外配处方编号")
    private String pref;

    @Schema(description = "业务id", example = "19126")
    private String bizId;

    @Schema(description = "业务类型 0-问诊,1-智慧脸...", example = "1")
    private Integer bizType;

    /**
     * 租户id
     */
    private Long tenantId;

    @Schema(description = "三方业务渠道 0荷叶,1智慧脸 2海典erp 3众康云", example = "2")
    private Integer bizChannelType;

    @Schema(description = "外配处方类型 0.电子处方、1.纸质处方", example = "1")
    private Integer externalType;

    @Schema(description = "外部系统处方号")
    private String externalRxPref;

    @Schema(description = "定点医疗机构编号")
    private String fixMedicalInstitutionsCode;

    @Schema(description = "定点医疗机构名称", example = "王五")
    private String fixMedicalInstitutionsName;

    @Schema(description = "开方时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] rxOutTime;

    @Schema(description = "有效截止时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] rxExpireTime;

    @Schema(description = "科室编码")
    private String deptPref;

    @Schema(description = "科室名称", example = "张三")
    private String deptName;

    @Schema(description = "医生编号")
    private String doctorPref;

    @Schema(description = "医生姓名", example = "芋艿")
    private String doctorName;

    @Schema(description = "药师编码")
    private String pharmacistPref;

    @Schema(description = "药师姓名", example = "李四")
    private String pharmacistName;

    private String patientPref;

    @Schema(description = "患者姓名")
    private String patientName;

    @Schema(description = "患者身份证号")
    private String patientIdCard;

    @Schema(description = "上传时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] uploadTime;

    /**
     * 上传状态 {@link RequestStatusEnum}
     */
    @Schema(description = "上传状态 0未请求,1请求中,2请求成功,3请求失败")
    private Integer requestStatus;

    @Schema(description = "医保就诊id")
    private String medicalVisitId;

    @Schema(description = "处方类别")
    private Integer rxCategory;

    @Schema(description = "长期处方标识")
    private Integer longTerm;

    @Schema(description = "电子处方平台流水号")
    private String electronicRxSn;

    @Schema(description = "电子处方外流状态 0待外流", example = "1")
    private Integer outFlowStatus;

    @Schema(description = "电子处方签名验签流水号")
    private String rxSignVerifySn;

    @Schema(description = "电子处方审核业务流水号")
    private String rxChkBizSn;

    @Schema(description = "电子处方审核状态", example = "2")
    private Integer rxChkStatus;

    @Schema(description = "电子处方审核时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] rxChkTime;

    @Schema(description = "医保处方号")
    private String medicareRxNo;

    @Schema(description = "医保处方追溯码")
    private String medicareRxTraceCode;

    @Schema(description = "医保处方状态 0初始,1有效 2失效", example = "2")
    private Integer medicareRxStatus;

    @Schema(description = "外配处方扩展字段")
    private String ext;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "处方类型")
    private Integer prescriptionType;
}