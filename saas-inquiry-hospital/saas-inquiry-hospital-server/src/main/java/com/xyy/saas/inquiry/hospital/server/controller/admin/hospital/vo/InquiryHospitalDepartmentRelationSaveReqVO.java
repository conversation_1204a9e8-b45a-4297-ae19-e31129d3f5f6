package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Schema(description = "管理后台 - 医院科室信息新增/修改 Request VO")
@Data
public class InquiryHospitalDepartmentRelationSaveReqVO implements Serializable {


    @Schema(description = "科室id", requiredMode = Schema.RequiredMode.REQUIRED, example = "23333")
    private Long deptId;

    @Schema(description = "医院编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "23333")
    @NotNull(message = "医院编码不能为空")
    private String hospitalPref;

    @Schema(description = "科室id集合", requiredMode = Schema.RequiredMode.REQUIRED, example = "23333")
    private List<Long> deptIds;

}