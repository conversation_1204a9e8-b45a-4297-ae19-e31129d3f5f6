package com.xyy.saas.inquiry.hospital.server.controller.admin.prescription;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionDetailRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionDetailSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.convert.prescription.InquiryPrescriptionDetailConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionDetailDO;
import com.xyy.saas.inquiry.hospital.server.service.prescription.InquiryPrescriptionDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 处方记录详情")
@RestController
@RequestMapping("/hospital/inquiry-prescription-detail")
@Validated
public class InquiryPrescriptionDetailController {

    @Resource
    private InquiryPrescriptionDetailService inquiryPrescriptionDetailService;

    @PutMapping("/update")
    @Operation(summary = "更新处方记录详情")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:update')")
    public CommonResult<Boolean> updateInquiryPrescriptionDetail(@Valid @RequestBody InquiryPrescriptionDetailSaveReqVO updateReqVO) {
        inquiryPrescriptionDetailService.updateInquiryPrescriptionDetail(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除处方记录详情")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:delete')")
    public CommonResult<Boolean> deleteInquiryPrescriptionDetail(@RequestParam("id") Long id) {
        inquiryPrescriptionDetailService.deleteInquiryPrescriptionDetail(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得处方记录详情")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:query')")
    public CommonResult<List<InquiryPrescriptionDetailRespVO>> getInquiryPrescriptionDetail(@RequestParam("pref") String pref) {
        final List<InquiryPrescriptionDetailDO> prescriptionDetailDOS = inquiryPrescriptionDetailService.getInquiryPrescriptionDetailsByPref(pref);
        return success(InquiryPrescriptionDetailConvert.INSTANCE.convertVos(prescriptionDetailDOS));
    }

}