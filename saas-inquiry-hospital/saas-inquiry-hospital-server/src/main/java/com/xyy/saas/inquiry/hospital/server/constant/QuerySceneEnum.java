package com.xyy.saas.inquiry.hospital.server.constant;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionDO;
import java.util.Arrays;
import lombok.Getter;

/**
 * @ClassName：QuerySceneEnum
 * @Author: xucao
 * @Date: 2024/10/30 20:25
 * @Description: 处方查询场景
 */
@Getter
public enum QuerySceneEnum {
    DRUGSTORE(0, "门店问诊处方", InquiryPrescriptionDO::getId),
    DOCTOR(1, "医生开具处方", InquiryPrescriptionDO::getId),
    PHARMACIST(2, "药师审核处方", InquiryPrescriptionDO::getAuditPrescriptionTime);

    private final int code;
    private final String desc;
    private final SFunction<InquiryPrescriptionDO, ?> sortField;

    QuerySceneEnum(int code, String desc ,SFunction<InquiryPrescriptionDO, ?> sortField) {
        this.code = code;
        this.desc = desc;
        this.sortField = sortField;
    }

    //根据code 查询对应的枚举
    public static QuerySceneEnum getEnumByCode(Integer code) {
        return Arrays.stream(QuerySceneEnum.values()).filter(item -> item.getCode() == code).findFirst().get();
    }
}
