package com.xyy.saas.inquiry.hospital.server.service.reception;

import com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo.DoctorOperatFloorResultVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import java.util.List;


/**
 * @Desc 医院接诊大厅
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/9/4 下午5:55
 */
public interface HospitalReceptionAreaService {

    /**
     * 给问诊分配医生
     *
     * @param inquiryRecordDto 问诊单信息
     */
    List<String> distributeDoctorForInquiry(InquiryRecordDto inquiryRecordDto);

    /**
     * 查询接诊大厅医生工作台问诊单列表
     *
     * @param status 状态
     * @return 接诊大厅医生工作台查询返参
     */
    DoctorOperatFloorResultVO getDoctorOperatFloorList(Integer status);


    /**
     * 医生从接诊大厅拉取问诊单
     * @param doctor 医生信息
     */
    void doctorPullInquiryFromReceptionArea(InquiryDoctorDO doctor);


    /**
     * 视频问诊完成后自动开方调度
     * @param inquiryPref 问诊单号
     * @return 调度结果
     */
    void autoVideoDispatch (String inquiryPref);
}
