package com.xyy.saas.inquiry.hospital.server.controller.admin.mainsuit;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.inquiry.hospital.server.controller.admin.mainsuit.vo.InquiryMainSuitPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.mainsuit.vo.InquiryMainSuitRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.mainsuit.vo.InquiryMainSuitSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.mainsuit.InquiryMainSuitDO;
import com.xyy.saas.inquiry.hospital.server.service.mainsuit.InquiryMainSuitService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "管理后台 - 主诉信息")
@RestController
@RequestMapping("/hospital/inquiry-main-suit")
@Validated
public class InquiryMainSuitController {

    @Resource
    private InquiryMainSuitService mainSuitService;

    @PostMapping("/create")
    @Operation(summary = "创建主诉信息")
    @PreAuthorize("@ss.hasPermission('hospital:main-suit:create')")
    public CommonResult<Long> createMainSuit(@Valid @RequestBody InquiryMainSuitSaveReqVO createReqVO) {
        return success(mainSuitService.createMainSuit(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新主诉信息")
    @PreAuthorize("@ss.hasPermission('hospital:main-suit:update')")
    public CommonResult<Boolean> updateMainSuit(@Valid @RequestBody InquiryMainSuitSaveReqVO updateReqVO) {
        mainSuitService.updateMainSuit(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除主诉信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hospital:main-suit:delete')")
    public CommonResult<Boolean> deleteMainSuit(@RequestParam("id") Long id) {
        mainSuitService.deleteMainSuit(id);
        return success(true);
    }

    @GetMapping("/get-by-id")
    @Operation(summary = "获得主诉信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hospital:main-suit:query')")
    public CommonResult<InquiryMainSuitRespVO> getMainSuit(@RequestParam("id") Long id) {
        InquiryMainSuitDO mainSuit = mainSuitService.getMainSuit(id);
        return success(BeanUtils.toBean(mainSuit, InquiryMainSuitRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得主诉信息分页")
    @PreAuthorize("@ss.hasPermission('hospital:main-suit:query')")
    public CommonResult<PageResult<InquiryMainSuitRespVO>> getMainSuitPage(@Valid InquiryMainSuitPageReqVO pageReqVO) {
        PageResult<InquiryMainSuitDO> pageResult = mainSuitService.getMainSuitPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InquiryMainSuitRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出主诉信息 Excel")
    @PreAuthorize("@ss.hasPermission('hospital:main-suit:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMainSuitExcel(@Valid InquiryMainSuitPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InquiryMainSuitDO> list = mainSuitService.getMainSuitPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "主诉信息.xls", "数据", InquiryMainSuitRespVO.class,
            BeanUtils.toBean(list, InquiryMainSuitRespVO.class));
    }

}