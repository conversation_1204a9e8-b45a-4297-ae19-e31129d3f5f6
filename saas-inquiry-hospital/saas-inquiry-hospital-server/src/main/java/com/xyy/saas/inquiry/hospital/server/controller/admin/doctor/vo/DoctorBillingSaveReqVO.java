package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "管理后台 - 医生收款信息新增/修改 Request VO")
@Data
public class DoctorBillingSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "25141")
    private Long id;

    @Schema(description = "医生GUID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21865")
    @NotEmpty(message = "医生GUID不能为空")
    private String guid;

    @Schema(description = "收款人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "收款人姓名不能为空")
    private String payeeName;

    @Schema(description = "收款人身份证号码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "收款人身份证号码不能为空")
    private String payeeIdCard;

    @Schema(description = "收款人手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "收款人手机号不能为空")
    private String payeeTelPhone;

    @Schema(description = "银行卡号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "银行卡号不能为空")
    private String payeeBankNo;

    @Schema(description = "开户行", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "开户行不能为空")
    private String payeeBankName;

}