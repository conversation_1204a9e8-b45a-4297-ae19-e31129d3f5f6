package com.xyy.saas.inquiry.hospital.server.controller.app.doctor;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo.DoctorOperatFloorResultVO;
import com.xyy.saas.inquiry.hospital.server.service.reception.HospitalReceptionAreaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: xucao
 * @Date: 2024/12/30 10:47
 * @Description: 医生工作台接口
 */

@Tag(name = "APP + PC - 医生工作台")
@RestController
@RequestMapping("/hospital/doctor-operat-floor")
@Validated
public class DoctorOperatFloorController {

    @Resource
    private HospitalReceptionAreaService hospitalReceptionAreaService;

    @GetMapping("/list")
    @Operation(summary = "查询医生工作台列表")
    public CommonResult<DoctorOperatFloorResultVO> getDoctorOperatFloorList(@RequestParam("status") Integer status) {
        return CommonResult.success(hospitalReceptionAreaService.getDoctorOperatFloorList(status));
    }
}
