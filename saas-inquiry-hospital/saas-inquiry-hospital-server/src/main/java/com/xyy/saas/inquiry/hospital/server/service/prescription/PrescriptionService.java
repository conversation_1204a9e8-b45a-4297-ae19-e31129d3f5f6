package com.xyy.saas.inquiry.hospital.server.service.prescription;


import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo.IssuesPrescriptionRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo.PrescriptionCancelVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo.PrescriptionGrabbingVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo.PrescriptionIssuesVO;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import jakarta.validation.Valid;

/**
 * 处方 - 医生 Service
 *
 * <AUTHOR>
 */
public interface PrescriptionService {

    /**
     *
     *
     * @param prescriptionGrabbingVO 抢处方VO
     */
    CommonResult<?> grabbingPrescriptionByDoctor(PrescriptionGrabbingVO prescriptionGrabbingVO);

    /**
     * 开具处方
     *
     * @param prescriptionIssuesVO 开具vo
     */
    CommonResult<IssuesPrescriptionRespVO> issuesPrescription(PrescriptionIssuesVO prescriptionIssuesVO);

    /**
     * 医生 无需开方
     *
     * @param prescriptionCancelVO - 取消处方vo
     */
    CommonResult<?> cancelPrescription(PrescriptionCancelVO prescriptionCancelVO);

    /**
     * 此接口目前场景
     * 1.在ai视频问诊中,在与ai医生对话过程中,还未结束ai对话就挂断电话
     *
     * @param prescriptionCancelVO
     * @return
     */
    CommonResult<?> autoCancelPrescription(@Valid PrescriptionCancelVO prescriptionCancelVO);

    /**
     * 处方开具超时检查
     *
     * @param inquiryRecordDto 问诊单信息
     */
    void prescriptionIssueTimeOutCheck(InquiryRecordDto inquiryRecordDto);

    /**
     * 处方开具超时时间 单位:分钟
     *
     * @return 超时分钟
     */
    Integer issuePrescriptionTimeOutConfig();
}