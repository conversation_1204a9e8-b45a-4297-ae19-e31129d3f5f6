package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.pojo.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 医院信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InquiryHospitalRespVO extends BaseDto {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "9640")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "医院编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "4346")
    @ExcelProperty("医院编码")
    private String pref;

    @Schema(description = "医院名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("医院名称")
    private String name;

    @Schema(description = "医疗机构编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "H404040")
    @ExcelProperty("医疗机构编码")
    private String institutionCode;

    @Schema(description = "医院等级", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("医院等级")
    private Integer level;

    @Schema(description = "医院地址")
    @ExcelProperty("医院地址")
    private String address;

    @Schema(description = "联系电话")
    @ExcelProperty("联系电话")
    private String phone;

    @Schema(description = "电子邮件")
    @ExcelProperty("电子邮件")
    private String email;

    @Schema(description = "官方网站")
    @ExcelProperty("官方网站")
    private String website;

    @Schema(description = "是否有医保资质", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否有医保资质")
    private Integer hasMedicare;

    @Schema(description = "医院配置信息")
    @ExcelIgnore
    private InquiryHospitalSettingVO setting;

    @Schema(description = "是否禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否禁用")
    private Integer disable;

}