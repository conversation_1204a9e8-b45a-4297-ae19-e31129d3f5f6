package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 医生备案信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DoctorFilingRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "8615")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "医生GUID", requiredMode = Schema.RequiredMode.REQUIRED, example = "26807")
    @ExcelProperty("医生GUID")
    private String guid;

    @Schema(description = "民族编码 1、汉族 ....", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("民族编码 1、汉族 ....")
    private Integer nationCode;

    @Schema(description = "民族名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("民族名称")
    private String nationName;

    @Schema(description = "通信地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("通信地址")
    private String address;

    @Schema(description = "学历，eg:1、博士研究生 2、硕士研究生 3、本科 ...")
    @ExcelProperty("学历，eg:1、博士研究生 2、硕士研究生 3、本科 ...")
    private Integer formalLevel;

    @Schema(description = "学历，eg:1、博士研究生 2、硕士研究生 3、本科 ...", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("学历，eg:1、博士研究生 2、硕士研究生 3、本科 ...")
    private String formalName;

    @Schema(description = "机构所在省份编码，eg: 420000", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("机构所在省份编码，eg: 420000")
    private String orgProvinceCode;

    @Schema(description = "机构所在省份名称，eg: 新疆维吾尔族自治区", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("机构所在省份名称，eg: 新疆维吾尔族自治区")
    private String orgProvinceName;

    @Schema(description = "备案状态 1、待审核  2、审核中  3、审核通过  4、审核驳回", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("备案状态 1、待审核  2、审核中  3、审核通过  4、审核驳回")
    private Integer recordStatus;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}