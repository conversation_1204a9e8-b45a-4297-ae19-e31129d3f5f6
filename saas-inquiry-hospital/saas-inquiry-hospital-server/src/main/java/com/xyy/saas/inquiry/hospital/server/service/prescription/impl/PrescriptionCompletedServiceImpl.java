package com.xyy.saas.inquiry.hospital.server.service.prescription.impl;

import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionQueryDTO;
import com.xyy.saas.inquiry.hospital.server.service.prescription.InquiryPrescriptionService;
import com.xyy.saas.inquiry.hospital.server.service.prescription.PrescriptionCompletedService;
import com.xyy.saas.inquiry.pojo.TenantDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

// 新增依赖导入
import cn.hutool.core.util.BooleanUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.hospital.api.medicare.dto.MedicareRegistrationDto;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.transmission.PrescriptionExternalTransmissionRespDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionRespVO;
import com.xyy.saas.inquiry.hospital.server.convert.prescription.external.PrescriptionExternalConvert;
import com.xyy.saas.inquiry.hospital.server.service.medical.MedicareService;
import com.xyy.saas.inquiry.hospital.server.service.prescription.InquiryPrescriptionService;
import com.xyy.saas.inquiry.hospital.server.service.prescription.PrescriptionCompletedService;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.transmitter.internet.PrescriptionTransmitterDTO;
import com.xyy.saas.transmitter.api.transmission.TransmissionApi;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionConfigReqDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionReqDTO;
import jakarta.annotation.Resource;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;



/**
 * 处方流程完成服务实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class PrescriptionCompletedServiceImpl implements PrescriptionCompletedService {

    @Autowired
    private InquiryPrescriptionService inquiryPrescriptionService;

    @DubboReference
    private InquiryApi inquiryApi;

    @DubboReference
    private TenantApi tenantApi;

    @DubboReference(retries = 0)
    private TransmissionApi transmissionApi;

    @Resource
    private MedicareService medicareService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handlePrescriptionCompleted(String prescriptionPref) {
        // 查询处方信息
        InquiryPrescriptionRespVO prescriptionRespVO = inquiryPrescriptionService.queryByCondition(
            InquiryPrescriptionQueryDTO.builder().pref(prescriptionPref).build()
        );

        if (Objects.isNull(prescriptionRespVO)) {
            return;
        }

        // 执行监管同步流程
        performSupervisionFlow(prescriptionRespVO);


    }

    /**
     * 执行监管同步流程 - 仿照externalSupervision方法 包含：3.1机构信息采集接口 -> 3.2病历数据采集接口
     *
     * @param prescriptionRespVO 处方信息
     */
    private void performSupervisionFlow(InquiryPrescriptionRespVO prescriptionRespVO) {
        // 获取问诊记录详情
        InquiryRecordDetailDto inquiryRecordDetail = inquiryApi.getInquiryRecordDetail(prescriptionRespVO.getInquiryPref());
        if (Objects.isNull(inquiryRecordDetail)) {
            return;
        }

        TenantDto tenant = tenantApi.getTenant(inquiryRecordDetail.getTenantId());
        // 构建传输数据
        PrescriptionTransmitterDTO transmitterDTO = PrescriptionExternalConvert.INSTANCE.convertPrescriptionTransmission(prescriptionRespVO, inquiryRecordDetail, tenant);

        // 机构信息采集
        performInstitutionInfoCollection(prescriptionRespVO, transmitterDTO);

        // 医保挂号登记
        // medicareVisitRegistration(prescriptionRespVO, transmitterDTO);
    }

    /**
     * 医保挂号登记 委托给医保服务处理
     *
     * @param transmitterDTO 传输数据
     */
    public void medicareVisitRegistration(PrescriptionTransmitterDTO transmitterDTO) {
        try {

            InquiryPrescriptionRespVO prescriptionRespVO = inquiryPrescriptionService.queryByCondition(InquiryPrescriptionQueryDTO.builder().pref(transmitterDTO.getPref()).build());
            if (prescriptionRespVO == null) {
                return;
            }

            // 2. 构建传输配置请求
            TransmissionConfigReqDTO configReqDTO = TransmissionConfigReqDTO.builder()
                .tenantId(prescriptionRespVO.getTenantId())
                .nodeType(NodeTypeEnum.INTERNET_SUPERVISION_REGISTRATION_FEEDBACK)
                .build();

            // 3. 构建传输请求
            TransmissionReqDTO transmissionReqDTO = TransmissionReqDTO.buildReq(configReqDTO, transmitterDTO);

            // 5. 业务逻辑校验
            CommonResult<Boolean> businessLogic = transmissionApi.validateBusinessLogic(transmissionReqDTO);
            if (businessLogic.isSuccess() && BooleanUtil.isTrue(businessLogic.getData())) {
                // 6. 执行合约调用
                CommonResult<MedicareRegistrationDto> commonResult =
                    transmissionApi.contractInvoke(transmissionReqDTO, MedicareRegistrationDto.class);
                if (commonResult.isSuccess() && Objects.nonNull(commonResult.getData())) {
                    // 7. 保存医保挂号登记信息
                    medicareService.saveMedicareRegistrationInfo(prescriptionRespVO, transmitterDTO, commonResult.getData());
                }
            }
        } catch (Exception e) {
            log.error("【医保挂号登记】处理异常，处方编号：{}, error:{}",
                transmitterDTO.getPref(), e.getMessage(), e);
        }
    }


    /**
     * 信息采集
     */
    private boolean performInstitutionInfoCollection(InquiryPrescriptionRespVO prescriptionRespVO,
        PrescriptionTransmitterDTO transmitterDTO) {
        try {
            // 构建传输配置请求
            TransmissionConfigReqDTO configReqDTO = TransmissionConfigReqDTO.builder()
                .tenantId(prescriptionRespVO.getTenantId())
                .nodeType(NodeTypeEnum.INTERNET_SUPERVISION_INSTITUTION_INFO_COLLECTION)
                .build();

            // 构建传输请求
            TransmissionReqDTO transmissionReqDTO = TransmissionReqDTO.buildReq(configReqDTO, transmitterDTO);

            // 1. 业务逻辑校验
            CommonResult<Boolean> businessLogic = transmissionApi.validateBusinessLogic(transmissionReqDTO);

            if (businessLogic.isSuccess() && BooleanUtil.isTrue(businessLogic.getData())) {
                log.info("【监管同步】机构信息采集调用, prescriptionPref:{}, request:{}",
                    prescriptionRespVO.getPref(), transmissionReqDTO);
                // 2. 执行合约调用
                CommonResult<PrescriptionExternalTransmissionRespDto> commonResult =
                    transmissionApi.contractInvoke(transmissionReqDTO, PrescriptionExternalTransmissionRespDto.class);

                log.info("【监管同步】机构信息采集调用结果, prescriptionPref:{}, result:{}",
                    prescriptionRespVO.getPref(), commonResult);

                return commonResult.isSuccess();
            }

            return false;
        } catch (Exception e) {
            log.error("【监管同步】机构信息采集异常, prescriptionPref:{}, error:{}",
                prescriptionRespVO.getPref(), e.getMessage(), e);
            return false;
        }
    }



}