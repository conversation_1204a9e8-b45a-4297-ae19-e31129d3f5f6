package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 医生收款信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DoctorBillingRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "25141")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "医生GUID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21865")
    @ExcelProperty("医生GUID")
    private String guid;

    @Schema(description = "收款人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("收款人姓名")
    private String payeeName;

    @Schema(description = "收款人身份证号码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("收款人身份证号码")
    private String payeeIdCard;

    @Schema(description = "收款人手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("收款人手机号")
    private String payeeTelPhone;

    @Schema(description = "银行卡号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("银行卡号")
    private String payeeBankNo;

    @Schema(description = "开户行", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("开户行")
    private String payeeBankName;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}