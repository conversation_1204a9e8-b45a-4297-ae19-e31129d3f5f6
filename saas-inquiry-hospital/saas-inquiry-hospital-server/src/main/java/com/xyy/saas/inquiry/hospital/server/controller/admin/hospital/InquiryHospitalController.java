package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalRespDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.service.hospital.InquiryHospitalService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "管理后台 - 医院信息")
@RestController
@RequestMapping("/hospital/inquiry-hospital")
@Validated
public class InquiryHospitalController {

    @Resource
    private InquiryHospitalService inquiryHospitalService;

    @PostMapping("/create")
    @Operation(summary = "创建医院信息")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-hospital:create')")
    public CommonResult<Long> createInquiryHospital(@Valid @RequestBody InquiryHospitalSaveReqVO createReqVO) {
        return success(inquiryHospitalService.createInquiryHospital(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新医院信息")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-hospital:update')")
    public CommonResult<Boolean> updateInquiryHospital(@Valid @RequestBody InquiryHospitalSaveReqVO updateReqVO) {
        inquiryHospitalService.updateInquiryHospital(updateReqVO);
        return success(true);
    }

    @PutMapping("/valid-special-ca")
    @Operation(summary = "更新医院信息-校验特定CA")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-hospital:update')")
    public CommonResult<String> validSpecialCa(@Valid @RequestBody InquiryHospitalSaveReqVO updateReqVO) {
        try {
            return inquiryHospitalService.validSpecialCa(updateReqVO);
        } catch (Exception e) {
            e.printStackTrace();
            return success("");
        }
    }


    @DeleteMapping("/delete")
    @Operation(summary = "删除医院信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-hospital:delete')")
    public CommonResult<Boolean> deleteInquiryHospital(@RequestParam("id") Long id) {
        inquiryHospitalService.deleteInquiryHospital(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得医院信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-hospital:query')")
    public CommonResult<InquiryHospitalRespVO> getInquiryHospital(@RequestParam("id") Long id) {
        InquiryHospitalRespVO inquiryHospital = inquiryHospitalService.convertDto2VO(inquiryHospitalService.getInquiryHospital(id));
        return success(inquiryHospital);
    }

    @GetMapping("/page")
    @Operation(summary = "获得医院信息分页")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-hospital:query')")
    public CommonResult<PageResult<InquiryHospitalRespVO>> getInquiryHospitalPage(@Valid InquiryHospitalPageReqVO pageReqVO) {
        PageResult<InquiryHospitalRespDto> pageResult = inquiryHospitalService.getInquiryHospitalPage(pageReqVO);
        List<InquiryHospitalRespVO> respVOS = inquiryHospitalService.convertDto2VOList(pageResult.getList());
        return success(new PageResult<>(respVOS, pageResult.getTotal()));
    }

    @GetMapping("/allList")
    @Operation(summary = "获得所有互联网医院列表")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-hospital:query')")
    public CommonResult<List<InquiryHospitalRespVO>> getInquiryHospitalAllList() {
        List<InquiryHospitalRespVO> list = inquiryHospitalService.convertDto2VOList(inquiryHospitalService.getInquiryHospitalListByDisable(CommonStatusEnum.ENABLE.getStatus()));
        return success(list);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出医院信息 Excel")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-hospital:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInquiryHospitalExcel(@Valid InquiryHospitalPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InquiryHospitalRespVO> list = inquiryHospitalService.convertDto2VOList(inquiryHospitalService.getInquiryHospitalPage(pageReqVO).getList());
        // 导出 Excel
        ExcelUtils.write(response, "医院信息.xls", "数据", InquiryHospitalRespVO.class, list);
    }

}