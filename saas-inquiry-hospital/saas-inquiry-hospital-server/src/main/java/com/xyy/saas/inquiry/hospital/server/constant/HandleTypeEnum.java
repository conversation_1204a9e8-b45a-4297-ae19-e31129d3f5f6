package com.xyy.saas.inquiry.hospital.server.constant;

import lombok.Getter;
import java.util.Arrays;

/**
 * @ClassName：HandleTypeEnum
 * @Author: xucao
 * @Description: 医生处理类型枚举 0 - 新增   1-修改
 */
@Getter
public enum HandleTypeEnum {
    ADD(0, "新增"),
    UPDATE(1, "修改");

    private final Integer code;
    private final String desc;

    HandleTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static HandleTypeEnum getEnumByCode(Integer code) {
        return Arrays.stream(values()).filter(item -> item.code.equals(code)).findFirst().orElse(null);
    }
}
