package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 医生备案信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DoctorFilingPageReqVO extends PageParam {

    @Schema(description = "医生ID", example = "26807")
    private Long doctorId;

    @Schema(description = "民族编码 1、汉族 ....")
    private Integer nationCode;

    @Schema(description = "民族名称", example = "赵六")
    private String nationName;

    @Schema(description = "通信地址")
    private String address;

    @Schema(description = "学历，eg:1、博士研究生 2、硕士研究生 3、本科 ...")
    private Integer formalLevel;

    @Schema(description = "学历，eg:1、博士研究生 2、硕士研究生 3、本科 ...", example = "芋艿")
    private String formalName;

    @Schema(description = "机构所在省份编码，eg: 420000")
    private String orgProvinceCode;

    @Schema(description = "机构所在省份名称，eg: 新疆维吾尔族自治区", example = "李四")
    private String orgProvinceName;

    @Schema(description = "备案状态 1、待审核  2、审核中  3、审核通过  4、审核驳回", example = "2")
    private Integer recordStatus;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}