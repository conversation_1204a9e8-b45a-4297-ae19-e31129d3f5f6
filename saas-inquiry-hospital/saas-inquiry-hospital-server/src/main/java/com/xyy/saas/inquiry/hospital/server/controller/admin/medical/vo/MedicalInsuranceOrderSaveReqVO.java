package com.xyy.saas.inquiry.hospital.server.controller.admin.medical.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

@Schema(description = "管理后台 - 医保订单信息新增/修改 Request VO")
@Data
public class MedicalInsuranceOrderSaveReqVO {

    @Schema(description = "主键", example = "2338")
    private Long id;

    @Schema(description = "医保订单编号")
    private String pref;

    @Schema(description = "业务id", example = "12998")
    @NotEmpty(message = "业务id不能为空")
    private String bizId;

    @Schema(description = "业务类型 0-问诊,1-智慧脸...", example = "1")
    @NotNull(message = "业务类型 0-问诊,1-智慧脸...不能为空")
    private Integer bizType;
    /**
     * 门店ID
     */
    private Long tenantId;

    @Schema(description = "定点机构编号")
    private String fixMedicalInstitutionsCode;

    @Schema(description = "结算ID", example = "29187")
    private String setlId;

    @Schema(description = "就诊ID", example = "9438")
    private String medicalId;

    @Schema(description = "人员编号")
    private String psnNo;

    @Schema(description = "人员姓名", example = "芋艿")
    private String psnName;

    @Schema(description = "人员证件类型", example = "1")
    private String psnCertType;

    @Schema(description = "证件号码")
    private String certNo;

    @Schema(description = "性别：1 男 2 女")
    private Integer sex;

    @Schema(description = "年龄")
    private String age;

    @Schema(description = "险种类型", example = "1")
    private String insuranceType;

    @Schema(description = "人员类别", example = "2")
    private String psnType;

    @Schema(description = "公务员标志")
    private String cvlServFlag;

    @Schema(description = "结算时间")
    private LocalDateTime setlTime;

    @Schema(description = "就诊凭证类型", example = "2")
    private String medicalCertType;

    @Schema(description = "医疗类别", example = "2")
    private String medType;

    @Schema(description = "医药机构结算ID", example = "20966")
    private String medicalInstitutionsSetlId;

    @Schema(description = "清算经办机构")
    private String clrOptions;

    @Schema(description = "清算方式")
    private String clrWay;

    @Schema(description = "清算类别", example = "2")
    private String clrType;

    @Schema(description = "本次交易后的账户余额")
    private BigDecimal balance;

    @Schema(description = "医疗费总额")
    private BigDecimal medicalFeeSumAmt;

    @Schema(description = "个人负担总金额")
    private BigDecimal psnPartAmt;

    @Schema(description = "基金支付总额")
    private BigDecimal fundPaySumAmt;

    @Schema(description = "其他支出")
    private BigDecimal othPay;

    @Schema(description = "医院负担金额")
    private BigDecimal hospPartAmt;

    @Schema(description = "个人账户支出")
    private BigDecimal acctPay;

    @Schema(description = "个人现金支出")
    private BigDecimal psnCashPay;

    @Schema(description = "个人账户共济支付金额")
    private BigDecimal acctMutualAidPay;

    @Schema(description = "医保订单扩展字段")
    private String ext;

}