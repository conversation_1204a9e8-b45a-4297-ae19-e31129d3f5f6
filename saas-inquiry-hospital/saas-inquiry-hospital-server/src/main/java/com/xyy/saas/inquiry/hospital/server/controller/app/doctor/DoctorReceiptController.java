package com.xyy.saas.inquiry.hospital.server.controller.app.doctor;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorStatusRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorStatusSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorStatusService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: xucao
 * @Date: 2024/12/27 15:47
 * @Description: 医生接诊出诊相关api
 */
@Tag(name = "APP + PC - 医生接诊相关api")
@RestController
@RequestMapping(value = {"/admin-api/kernel/hospital/doctor-receipt", "/app-api/kernel/hospital/doctor-receipt"})
@Validated
public class DoctorReceiptController {

    @Resource
    private InquiryDoctorStatusService inquiryDoctorStatusService;

    @PostMapping("/start-receipt")
    @Operation(summary = "医生出诊")
    public CommonResult<Boolean> startReceipt(@Valid @RequestBody InquiryDoctorStatusSaveReqVO saveReqVO) {
        return success(inquiryDoctorStatusService.startReceipt(saveReqVO));
    }

    @PutMapping("/stop-receipt")
    @Operation(summary = "医生停诊")
    public CommonResult<Boolean> stopReceipt(@RequestParam("userId") Long userId) {
        return success(inquiryDoctorStatusService.stopReceipt(userId));
    }

    @GetMapping("/get-scope")
    @Operation(summary = "查询医生接诊范围")
    public CommonResult<InquiryDoctorStatusRespVO> getDoctorReceiptScope(@RequestParam("userId") Long userId) {
        return success(inquiryDoctorStatusService.getDoctorReceiptScope(userId));
    }

    @PostMapping("/update-scope")
    @Operation(summary = "更新医生接诊范围")
    public CommonResult<Boolean> updateDoctorReceiptScope(@Valid @RequestBody InquiryDoctorStatusSaveReqVO reqVO) {
        return success(inquiryDoctorStatusService.updateDoctorReceiptScope(reqVO));
    }
}
