package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 医生工作履历记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DoctorWorkRecordRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "14539")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "医生GUID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20902")
    @ExcelProperty("医生GUID")
    private String guid;

    @Schema(description = "工作单位名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("工作单位名称")
    private String workUnitName;

    @Schema(description = "职位", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("职位")
    private String jobPosition;

    @Schema(description = "证明人", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("证明人")
    private String prover;

    @Schema(description = "开始时间,eg:2021-03-20")
    @ExcelProperty("开始时间")
    private LocalDateTime startDate;

    @Schema(description = "结束时间,eg:2029-03-20")
    @ExcelProperty("结束时间")
    private LocalDateTime endDate;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}