package com.xyy.saas.inquiry.hospital.server.controller.app.clinicalcase.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.pojo.inquiry.ClinicalCaseExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Schema(description = "管理后台 - 门诊病例 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InquiryClinicalCaseRespVO {

    @Schema(description = "主键", example = "4747")
    private Long id;

    @Schema(description = "病例号")
    private String pref;

    @Schema(description = "门店id")
    private Long tenantId;

    @Schema(description = "门店名称")
    private String tenantName;

    @Schema(description = "门诊号")
    private String iptOtpNo;

    @Schema(description = "业务就诊流水号")
    private String bizVisitId;

    @Schema(description = "就诊时间")
    private LocalDateTime startTime;

    /**
     * 用药类型：0西药  、1中药 {@link MedicineTypeEnum}
     */
    @Schema(description = "用药类型：0西药  、1中药")
    private Integer medicineType;

    @Schema(description = "问诊编号")
    private String inquiryPref;

    @Schema(description = "互联网医院编号")
    private String hospitalPref;

    @Schema(description = "互联网医院名称")
    private String hospitalName;

    @Schema(description = "医生编号")
    private String doctorPref;

    @Schema(description = "医生姓名")
    private String doctorName;

    @Schema(description = "医生所在当前医院编码")
    private String doctorHospitalPref;

    @Schema(description = "医生签章图片url")
    private String doctorSignatureUrl;

    @Schema(description = "科室编码")
    private String deptPref;

    @Schema(description = "科室编码")
    private String deptName;

    @Schema(description = "患者pref")
    private String patientPref;

    @Schema(description = "患者姓名", example = "赵六")
    private String patientName;

    @Schema(description = "患者身份证号")
    private String patientIdCard;

    @Schema(description = "患者年龄")
    private String patientAge;

    @Schema(description = "患者性别")
    private Integer patientSex;

    @Schema(description = "主诉")
    private List<String> mainSuit;

    @Schema(description = "过敏史")
    private List<String> allergic;

    @Schema(description = "既往史")
    private String patientHisDesc;

    @Schema(description = "现病史")
    private String currentIllnessDesc;

    @Schema(description = "复诊标识 0初次 1复诊")
    private Integer followUp;

    @Schema(description = "主要症状")
    private String mainSymptoms;

    @Schema(description = "处理措施")
    private String measures;

    @Schema(description = "是否需要留院观察 0否 1是")
    private Integer observation;

    @Schema(description = "转诊标识 0非转诊")
    private Integer referral;

    @Schema(description = "诊断编码")
    private List<String> diagnosisCode;

    @Schema(description = "诊断说明")
    private List<String> diagnosisName;

    @Schema(description = "中药西医诊断编码")
    private List<String> tcmDiagnosisCode;

    @Schema(description = "中药西医诊断说明")
    private List<String> tcmDiagnosisName;

    @Schema(description = "中医辨证代码")
    private String tcmSyndromeCode;

    @Schema(description = "中医辨证名称")
    private String tcmSyndromeName;

    @Schema(description = "中医治法代码")
    private String tcmTreatmentMethodCode;

    @Schema(description = "中医治法名称")
    private String tcmTreatmentMethodName;

    @Schema(description = "病例扩展字段")
    private ClinicalCaseExtDto ext;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}