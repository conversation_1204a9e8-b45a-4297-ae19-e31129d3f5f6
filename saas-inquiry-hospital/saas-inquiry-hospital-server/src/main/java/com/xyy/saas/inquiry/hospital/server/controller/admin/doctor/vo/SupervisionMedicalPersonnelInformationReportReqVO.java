package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Tag(name = "管理后台 - 医疗人员信息上报 Request VO")
@Data
@ToString(callSuper = true)
public class SupervisionMedicalPersonnelInformationReportReqVO implements Serializable {

    @Parameter(description = "医生ID集合", required = true)
    @NotEmpty(message = "医生ID集合不能为空")
    private List<Long> doctorIds;
}
