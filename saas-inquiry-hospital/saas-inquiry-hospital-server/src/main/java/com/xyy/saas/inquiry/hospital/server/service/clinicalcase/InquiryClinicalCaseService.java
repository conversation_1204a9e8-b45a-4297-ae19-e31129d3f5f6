package com.xyy.saas.inquiry.hospital.server.service.clinicalcase;

import com.xyy.saas.inquiry.hospital.server.controller.app.clinicalcase.vo.InquiryClinicalCaseRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.clinicalcase.vo.InquiryClinicalCaseSaveReqVO;

/**
 * 门诊病例 Service 接口
 *
 * <AUTHOR>
 */
public interface InquiryClinicalCaseService {

    /**
     * 创建门诊病例
     *
     * @param createReqVO 创建信息 saveInquiryClinicalCase
     * @return 编号
     */
    String saveInquiryClinicalCase(InquiryClinicalCaseSaveReqVO createReqVO);

    /**
     * 自动开方创建病例信息
     *
     * @param inquiryPref 问诊单号
     */
    void autoInquiryCreateClinicalCase(String inquiryPref);

    /**
     * 获得门诊病例
     *
     * @param inquiryPref 问诊编号
     * @return 门诊病例
     */
    InquiryClinicalCaseRespVO getInquiryClinicalCase(String inquiryPref);


}