package com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.external;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.external.vo.SaasPrescriptionExternalPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.external.vo.SaasPrescriptionExternalRespVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.external.SaasPrescriptionExternalDO;
import com.xyy.saas.inquiry.hospital.server.service.prescription.external.SaasPrescriptionExternalService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "管理后台 - 外配(电子)处方记录")
@RestController
@RequestMapping("/hospital/saas-prescription-external")
@Validated
public class SaasPrescriptionExternalController {

    @Resource
    private SaasPrescriptionExternalService saasPrescriptionExternalService;


    @GetMapping("/get")
    @Operation(summary = "获得外配(电子)处方记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hospital:saas-prescription-external')")
    public CommonResult<SaasPrescriptionExternalRespVO> getSaasPrescriptionExternal(@RequestParam("id") Long id) {
        SaasPrescriptionExternalDO saasPrescriptionExternal = saasPrescriptionExternalService.getSaasPrescriptionExternal(id);
        return success(BeanUtils.toBean(saasPrescriptionExternal, SaasPrescriptionExternalRespVO.class));
    }

    @PutMapping("/retry-upload")
    @Operation(summary = "重传外配(电子)处方记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hospital:saas-prescription-external')")
    public CommonResult<Boolean> retryUpload(@RequestParam("id") Long id) {
        saasPrescriptionExternalService.retryUpload(id);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得外配(电子)处方记录分页")
    @PreAuthorize("@ss.hasPermission('hospital:saas-prescription-external')")
    public CommonResult<PageResult<SaasPrescriptionExternalRespVO>> getSaasPrescriptionExternalPage(@Valid SaasPrescriptionExternalPageReqVO pageReqVO) {
        PageResult<SaasPrescriptionExternalRespVO> pageResult = saasPrescriptionExternalService.getSaasPrescriptionExternalPage(pageReqVO);
        return success(pageResult);
    }

}