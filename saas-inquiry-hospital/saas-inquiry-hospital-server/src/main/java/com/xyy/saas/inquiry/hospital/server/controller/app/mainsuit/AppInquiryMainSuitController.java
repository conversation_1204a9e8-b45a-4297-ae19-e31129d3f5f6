package com.xyy.saas.inquiry.hospital.server.controller.app.mainsuit;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.hospital.server.controller.admin.mainsuit.vo.InquiryMainSuitPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.mainsuit.vo.InquiryMainSuitRespVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.mainsuit.InquiryMainSuitDO;
import com.xyy.saas.inquiry.hospital.server.service.mainsuit.InquiryMainSuitService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "APP+PC - 主诉信息")
@RestController
@RequestMapping(value = {"/admin-api/kernel/hospital/inquiry-main-suit", "/app-api/kernel/hospital/inquiry-main-suit"})
@Validated
public class AppInquiryMainSuitController {

    @Resource
    private InquiryMainSuitService mainSuitService;


    @GetMapping("/get")
    @Operation(summary = "获得常用主诉列表")
    public CommonResult<List<InquiryMainSuitRespVO>> getMainSuit() {
        List<InquiryMainSuitDO> mainSuit = mainSuitService.getHotMainSuit();
        return success(BeanUtils.toBean(mainSuit, InquiryMainSuitRespVO.class));
    }

    @GetMapping("/pages")
    @Operation(summary = "获得主诉信息分页")
    public CommonResult<PageResult<InquiryMainSuitRespVO>> getMainSuitPage(@Valid InquiryMainSuitPageReqVO pageReqVO) {
        pageReqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        PageResult<InquiryMainSuitDO> pageResult = mainSuitService.getMainSuitPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InquiryMainSuitRespVO.class));
    }

}