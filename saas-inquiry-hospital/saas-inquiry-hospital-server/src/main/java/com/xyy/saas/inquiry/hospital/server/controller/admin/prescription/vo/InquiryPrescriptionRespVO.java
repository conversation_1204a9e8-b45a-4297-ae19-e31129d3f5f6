package com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xyy.saas.inquiry.annotation.InquiryDateType;
import com.xyy.saas.inquiry.enums.prescription.PrescriptionAuditWayTypeEnum;
import com.xyy.saas.inquiry.pojo.prescription.PrescriptionExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "管理后台 - 处方记录 Response VO")
@Data
@ExcelIgnoreUnannotated
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InquiryPrescriptionRespVO {

    @Schema(description = "处方ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27739")
    @ExcelProperty("处方ID")
    private Long id;

    @Schema(description = "处方编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1517")
    @ExcelProperty("处方编码")
    private String pref;

    @Schema(description = "租户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1517")
    @ExcelProperty("租户ID")
    private Long tenantId;

    @Schema(description = "租户名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "1517")
    @ExcelProperty("租户名称")
    private String tenantName;

    @Schema(description = "互联网医院编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "21843")
    @ExcelProperty("互联网医院编码")
    private String hospitalPref;

    @Schema(description = "互联网医院名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "21843")
    @ExcelProperty("互联网医院名称")
    private String hospitalName;

    @Schema(description = "医疗机构编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "21843")
    private String institutionCode;

    @Schema(description = "处方笺模版id", requiredMode = Schema.RequiredMode.REQUIRED, example = "21304")
    @ExcelProperty("处方笺模版id")
    private Long preTempId;

    @Schema(description = "科室编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("科室编码")
    private String deptPref;

    @Schema(description = "科室名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("科室名称")
    private String deptName;

    @Schema(description = "问诊编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "32287")
    @ExcelProperty("问诊编码")
    private String inquiryPref;

    @Schema(description = "病历编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "31005")
    @ExcelProperty("病历编码")
    private String diseaseCasesPref;

    @Schema(description = "患者编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "30337")
    @ExcelProperty("患者编码")
    private String patientPref;

    @Schema(description = "患者姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("患者姓名")
    private String patientName;

    @Schema(description = "患者性别：1 男 2 女", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("患者性别：1 男 2 女")
    private Integer patientSex;

    @Schema(description = "患者年龄", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("患者年龄")
    private String patientAge;

    @Schema(description = "患者手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("患者手机号")
    private String patientMobile;

    @Schema(description = "医生编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "20472")
    @ExcelProperty("医生编码")
    private String doctorPref;

    @Schema(description = "医师姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("医师姓名")
    private String doctorName;


    @Schema(description = "医生医保编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String doctorMedicareNo;

    @Schema(description = "药师编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "20472")
    @ExcelProperty("药师编码")
    private String pharmacistPref;

    @Schema(description = "药师姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "20472")
    @ExcelProperty("药师姓名")
    private String pharmacistName;

    @Schema(description = "是否自动开方：0 否  、 1是", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否自动开方：0 否  、 1是")
    private Integer autoInquiry;

    @Schema(description = "处方审核级数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("处方审核级数")
    private Integer auditLevel;

    @Schema(description = "处方状态  0、待开方   1、已取消   2、待审核     3、审核中  4、审核通过   5、审核驳回", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("处方状态  0、待开方   1、已取消   2、待审核     3、审核中  4、审核通过   5、审核驳回")
    private Integer status;

    /**
     * {@link com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum}
     */
    @Schema(description = "当前审方人类型 1-医生,2-药店,3-平台,4-医院", example = "2")
    private Integer auditorType;

    @Schema(description = "处方分发状态 0-未分配,1-已分配", example = "2")
    private Integer distributeStatus;

    @Schema(description = "处方分配的用户id", example = "2")
    private Long distributeUserId;

    @Schema(description = "用药类型：0西药，1中药", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("用药类型：0西药，1中药")
    private Integer medicineType;

    @Schema(description = "使用状态：0 初始 1可用 2已用 3过期  4失效", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("使用状态：0 初始 1可用 2已用 3过期  4失效")
    private Integer useStatus;

    @Schema(description = "失效原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "不好")
    @ExcelProperty("失效原因")
    private String invalidReason;

    @Schema(description = "失效时间")
    private LocalDateTime invalidTime;

    @InquiryDateType("invalidTime")
    @ExcelProperty("失效时间")
    private String invalidTimeStr;

    @Schema(description = "费别", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("费别")
    private String feeType;

    @Schema(description = "问诊开始时间")
    private LocalDateTime inquiryStartTime;

    @InquiryDateType("inquiryStartTime")
    @ExcelProperty("问诊开始时间")
    private String inquiryStartTimeStr;

    @Schema(description = "问诊结束时间")
    private LocalDateTime inquiryEndTime;

    @InquiryDateType("inquiryEndTime")
    @ExcelProperty("问诊结束时间")
    private String inquiryEndTimeStr;

    @Schema(description = "医师出方时间")
    private LocalDateTime outPrescriptionTime;

    @InquiryDateType("outPrescriptionTime")
    @ExcelProperty("医师出方时间")
    private String outPrescriptionTimeStr;

    @Schema(description = "药师审方时间")
    private LocalDateTime auditPrescriptionTime;

    @InquiryDateType("auditPrescriptionTime")
    @ExcelProperty("药师审方时间")
    private String auditPrescriptionTimeStr;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("订单号")
    private String orderNo;

    @Schema(description = "三方处方编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("三方处方编号")
    private String thirdPrescriptionNo;


    @Schema(description = "主诉", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("主诉")
    private List<String> mainSuit;

    @Schema(description = "诊断编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("诊断编码")
    private List<String> diagnosisCode;

    @Schema(description = "诊断说明", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("诊断说明")
    private List<String> diagnosisName;

    @Schema(description = "处方笺图片url", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("处方笺图片url")
    private String prescriptionImgUrl;

    @Schema(description = "处方笺PDFurl", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("处方笺PDFurl")
    private String prescriptionPdfUrl;

    @Schema(description = "病历img图片url", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("病历img图片url")
    private String caseImgUrl;

    @Schema(description = "问诊方式  1、图文问诊  2、视频问诊  3、电话问诊", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("问诊方式  1、图文问诊  2、视频问诊  3、电话问诊")
    private Integer inquiryWayType;

    @Schema(description = "问诊业务类型 1、药店问诊  2、远程审方", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("问诊业务类型 1、药店问诊  2、远程审方")
    private Integer inquiryBizType;

    /**
     * {@link PrescriptionAuditWayTypeEnum}
     */
    @Schema(description = "审方方式类别 1、药店问诊  2、远程审方", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("问诊业务类型 1、药店问诊  2、远程审方")
    private Integer auditWayType;

    @Schema(description = "客户端渠类型 0、app  1、pc  2、小程序 ", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("客户端渠类型 0、app  1、pc  2、小程序 ")
    private Integer clientChannelType;

    @Schema(description = "医生客户端类型 0：未知，10：微信小程序，11：微信公众号，20：H5 网页，31：手机 App", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("医生客户端类型 0：未知，10：微信小程序，11：微信公众号，20：H5 网页，31：手机 App")
    private Integer doctorChannelType;

    @Schema(description = "医生操作系统类型 0：未知，1：Android，2：iOS，3：Windows，4：Mac，5：Linux", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("医生操作系统类型 0：未知，1：Android，2：iOS，3：Windows，4：Mac，5：Linux")
    private String doctorOsType;

    @Schema(description = "问诊渠道 0、荷叶 1、智慧脸  2、海典ERP", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("问诊渠道 0、荷叶 1、智慧脸  2、海典ERP")
    private Integer bizChannelType;

    @Schema(description = "处方打印状态（0-未打印、1-已打印、NULL -未知）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("处方打印状态（0-未打印、1-已打印、NULL -未知）")
    private Integer printStatus;

    @Schema(description = "签章平台 0-自绘 1-法大大", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("签章平台 0-自绘 1-法大大")
    private Integer signPlatform;

    @Schema(description = "处方拓展字段", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("处方拓展字段")
    private PrescriptionExtDto ext;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @InquiryDateType("createTime")
    @ExcelProperty("创建时间")
    private String createTimeStr;

    /**
     * 处方类型
     */
    @Schema(description = "处方类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer prescriptionType;
    /**
     * 肝肾功能异常  0、无  1、肝功能异常  2、肾功能异常  3、肝肾功能异常
     */
    @Schema(description = "肝肾功能异常", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer liverKidneyValue;
    /**
     * 妊娠哺乳期   0、否  1、妊娠期   2、哺乳期  3、妊娠哺乳期
     */
    @Schema(description = "妊娠哺乳期", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer gestationLactationValue;

    //  问诊单详情*********************************
    /**
     * 患者身份证号码
     */
    private String patientIdCard;
    /**
     * 过敏史  eg：青霉素|头孢
     */
    private List<String> allergic;
    /**
     * 线下就医处方或病历图片
     */
    private List<String> offlinePrescriptions;

    /**
     * 数据状态 0 有效 1 作废 {@link CommonStatusEnum}
     */
    private Integer enable;

    @JsonIgnore
    public PrescriptionExtDto extGet() {
        if (ext == null) {
            ext = new PrescriptionExtDto();
        }
        return ext;
    }

}