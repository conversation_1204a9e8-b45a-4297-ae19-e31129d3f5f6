package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalSettingDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serializable;

@Schema(description = "管理后台 - 可绑定医院信息 Response VO")
@Data
public class HospitalAvailableRespVO implements Serializable {


    /**
     * 主键
     */
    @Schema(description = "医院编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;
    /**
     * 医院名称
     */
    @Schema(description = "医院名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    private String name;

    /**
     * 医疗机构编码
     */
    @Schema(description = "医疗机构编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "H404040")
    private String institutionCode;
    /**
     * 医院编码
     */
    @Schema(description = "医院编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1211254")
    private String pref;
    /**
     * 医院等级
     */
    @Schema(description = "医院等级")
    private Integer level;
    /**
     * 医院地址
     */
    @Schema(description = "医院地址")
    private String address;
    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    private String phone;
    /**
     * 电子邮件
     */
    @Schema(description = "电子邮件")
    private String email;
    /**
     * 官方网站
     */
    @Schema(description = "官方网站")
    private String website;
    /**
     * 是否有医保资质
     */
    @Schema(description = "是否有医保资质")
    private Integer hasMedicare;
    /**
     * 是否禁用
     */
    @Schema(description = "是否禁用")
    private Integer disable;


}