package com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 医生快捷回复语 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DoctorQuickReplyMsgRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "21432")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "父级id", example = "21577")
    @ExcelProperty("父级id")
    private Long parentId;

    @Schema(description = "GUID(医生)", requiredMode = Schema.RequiredMode.REQUIRED, example = "7595")
    @ExcelProperty("GUID(医生)")
    private String guid;

    @Schema(description = "分类标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("分类标题")
    private String title;

    @Schema(description = "常用语内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("常用语内容")
    private String content;

    @Schema(description = "排序")
    @ExcelProperty("排序")
    private Integer sorted;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}