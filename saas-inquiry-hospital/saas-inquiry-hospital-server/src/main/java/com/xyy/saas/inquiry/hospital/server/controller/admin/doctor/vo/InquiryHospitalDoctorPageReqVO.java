package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 医院医生关系分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InquiryHospitalDoctorPageReqVO extends PageParam {

    @Schema(description = "医院编码", example = "2229")
    private Integer hospitalPref;

    @Schema(description = "科室编码", example = "11682")
    private Long deptPref;

    @Schema(description = "医生编码", example = "11682")
    private Long doctorPref;

    @Schema(description = "合作状态：0未合作 1 合作中 2禁用合作 3过期")
    private Integer cooperation;

    @Schema(description = "自动开方：0自动开方 1人工开方")
    private Integer autoInquiry;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}