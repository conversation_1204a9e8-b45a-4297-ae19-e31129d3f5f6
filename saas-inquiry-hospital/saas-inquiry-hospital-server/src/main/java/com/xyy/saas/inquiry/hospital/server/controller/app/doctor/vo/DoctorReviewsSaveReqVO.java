package com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Schema(description = "管理后台 - 医生问诊评价新增/修改 Request VO")
@Data
public class DoctorReviewsSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "4994")
    private Long id;

    @Schema(description = "问诊单pref", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "问诊单pref不能为空")
    private String inquiryPref;

    @Schema(description = "处方编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "处方编号不能为空")
    private String prescriptionPref;

    @Schema(description = "医师编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "医师编码不能为空")
    private String doctorPref;

    @Schema(description = "满意度评分", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "满意度评分不能为空")
    private BigDecimal satisfactionScore;

    @Schema(description = "满意的点", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "满意的点不能为空")
    private List<String> satisfactionItem;

    @Schema(description = "评论内容", requiredMode = Schema.RequiredMode.REQUIRED)
    // @NotEmpty(message = "评论内容不能为空")
    @Size(max = 50, message = "评论内容长度不能超过 50个字符")
    private String reviewsContent;

}