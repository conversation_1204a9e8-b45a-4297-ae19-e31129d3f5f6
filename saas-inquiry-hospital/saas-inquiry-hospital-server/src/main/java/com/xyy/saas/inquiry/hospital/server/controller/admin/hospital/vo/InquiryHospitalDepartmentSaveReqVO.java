package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Schema(description = "管理后台 - 科室字典新增/修改 Request VO")
@Data
public class InquiryHospitalDepartmentSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "3483")
    private Long id;

    @Schema(description = "科室名称,eg:内科", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "科室名称,eg:内科不能为空")
    @Size(max = 64, message = "科室名称不能超过64个字符")
    private String deptName;

    @Schema(description = "父级科室id", requiredMode = Schema.RequiredMode.REQUIRED, example = "5502")
    @NotNull(message = "父级科室id不能为空")
    private Long deptParentId;

    @Schema(description = "科室序号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "科室序号不能为空")
    private Integer deptOrder;

    @Schema(description = "当前科室状态 0 启用 1 禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "当前科室状态 0 启用 1 禁用不能为空")
    private Integer status;

}