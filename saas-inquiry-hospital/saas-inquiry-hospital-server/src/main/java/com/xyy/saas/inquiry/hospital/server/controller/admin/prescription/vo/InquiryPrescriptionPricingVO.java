package com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 处方划价 VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class InquiryPrescriptionPricingVO {

    @Schema(description = "处方编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "处方编号不能为空")
    private String prescriptionPref;

    @Schema(description = "结算发票号")
    private String setlInvoiceNumber;

    @Schema(description = "处方总价")
    private BigDecimal pricingPrice;

    @Schema(description = "划价单据类型 1-暂存 2-确认")
    private Integer type;

    @Schema(description = "处方划价明细")
    private List<PrescriptionDetailPricingVO> detailPricingVos;


    @Data
    public static class PrescriptionDetailPricingVO {

        @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "2494")
        private Long id;

        @Schema(description = "商品单价", requiredMode = Schema.RequiredMode.REQUIRED, example = "8211")
        private BigDecimal productPrice;

    }


}