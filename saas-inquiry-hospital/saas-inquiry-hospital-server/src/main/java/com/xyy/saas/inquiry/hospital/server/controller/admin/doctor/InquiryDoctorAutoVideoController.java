package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorVideoPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorVideoRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorVideoSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorVideoDO;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorVideoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName：InquiryDoctorAutoVideoController
 * @Author: xucao
 * @Date: 2025/3/10 14:50
 * @Description: 医生录屏视频相关接口
 */
@Tag(name = "管理后台 - 医生录屏维护")
@RestController
@RequestMapping("/hospital/inquiry-doctor/auto-video")
@Validated
public class InquiryDoctorAutoVideoController {
    @Resource
    private InquiryDoctorVideoService inquiryDoctorVideoService;

    @PostMapping("/create")
    @Operation(summary = "创建医生录屏记录")
    public CommonResult<Long> createInquiryDoctorVideo(@Valid @RequestBody InquiryDoctorVideoSaveReqVO createReqVO) {
        return success(inquiryDoctorVideoService.createInquiryDoctorVideo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新医生录屏记录")
    public CommonResult<Boolean> updateInquiryDoctorVideo(@Valid @RequestBody InquiryDoctorVideoSaveReqVO updateReqVO) {
        inquiryDoctorVideoService.updateInquiryDoctorVideo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除医生录屏记录")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteInquiryDoctorVideo(@RequestParam("id") Long id) {
        inquiryDoctorVideoService.deleteInquiryDoctorVideo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得医生录屏记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<InquiryDoctorVideoRespVO> getInquiryDoctorVideo(@RequestParam("id") Long id) {
        InquiryDoctorVideoDO inquiryDoctorVideo = inquiryDoctorVideoService.getInquiryDoctorVideo(id);
        return success(BeanUtils.toBean(inquiryDoctorVideo, InquiryDoctorVideoRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得医生录屏记录分页")
    public CommonResult<PageResult<InquiryDoctorVideoRespVO>> getInquiryDoctorVideoPage(@Valid InquiryDoctorVideoPageReqVO pageReqVO) {
        PageResult<InquiryDoctorVideoDO> pageResult = inquiryDoctorVideoService.getInquiryDoctorVideoPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InquiryDoctorVideoRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出医生录屏记录 Excel")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInquiryDoctorVideoExcel(@Valid InquiryDoctorVideoPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InquiryDoctorVideoDO> list = inquiryDoctorVideoService.getInquiryDoctorVideoPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "医生录屏记录.xls", "数据", InquiryDoctorVideoRespVO.class,
            BeanUtils.toBean(list, InquiryDoctorVideoRespVO.class));
    }
}