package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 医院科室信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InquiryHospitalDepartmentRelationRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "17610")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "医院id", requiredMode = Schema.RequiredMode.REQUIRED, example = "23333")
    @ExcelProperty("医院id")
    private Long hospitalId;

    @Schema(description = "医院编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "23333")
    @ExcelProperty("医院编码")
    private String hospitalPref;

    @Schema(description = "科室id", requiredMode = Schema.RequiredMode.REQUIRED, example = "23333")
    @ExcelProperty("科室id")
    private Long deptId;

    @Schema(description = "科室编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("科室编码")
    private String deptPref;

    @Schema(description = "科室名称,eg:内科", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("科室名称,eg:内科")
    private String deptName;

    @Schema(description = "父级科室id,eg:0", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @ExcelProperty("父级科室id,eg:0")
    private Long deptParentId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}