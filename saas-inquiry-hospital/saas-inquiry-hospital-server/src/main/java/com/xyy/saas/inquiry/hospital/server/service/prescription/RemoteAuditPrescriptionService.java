package com.xyy.saas.inquiry.hospital.server.service.prescription;

import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionDO;
import java.util.List;

/**
 * @Author: xucao
 * @DateTime: 2025/4/24 14:46
 * @Description: 远程问诊服务类
 **/
public interface RemoteAuditPrescriptionService {

    /**
     * 保存远程问诊处方
     * @param prescriptionDO
     */
    void saveRemotePrescription(InquiryPrescriptionDO prescriptionDO);


    /**
     * 删除远程问诊处方
     * @param prescriptionDO
     */
    void delRemotePrescription(InquiryPrescriptionDO prescriptionDO);


    /**
     * 根据问诊单号查询处方信息
     * @param inquiryPref
     * @return
     */
    InquiryPrescriptionDO selectByInquiryPref(String inquiryPref);

    /**
     * 查询当前门店所有待审核的远程审方问诊单号
     * @param tenantId 租户id
     */
    List<String> selectRemoteAuditInquriyPrefList(Long tenantId);

    void batchDelRemotePrescription(List<InquiryPrescriptionDO> prescriptions);
}
