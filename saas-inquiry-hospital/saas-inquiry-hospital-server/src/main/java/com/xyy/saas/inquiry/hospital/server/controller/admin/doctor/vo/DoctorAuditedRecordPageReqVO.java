package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 医生审核记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DoctorAuditedRecordPageReqVO extends PageParam {

    @Schema(description = "医生id", example = "7866")
    private Long doctorId;

    @Schema(description = "审核人姓名", example = "赵六")
    private String auditorName;

    @Schema(description = "审核人工号", example = "31169")
    private String auditorId;

    @Schema(description = "审核类型 1、基本信息审核  2、备案信息审核  3、资质信息审核", example = "1")
    private Integer auditType;

    @Schema(description = "审核结果  1、审核通过  2、审核驳回", example = "1")
    private Integer auditStatus;

    @Schema(description = "审核时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] auditTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}