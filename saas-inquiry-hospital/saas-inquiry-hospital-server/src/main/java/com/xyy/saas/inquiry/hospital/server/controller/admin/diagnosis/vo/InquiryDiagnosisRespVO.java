package com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 问诊诊断信息 Response VO")
@Data
@Builder
@ExcelIgnoreUnannotated
public class InquiryDiagnosisRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "5037")
    private Long id;

    @Schema(description = "诊断编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("诊断编码")
    private String diagnosisCode;

    @Schema(description = "诊断名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("诊断名称")
    private String diagnosisName;


    @Schema(description = "展示名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("展示名称")
    private String showName;

    @Schema(description = "诊断类型：0-默认(西医),1-中医", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "诊断类型", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.DIAGNOSIS_TYPE)
    private Integer diagnosisType;


    @Schema(description = "状态 0启用 1禁用", example = "2")
    @DictFormat(DictTypeConstants.COMMON_STATUS)
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    private Integer status;

    @Schema(description = "性别限制：0无限制,1限男,2限女", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @DictFormat(DictTypeConstants.DIAGNOSIS_SEX_LIMIT)
    @ExcelProperty(value = "性别限制", converter = DictConvert.class)
    private Integer sexLimit;

    /**
     * 数据类型：1-常规, 2-系统默认 3-推荐
     */
    private Integer dataType;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}