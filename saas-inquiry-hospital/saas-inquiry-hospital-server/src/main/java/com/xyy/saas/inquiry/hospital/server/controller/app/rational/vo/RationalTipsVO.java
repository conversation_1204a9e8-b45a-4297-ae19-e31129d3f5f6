package com.xyy.saas.inquiry.hospital.server.controller.app.rational.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Desc 合理用药tips
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/11/06 11:31
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RationalTipsVO implements Serializable {

    @Schema(description = "当前诊断")
    private String diagnosName;

    @Schema(description = "冲突药品名称")
    private String secName;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "通用名")
    private String commonName;

    @Schema(description = "警示级别")
    private Integer caution;

    @Schema(description = "警示级别展示")
    private String cautionStr;

    @Schema(description = "禁忌类型")
    private Integer limitType;

    @Schema(description = "禁忌类型")
    private String limitTypeStr;

    @Schema(description = "提示语")
    private String desc;

    /**
     * {"code":0,"msg":"","result":[{"commonName":"配伍禁忌","caution":2,"cautionStr":"禁用","list":[{"caution":2,"cautionStr":"禁用","limitType":8,"limitTypeStr":"药品配伍","desc":"阿奇霉素和青霉素注射液，可造成胃痉挛"},{"caution":4,"cautionStr":"慎用","limitType":8,"limitTypeStr":"药品配伍","desc":"阿奇霉素和青霉素注射液，可造成胃痉挛"}]},{"commonName":"阿莫西林","caution":3,"cautionStr":"忌用","list":[{"commonName":"阿莫西林","caution":3,"cautionStr":"忌用","limitType":4,"limitTypeStr":"特定人群","desc":"肝功能异常人群忌用"}]},{"commonName":"阿莫西林","caution":1,"cautionStr":"提醒","list":[{"commonName":"阿莫西林","caution":1,"cautionStr":"提醒","limitType":2,"limitTypeStr":"特定人群","desc":"0天-1岁人群提醒"}]}]}
     */
    private List<RationalTipsVO> list;

}
