package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 医院科室信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InquiryHospitalDepartmentRelationPageReqVO extends PageParam {

    @Schema(description = "医院科室关系id", example = "23333")
    private Long hospitalDeptRelationId;

    /**
     * 医生姓名
     */
    private String doctorName;
    /**
     * 医生手机号
     */
    private String mobile;


    @Schema(description = "科室id", example = "23333")
    private Long deptId;

    @Schema(description = "医院id", example = "23333")
    private Long hospitalId;

    @Schema(description = "医院编码", example = "23333")
    private String hospitalPref;

    @Schema(description = "科室编码,eg:101")
    private String deptPref;

    @Schema(description = "医生编码,eg:101")
    private String doctorPref;

    @Schema(description = "科室名称,eg:内科", example = "王五")
    private String deptName;

    @Schema(description = "父级科室id", example = "12")
    private Long deptParentId;

    /**
     * {@link com.xyy.saas.inquiry.enums.doctor.DoctorInquiryTypeEnum}
     */
    @Schema(description = "开方类型", example = "0")
    private Integer inquiryType;

    /**
     * {@link com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum}
     */
    @Schema(description = "问诊方式", example = "0")
    private List<Integer> inquiryWayTypes;

    @Schema(description = "医院编码", example = "王五")
    private List<String> hospitalPrefs;

    /**
     * 医生编码
     */
    private List<String> doctorPrefs;


    /**
     * 类型,1医生,2药师
     */
    private Integer doctorType;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}