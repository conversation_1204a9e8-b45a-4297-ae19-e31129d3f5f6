package com.xyy.saas.inquiry.hospital.server.service.prescription;


import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_DOCTOR_DEPARTMENT_NOT_MATCH;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_PRESCRIPTION_SIGNATURE_ERROR;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.baomidou.lock.annotation.Lock4j;
import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.constant.PrescriptionConstant;
import com.xyy.saas.inquiry.drugstore.api.tenant.TenantParamConfigApi;
import com.xyy.saas.inquiry.enums.im.ImEventPushEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryStatusEnum;
import com.xyy.saas.inquiry.enums.registration.RegistrationStatusEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.enums.tenant.CostRecordTypeEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDto;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionDetailRespDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentRelationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo.IssuesPrescriptionRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo.PrescriptionCancelVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo.PrescriptionGrabbingVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo.PrescriptionIssuesVO;
import com.xyy.saas.inquiry.hospital.server.convert.diagnosis.InquiryDiagnosisConvert;
import com.xyy.saas.inquiry.hospital.server.convert.doctor.InquiryDoctorConvert;
import com.xyy.saas.inquiry.hospital.server.convert.prescription.InquiryPrescriptionConvert;
import com.xyy.saas.inquiry.hospital.server.convert.prescription.InquiryPrescriptionDetailConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryHospitalDeptDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.redis.RedisKeyConstants;
import com.xyy.saas.inquiry.hospital.server.dal.redis.doctor.DoctorRedisDao;
import com.xyy.saas.inquiry.hospital.server.dal.redis.inquiry.HospitalInquiryRedisDao;
import com.xyy.saas.inquiry.hospital.server.mq.message.prescription.PrescriptionIssueTimeOutCheckEvent;
import com.xyy.saas.inquiry.hospital.server.mq.message.prescription.dto.PrescriptionIssueTimeOutCheckMessage;
import com.xyy.saas.inquiry.hospital.server.mq.producer.inquiry.InquiryDoctorGrabbingPrescriptionProducer;
import com.xyy.saas.inquiry.hospital.server.mq.producer.inquiry.InquiryEndProducer;
import com.xyy.saas.inquiry.hospital.server.mq.producer.prescription.PrescriptionIssueTimeOutCheckProducer;
import com.xyy.saas.inquiry.hospital.server.service.diagnosis.InquiryDiagnosisService;
import com.xyy.saas.inquiry.hospital.server.service.doctor.DoctorPracticeService;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorService;
import com.xyy.saas.inquiry.hospital.server.service.hospital.InquiryHospitalDeptDoctorService;
import com.xyy.saas.inquiry.hospital.server.service.hospital.InquiryHospitalService;
import com.xyy.saas.inquiry.hospital.server.service.message.DoctorImService;
import com.xyy.saas.inquiry.hospital.server.service.prescription.dto.IssuesPrescriptionConfigDto;
import com.xyy.saas.inquiry.im.api.trtc.InquiryTrtcApi;
import com.xyy.saas.inquiry.mq.inquiry.InquiryDoctorGrabbingPrescriptionEvent;
import com.xyy.saas.inquiry.mq.inquiry.InquiryEndEvent;
import com.xyy.saas.inquiry.mq.inquiry.dto.InquiryDoctorGrabbingPrescriptionMessage;
import com.xyy.saas.inquiry.mq.inquiry.dto.InquiryEndMessage;
import com.xyy.saas.inquiry.mq.tenant.cost.InquiryReBackCostEvent;
import com.xyy.saas.inquiry.mq.tenant.cost.InquiryReBackProducer;
import com.xyy.saas.inquiry.mq.tenant.cost.TenantChangeCostDto;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.pojo.HospitalDeptDto.Dept;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.signature.api.prescription.InquirySignaturePrescriptionApi;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureInitDto;
import com.xyy.saas.transmitter.api.dict.TransmissionOrganDictApi;
import com.xyy.saas.transmitter.api.dict.dto.TransmissionOrganDictDTO;
import com.xyy.saas.transmitter.api.transmission.TransmissionConfigApi;
import com.xyy.saas.transmitter.enums.DictTypeConstants;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 处方 - 医生 Service
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class PrescriptionServiceImpl implements PrescriptionService {

    @DubboReference
    private InquirySignaturePrescriptionApi inquirySignaturePrescriptionApi;

    @DubboReference
    private InquiryApi inquiryApi;

    @DubboReference
    private TenantParamConfigApi tenantParamConfigApi;

    @Resource
    private TenantApi tenantApi;

    @Resource
    private ConfigApi configApi;

    @Resource
    private InquiryPrescriptionService inquiryPrescriptionService;

    @Resource
    private InquiryPrescriptionDetailService inquiryPrescriptionDetailService;

    @Resource
    private InquiryDoctorService inquiryDoctorService;

    @Resource
    private InquiryDiagnosisService inquiryDiagnosisService;

    @Resource
    private DoctorRedisDao doctorRedisDao;

    @Resource
    private HospitalInquiryRedisDao hospitalInquiryRedisDao;

    @Resource
    private PrescriptionIssueTimeOutCheckProducer prescriptionIssueTimeOutCheckProducer;

    @Resource
    private InquiryDoctorGrabbingPrescriptionProducer inquiryDoctorGrabbingPrescriptionProducer;

    @Resource
    private InquiryEndProducer inquiryEndProducer;

    @Resource
    private InquiryHospitalService inquiryHospitalService;

    @Resource
    private DoctorImService doctorImService;

    @Resource
    private DoctorPracticeService doctorPracticeService;

    @Resource
    private InquiryHospitalDeptDoctorService inquiryHospitalDeptDoctorService;

    @Resource
    private InquiryReBackProducer inquiryReBackProducer;

    @DubboReference
    private TransmissionConfigApi transmissionConfigApi;

    @DubboReference
    private TransmissionOrganDictApi transmissionOrganDictApi;

    private PrescriptionServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }

    @DubboReference
    private InquiryTrtcApi inquiryTrtcApi;


    @Override
    @Lock4j(keys = "'" + RedisKeyConstants.PRESCRIPTION_INQUIRY_LOCK_KEY + "'.concat(#prescriptionGrabbingVO.inquiryPref)")
    public CommonResult<?> grabbingPrescriptionByDoctor(PrescriptionGrabbingVO prescriptionGrabbingVO) {
        // 1.获取当前医生信息
        InquiryDoctorDO doctorDO = StringUtils.isNotBlank(prescriptionGrabbingVO.getDoctorPref()) ? inquiryDoctorService.getRequireInquiryDoctorByDoctorPref(prescriptionGrabbingVO.getDoctorPref())
            : inquiryDoctorService.getInquiryDoctorByUserId(WebFrameworkUtils.getLoginUserId());
        // 查询问诊单信息
        InquiryRecordDto inquiryRecordDto = inquiryApi.getInquiryDtoByPref(prescriptionGrabbingVO.getInquiryPref());

        // 2.检查当前问诊单是否可被接诊
        CommonResult commonResult = beforeGrabbingCheck(doctorDO, inquiryRecordDto);
        if (commonResult.isError()) {
            return commonResult;
        }
        TenantDto tenantDto = tenantApi.getTenant(inquiryRecordDto.getTenantId());
        // 定义接诊科室
        Dept dept = designatedDept(inquiryRecordDto, doctorDO);
        // 3.乐观锁修改问诊状态+医师信息
        if (!inquiryApi.doctorGrabbingInquiry(InquiryPrescriptionConvert.INSTANCE.convertDoctorGrabbingDto(inquiryRecordDto, doctorDO, prescriptionGrabbingVO, dept))) {
            return CommonResult.error("操作失败,请重试!");
        }
        log.info("医生抢单成功:inquiryPref:{},doctor:{}", prescriptionGrabbingVO.getInquiryPref(), doctorDO.getPref());
        // 4.操作redis，返回已派单医生列表
        List<String> doctorList = doctorRedisDao.doctorReception(inquiryRecordDto, tenantDto, doctorDO);
        doctorImService.batchNotifyDoctorForInquiryChange(doctorList);

        // 5.发送mq处理医生抢单成功事件,处理超时开方、变更通知第三方等
        sendTimeOutMq(prescriptionGrabbingVO, doctorDO);

        inquiryDoctorGrabbingPrescriptionProducer.sendMessage(
            InquiryDoctorGrabbingPrescriptionEvent.builder().msg(InquiryDoctorGrabbingPrescriptionMessage.builder().inquiryPref(prescriptionGrabbingVO.getInquiryPref()).registrationStatusEnum(RegistrationStatusEnum.PROGRESS).build())
                .build());
        // 6.通知门店

        InquiryRecordDetailDto recordDetailDto = inquiryApi.getInquiryRecordDetail(prescriptionGrabbingVO.getInquiryPref());
        return CommonResult.success(inquiryRecordDto.setInquiryRecordDetailDto(recordDetailDto));
    }

    /**
     * 发送mq处理医生抢单成功事件,处理超时开方、变更通知第三方等
     *
     * @param prescriptionGrabbingVO
     */
    private void sendTimeOutMq(PrescriptionGrabbingVO prescriptionGrabbingVO, InquiryDoctorDO doctorDO) {
        List<Integer> notifyConfig = issuePrescriptionTimeOutNotifyConfig();
        Integer timeOut = issuePrescriptionTimeOutConfig();
        prescriptionIssueTimeOutCheckProducer.sendMessage(
            PrescriptionIssueTimeOutCheckEvent.builder()
                .notifyConfigMessage(PrescriptionIssueTimeOutCheckMessage.builder().doctorPref(doctorDO.getPref()).inquiryPref(prescriptionGrabbingVO.getInquiryPref()).timeOutConfig(notifyConfig).build()).build(),
            LocalDateTime.now().plusSeconds((timeOut - notifyConfig.getFirst()) * 60L));
    }

    /**
     * 医生接诊前校验是否可接诊
     *
     * @param doctorDO
     * @param inquiryRecordDto
     * @return
     */
    private CommonResult beforeGrabbingCheck(InquiryDoctorDO doctorDO, InquiryRecordDto inquiryRecordDto) {
        // 1、检查当前问诊单是否存在
        if (ObjectUtil.isEmpty(inquiryRecordDto)) {
            return CommonResult.error("患者已取消当前问诊");
        }
        // 2、检查当前问诊是否已被其他医生抢单
        String currInquiryDoctor = hospitalInquiryRedisDao.getInquiryCurrentDoctor(inquiryRecordDto.getPref());
        if (StringUtils.isNotBlank(currInquiryDoctor) && !Objects.equals(currInquiryDoctor, doctorDO.getPref())) {
            return CommonResult.error("当前问诊已被其他医生接单");
        }

        // 3、检查当前问诊状态是否为排队中
        if (!Objects.equals(InquiryStatusEnum.QUEUING.getStatusCode(), inquiryRecordDto.getInquiryStatus())) {
            return CommonResult.error(InquiryStatusEnum.fromStatusCode(inquiryRecordDto.getInquiryStatus()).getDesc());
        }

        // 4、检查当前问诊是否在医生可接诊范围内
        List<String> inquiryPrefList = doctorRedisDao.getDoctorCanReceptionList(doctorDO.getPref(), doctorDO.getEnvTag());
        if (!inquiryRecordDto.isAutoInquiry() && !inquiryPrefList.contains(inquiryRecordDto.getPref())) {
            return CommonResult.error("当前问诊无法抢单");
        }

        // 5、检查医生已接诊数量
        List<String> receptionList = doctorRedisDao.getDoctorReceptionList(doctorDO.getPref(), doctorDO.getEnvTag());
        if (!CollectionUtils.isEmpty(receptionList) && receptionList.size() >= doctorRedisDao.getDoctorMaxReceptionNum()) {
            return CommonResult.error("你当前的进行中订单数量超过最大可接诊数量上限，请先完处理进行中的订单再来接诊哦~");
        }
        return CommonResult.success("校验通过");
    }

    /**
     * 医生接诊时候，指定接诊科室
     *
     * @param inquiryRecordDto 问诊单
     * @param doctorDO         医生信息
     * @return
     */
    private Dept designatedDept(InquiryRecordDto inquiryRecordDto, InquiryDoctorDO doctorDO) {
        // 1.获取当前问诊单调度的科室
        List<String> deptPrefList = hospitalInquiryRedisDao.getInquiryDispatchDeptList(inquiryRecordDto.getPref());
        // 2.获取当前医生在此医院接诊的科室
        List<InquiryHospitalDeptDoctorDO> doctorDeptList = inquiryHospitalDeptDoctorService.selectList(
            InquiryHospitalDepartmentRelationPageReqVO.builder().doctorPref(doctorDO.getPref()).hospitalPref(inquiryRecordDto.getHospitalPref()).build());
        InquiryHospitalDeptDoctorDO deptDoctorDO = doctorDeptList.stream().filter(d -> deptPrefList.contains(d.getDeptPref())).toList().getFirst();
        if (deptDoctorDO == null) {
            throw exception(INQUIRY_DOCTOR_DEPARTMENT_NOT_MATCH);
        }
        return Dept.builder().deptPref(deptDoctorDO.getDeptPref()).deptName(deptDoctorDO.getDeptName()).build();
    }

    @Override
    @Lock4j(keys = "'" + RedisKeyConstants.PRESCRIPTION_INQUIRY_LOCK_KEY + "'.concat(#prescriptionIssuesVO.inquiryPref)")
    public CommonResult<IssuesPrescriptionRespVO> issuesPrescription(PrescriptionIssuesVO prescriptionIssuesVO) {
        // 1.校验问诊状态是否可开方
        InquiryRecordDto inquiryRecordDto = inquiryApi.getInquiryRecord(prescriptionIssuesVO.getInquiryPref());
        if (!Objects.equals(InquiryStatusEnum.INQUIRING.getStatusCode(), inquiryRecordDto.getInquiryStatus())) {
            return CommonResult.error(InquiryStatusEnum.fromStatusCode(inquiryRecordDto.getInquiryStatus()).getDesc());
        }
        // 2. 开具处方 - 获取处方信息
        CommonResult<InquiryPrescriptionRespDTO> issuesPrescriptionResult = getSelf().issuesPrescription(prescriptionIssuesVO, inquiryRecordDto);
        if (issuesPrescriptionResult.isError()) {
            return CommonResult.error(issuesPrescriptionResult.getCode(), issuesPrescriptionResult.getMsg());
        }
        InquiryPrescriptionRespDTO prescriptionRespDTO = issuesPrescriptionResult.getData();

        // 3.发送问诊结束事件
        inquiryEndProducer.sendMessage(InquiryEndEvent.builder().msg(InquiryEndMessage.builder().inquiryPref(prescriptionIssuesVO.getInquiryPref()).build()).build());

        // 4.发送开具处方相关IM消息
        doctorImService.sendPrescriptionImMessage(prescriptionRespDTO, prescriptionIssuesVO.getInquiryPref(), ImEventPushEnum.ISSUE_PRESCRIPTION);

        // 5.通知门店
        return CommonResult.success(InquiryPrescriptionConvert.INSTANCE.convertDTO2VO(prescriptionRespDTO));
    }

    /**
     * 开具处方
     *
     * @param prescriptionIssuesVO
     * @param inquiryRecordDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<InquiryPrescriptionRespDTO> issuesPrescription(PrescriptionIssuesVO prescriptionIssuesVO, InquiryRecordDto inquiryRecordDto) {
        TenantDto tenantDto = tenantApi.getTenant(inquiryRecordDto.getTenantId());
        // 1.校验获取医生
        InquiryDoctorDO doctorDO = getDoctorInfo(inquiryRecordDto, prescriptionIssuesVO);
        if (doctorDO == null) {
            return CommonResult.error("当前问诊单已被其他医师接单");
        }

        InquiryRecordDetailDto inquiryRecordDetailDto = inquiryApi.getInquiryRecordDetail(prescriptionIssuesVO.getInquiryPref());
        // 2.组合必要参数 (选取处方笺模板id + 医师信息)
        IssuesPrescriptionConfigDto issuesPrescriptionConfigDto = inquiryHospitalService.getHospitalPrescriptionConfigByInquiry(inquiryRecordDto, inquiryRecordDetailDto);
        if (issuesPrescriptionConfigDto == null || issuesPrescriptionConfigDto.getPrescriptionTemplateId() == null) {
            return CommonResult.error("选取不到处方笺模板,请联系管理员");
        }
        InquiryPrescriptionConvert.INSTANCE.fillInquiryRecordIssues(inquiryRecordDto, issuesPrescriptionConfigDto, doctorDO);

        // 3.根据问诊信息和医生开方入参 创建处方+处方明细
        InquiryPrescriptionSaveReqVO prescriptionSaveReqVO = InquiryPrescriptionConvert.INSTANCE.convertSaveVO(inquiryRecordDto, inquiryRecordDetailDto, prescriptionIssuesVO, tenantParamConfigApi.getTenantPresDateType(tenantDto.getId()));
        // 选择处方同步签章的三方平台id
        prescriptionSaveReqVO.setSignPlatform(choosePrescriptionSignaturePlatform(issuesPrescriptionConfigDto));

        InquiryPrescriptionRespDTO prescriptionRespDTO = inquiryPrescriptionService.createInquiryPrescription(prescriptionSaveReqVO);
        // 保存处方明细
        List<InquiryPrescriptionDetailRespDTO> prescriptionDetailRespDTOS = inquiryPrescriptionDetailService.batchCreateInquiryPrescriptionDetail(
            InquiryPrescriptionDetailConvert.INSTANCE.convertSaveVOs(prescriptionRespDTO, inquiryRecordDto, inquiryRecordDetailDto, prescriptionIssuesVO));

        log.info("医生创建处方,调用签章:inquiryPref:{},doctor:{}", prescriptionIssuesVO.getInquiryPref(), doctorDO.getPref());
        // 4. 调用签章平台处理
        CommonResult<?> issuePrescriptionResult = handleSignaturePrescription(prescriptionRespDTO, prescriptionDetailRespDTOS, doctorDO, inquiryRecordDetailDto, issuesPrescriptionConfigDto);
        if (issuePrescriptionResult.isError()) {
            throw exception(INQUIRY_PRESCRIPTION_SIGNATURE_ERROR, issuePrescriptionResult.getMsg());
        }
        // 5.回更医师信息及状态
        inquiryApi.updateInquiry(inquiryRecordDto.setEndTime(inquiryRecordDto.getEndTime() == null ? LocalDateTime.now() : inquiryRecordDto.getEndTime()).setInquiryStatus(InquiryStatusEnum.ENDED.getStatusCode()));
        // 6.操作redis (归还医师)
        doctorRedisDao.doctorOverInquiry(inquiryRecordDto, tenantDto, doctorDO);

        return CommonResult.success(prescriptionRespDTO);
    }

    /**
     * 选择处方同步签章的三方平台id
     */
    private Integer choosePrescriptionSignaturePlatform(IssuesPrescriptionConfigDto issuesPrescriptionConfigDto) {
        String value = configApi.getConfigValueByKey(PrescriptionConstant.PRESCRIPTION_SIGNATURE_DEFAULT_SYNC_PLATFORM);

        return Objects.equals(SignaturePlatformEnum.SELF, SignaturePlatformEnum.fromCode(issuesPrescriptionConfigDto.getSignaturePlatform())) ? NumberUtils.toInt(value) : issuesPrescriptionConfigDto.getSignaturePlatform();
    }


    private InquiryDoctorDO getDoctorInfo(InquiryRecordDto inquiryRecordDto, PrescriptionIssuesVO prescriptionIssuesVO) {
        String doctorPref = hospitalInquiryRedisDao.getInquiryCurrentDoctor(prescriptionIssuesVO.getInquiryPref());
        if (StringUtils.isEmpty(doctorPref)) {
            return null;
        }
        InquiryDoctorDO doctorDO =
            inquiryRecordDto.isAutoInquiry() ? inquiryDoctorService.getRequireInquiryDoctorByDoctorPref(inquiryRecordDto.getDoctorPref()) : inquiryDoctorService.getInquiryDoctorByUserId(prescriptionIssuesVO.getDoctorUserId());
        if (!StringUtils.equals(doctorPref, doctorDO.getPref())) {
            return null;
        }
        return doctorDO;
    }

    /**
     * 转化参数调用签章平台
     *
     * @param prescriptionRespDTO 处方
     * @param detailRespDTOS      处方明细
     * @param doctorDO            医师
     * @param inquiryRecordDetail 问诊详情(中药用量)
     * @return
     */
    public CommonResult<?> handleSignaturePrescription(InquiryPrescriptionRespDTO prescriptionRespDTO, List<InquiryPrescriptionDetailRespDTO> detailRespDTOS, InquiryDoctorDO doctorDO, InquiryRecordDetailDto inquiryRecordDetail,
        IssuesPrescriptionConfigDto issuesPrescriptionConfigDto) {
        try {
            // 填充处方笺医生其他信息
            InquiryDoctorDto doctorDto = InquiryDoctorConvert.INSTANCE.convertToDto(doctorDO);
            Optional.ofNullable(doctorPracticeService.getDoctorPracticeByDoctorId(doctorDto.getId())).ifPresent(p -> {
                doctorDto.setDoctorMedicareNo(p.getDoctorMedicareNo());
            });
            // 查询诊断信息
            List<InquiryDiagnosisRespVO> diagnosisRespVOS = queryDiagnosis(prescriptionRespDTO);

            PrescriptionSignatureInitDto signatureInitDto = convertToSignatureInitDto(prescriptionRespDTO, detailRespDTOS, doctorDto, inquiryRecordDetail, issuesPrescriptionConfigDto, diagnosisRespVOS);

            // 签发处方
            return TenantUtils.execute(prescriptionRespDTO.getTenantId(), () -> inquirySignaturePrescriptionApi.issuePrescription(signatureInitDto));
        } catch (Exception e) {
            log.error("签章平台调用过程中发生异常,pref:{}", prescriptionRespDTO.getPref(), e);
            return CommonResult.error(e.getMessage());
        }
    }

    private List<InquiryDiagnosisRespVO> queryDiagnosis(InquiryPrescriptionRespDTO prescriptionRespDTO) {
        if (CollUtil.isEmpty(prescriptionRespDTO.getDiagnosisCode())) {
            return Collections.emptyList();
        }
        // 走诊断匹配的数据源
        Integer organId = transmissionConfigApi.diagnosisChangeQueryCatalog(prescriptionRespDTO.getTenantId(), prescriptionRespDTO.getPrescriptionType());

        // 查询诊断是否切换数据源,如果是，这里是三方的诊断编码
        if (organId == null) {
            return inquiryDiagnosisService.queryInquiryDiagnosis(InquiryDiagnosisDto.builder().diagnosisCodes(prescriptionRespDTO.getDiagnosisCode()).build());
        }

        List<TransmissionOrganDictDTO> organDictDTOS = transmissionOrganDictApi.queryDictList(
            TransmissionOrganDictDTO.builder().organId(organId).dictType(DictTypeConstants.DIAGNOSIS_DICT).values(prescriptionRespDTO.getDiagnosisCode()).build());

        log.info("诊断走三方数据源,问诊单号：{},诊断：{}", prescriptionRespDTO.getInquiryPref(), organDictDTOS);

        return InquiryDiagnosisConvert.INSTANCE.convertDictDtoRespVos(organDictDTOS);
    }

    private PrescriptionSignatureInitDto convertToSignatureInitDto(InquiryPrescriptionRespDTO prescriptionRespDTO, List<InquiryPrescriptionDetailRespDTO> detailRespDTOS, InquiryDoctorDto doctorDto,
        InquiryRecordDetailDto inquiryRecordDetail, IssuesPrescriptionConfigDto issuesPrescriptionConfigDto, List<InquiryDiagnosisRespVO> diagnosisRespVOS) {

        PrescriptionSignatureInitDto signatureInitDto = InquiryPrescriptionConvert.INSTANCE.convertSignatureInitDto(prescriptionRespDTO, detailRespDTOS, doctorDto, inquiryRecordDetail, issuesPrescriptionConfigDto);

        signatureInitDto.getParam().setDiagnosis(diagnosisRespVOS.stream()
            .map(d -> d.getShowName() + " " + d.getDiagnosisCode()).collect(Collectors.joining("|")));

        return signatureInitDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(keys = "'" + RedisKeyConstants.PRESCRIPTION_INQUIRY_LOCK_KEY + "'.concat(#prescriptionCancelVO.inquiryPref)")
    public CommonResult<?> cancelPrescription(PrescriptionCancelVO prescriptionCancelVO) {
        // 校验问诊状态是否可取消
        InquiryRecordDto inquiryRecordDto = inquiryApi.getInquiryRecord(prescriptionCancelVO.getInquiryPref());
        if (!Objects.equals(InquiryStatusEnum.INQUIRING.getStatusCode(), inquiryRecordDto.getInquiryStatus())) {
            return CommonResult.error(InquiryStatusEnum.fromStatusCode(inquiryRecordDto.getInquiryStatus()).getDesc());
        }
        TenantDto tenantDto = tenantApi.getTenant(inquiryRecordDto.getTenantId());
        InquiryDoctorDO doctorDO = inquiryDoctorService.getInquiryDoctorByUserId(WebFrameworkUtils.getLoginUserId());
        // 取消开方
        InquiryRecordDto recordDto = InquiryRecordDto.builder().id(inquiryRecordDto.getId()).inquiryStatus(InquiryStatusEnum.DOCTOR_CANCELED.getStatusCode()).cancelReason(prescriptionCancelVO.getCancelReason())
            .endTime(inquiryRecordDto.getEndTime() == null ? LocalDateTime.now() : inquiryRecordDto.getEndTime()).build();
        if (!inquiryApi.updateInquiry(recordDto)) {
            return CommonResult.error("操作失败,请重试!");
        }
        log.info("医生取消开方:inquiryPref:{},doctor:{}", prescriptionCancelVO.getInquiryPref(), doctorDO.getPref());
        // 操作redis (归还医师)
        doctorRedisDao.doctorOverInquiry(inquiryRecordDto, tenantDto, doctorDO);
        // 发送问诊结束的mq
        inquiryEndProducer.sendMessage(InquiryEndEvent.builder().msg(InquiryEndMessage.builder().inquiryPref(prescriptionCancelVO.getInquiryPref()).build()).build());
        // 通知门店
        doctorImService.sendPrescriptionImMessage(null, prescriptionCancelVO.getInquiryPref(), ImEventPushEnum.CANCEL_PRESCRIPTION);
        return CommonResult.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(keys = "'" + RedisKeyConstants.PRESCRIPTION_AUTO_INQUIRY_LOCK_KEY + "'.concat(#prescriptionCancelVO.inquiryPref)")
    public CommonResult<?> autoCancelPrescription(PrescriptionCancelVO prescriptionCancelVO) {
        // 校验问诊状态是否可取消
        InquiryRecordDto inquiryRecordDto = inquiryApi.getInquiryRecord(prescriptionCancelVO.getInquiryPref());
        if (!Objects.equals(InquiryStatusEnum.INQUIRING.getStatusCode(), inquiryRecordDto.getInquiryStatus())) {
            return CommonResult.error(InquiryStatusEnum.fromStatusCode(inquiryRecordDto.getInquiryStatus()).getDesc());
        }
        TenantDto tenantDto = tenantApi.getTenant(inquiryRecordDto.getTenantId());
        InquiryDoctorDO doctorDO = inquiryDoctorService.getInquiryDoctorByDoctorPref(inquiryRecordDto.getDoctorPref());
        if (doctorDO == null) {
            return CommonResult.error("医生还未接诊,不能取消问诊!");
        }
        // 取消开方
        InquiryRecordDto recordDto = InquiryRecordDto.builder().id(inquiryRecordDto.getId()).inquiryStatus(InquiryStatusEnum.DOCTOR_CANCELED.getStatusCode()).cancelReason(prescriptionCancelVO.getCancelReason())
            .endTime(inquiryRecordDto.getEndTime() == null ? LocalDateTime.now() : inquiryRecordDto.getEndTime()).build();
        if (!inquiryApi.updateInquiry(recordDto)) {
            return CommonResult.error("操作失败,请重试!");
        }
        log.info("Auto医生取消开方:inquiryPref:{},doctor:{}", prescriptionCancelVO.getInquiryPref(), doctorDO.getPref());
        // 操作redis (归还医师)
        doctorRedisDao.doctorOverInquiry(inquiryRecordDto, tenantDto, doctorDO);
        // 发送问诊结束的mq
        inquiryEndProducer.sendMessage(InquiryEndEvent.builder().msg(InquiryEndMessage.builder().inquiryPref(prescriptionCancelVO.getInquiryPref()).build()).build());
        // 通知门店
        doctorImService.sendPrescriptionImMessage(null, prescriptionCancelVO.getInquiryPref(), ImEventPushEnum.CANCEL_PRESCRIPTION);
        return CommonResult.success(true);
    }

    @Override
    @Lock4j(keys = "'" + RedisKeyConstants.PRESCRIPTION_INQUIRY_LOCK_KEY + "'.concat(#inquiryRecordDto.pref)")
    @TraceNode(node = TraceNodeEnum.PRESCRIPTION_ISSUE_TIME_OUT, prefLocation = "inquiryRecordDto.pref")
    public void prescriptionIssueTimeOutCheck(InquiryRecordDto inquiryRecordDto) {
        TenantDto tenantDto = tenantApi.getTenant(inquiryRecordDto.getTenantId());
        // 视频问诊开方超时需要解散视频聊天直播间
        if (inquiryRecordDto.isVideoInquiry()) {
            inquiryTrtcApi.destroyRoom(inquiryRecordDto.getPref());
        }
        InquiryDoctorDO doctorDO = inquiryDoctorService.getRequireInquiryDoctorByDoctorPref(inquiryRecordDto.getDoctorPref());
        InquiryRecordDto recordDto = InquiryRecordDto.builder().id(inquiryRecordDto.getId()).inquiryStatus(InquiryStatusEnum.TIMEOUT_CANCELED.getStatusCode()).cancelReason(InquiryStatusEnum.TIMEOUT_CANCELED.getDesc())
            .endTime(inquiryRecordDto.getEndTime() == null ? LocalDateTime.now() : inquiryRecordDto.getEndTime()).build();
        inquiryApi.updateInquiry(recordDto);
        // 操作redis (归还医师)
        doctorRedisDao.doctorOverInquiry(inquiryRecordDto, tenantDto, doctorDO);
        // 发送问诊结束的mq
        inquiryEndProducer.sendMessage(InquiryEndEvent.builder().msg(InquiryEndMessage.builder().inquiryPref(inquiryRecordDto.getPref()).build()).build());
        // 通知门店
        doctorImService.sendPrescriptionImMessage(null, inquiryRecordDto.getPref(), ImEventPushEnum.ISSUE_PRESCRIPTION_TIMEOUT);
        // 发送mq 加回额度
        inquiryReBackProducer.sendMessage(
            InquiryReBackCostEvent.builder().msg(TenantChangeCostDto.builder().bizId(inquiryRecordDto.getPref()).recordType(CostRecordTypeEnum.INQUIRY.getCode()).reBackRecordType(CostRecordTypeEnum.INQUIRY_CANAL.getCode()).build())
                .build());
    }

    @Override
    public Integer issuePrescriptionTimeOutConfig() {
        return NumberUtil.parseInt(configApi.getConfigValueByKey(PrescriptionConstant.PRESCRIPTION_ISSUE_TIMEOUT), 30);
    }

    /**
     * 获取超时提醒配置
     *
     * @return
     */
    public List<Integer> issuePrescriptionTimeOutNotifyConfig() {
        Integer timeOut = NumberUtil.parseInt(configApi.getConfigValueByKey(PrescriptionConstant.PRESCRIPTION_ISSUE_TIMEOUT), 30);
        String config = configApi.getConfigValueByKey(PrescriptionConstant.PRESCRIPTION_ISSUE_TIMEOUT_NOTIFY);
        if (StringUtils.isBlank(config)) {
            // 未配置超时提醒通知情况下则返回超时检测
            return Collections.singletonList(0);
        }
        // 将config通过，分割，并转为Integer 类型的list，并过滤掉>timeOut的元素后，降序排列
        return Arrays.stream(config.split(",")).map(Integer::parseInt).filter(i -> i < timeOut && i > 0).distinct().sorted().collect(Collectors.toList()).reversed();
    }

    public static void main(String[] args) {
        String aa = "3,8,9,5,17,2";
        System.out.println(Arrays.stream(aa.split(",")).map(Integer::parseInt).filter(i -> i < 10 && i > 0).distinct().sorted().collect(Collectors.toList()).reversed());
    }
}