package com.xyy.saas.inquiry.hospital.server.controller.app.rational.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2023/11/06 18:40
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InquiryRationalDictConfigPageVo extends PageParam {

    /**
     * 名称 eg:新生儿/抗生素
     */
    private String name;

}
