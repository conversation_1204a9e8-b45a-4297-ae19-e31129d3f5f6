package com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName：InquiryPrescriptionCountVO
 * @Author: xucao
 * @Date: 2024/10/31 19:43
 * @Description: 处方统计数据VO模型
 */
@Schema(description = "app - 处方记录统计 VO")
@Data
public class InquiryPrescriptionCountVO {

    @Schema(description = "昨日处方数量")
    private Long yesterdayPrescriptionCount;

    @Schema(description = "昨日处方数量")
    private Long monthPrescriptionCount;

    @Schema(description = "昨日处方数量")
    private Long totalPrescriptionCount;

    @Schema(description = "月审方数量")
    private Long monthAuditPrescriptionCount;

    @Schema(description = "总审方数量")
    private Long totalAuditPrescriptionCount;
}
