package com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName：DoctorQuickReplyMsgGroupRespVO
 * @Author: xucao
 * @Date: 2024/11/07 15:54
 * @Description: 医生快捷回复语分组
 */
@Schema(description = "管理后台 - 医生快捷回复语分组 Response VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DoctorQuickReplyMsgGroupRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "id")
    private Long id;

    @Schema(description = "分组名", requiredMode = Schema.RequiredMode.REQUIRED, example = "问诊中")
    private String title;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer sorted;

    @Schema(description = "组内常用语", requiredMode = Schema.RequiredMode.REQUIRED, example = "111")
    private List<DoctorQuickReplyMsgRespVO> itemList;
}
