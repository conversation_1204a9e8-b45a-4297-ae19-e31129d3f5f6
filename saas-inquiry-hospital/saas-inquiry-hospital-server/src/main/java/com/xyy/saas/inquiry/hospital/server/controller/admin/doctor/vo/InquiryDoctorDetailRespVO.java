package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.hospital.enums.DoctorFillingStatusEnum;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDeptDoctorConfigRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Schema(description = "管理后台 - 医生信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InquiryDoctorDetailRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "20449")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "医生编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1276")
    @ExcelProperty("医生编码")
    private String pref;

    @Schema(description = "医生名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("医生名称")
    private String name;

    @Schema(description = "性别 1男 2女", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("性别")
    private Integer sex;

    @Schema(description = "身份证号码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("身份证号码")
    private String idCard;

    @Schema(description = "手机号")
    @ExcelProperty("手机号")
    private String mobile;

    @Schema(description = "审核状态")
    @ExcelProperty("审核状态")
    private Integer auditStatus;

    @Schema(description = "第一职业机构")
    @ExcelProperty("第一职业机构")
    private String firstPracticeName;

    @Schema(description = "第一职业机构等级")
    @ExcelProperty("第一职业机构等级")
    private Integer firstPracticeLevel;

    @Schema(description = "医生类型： 1全职医生 2兼职医生", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("医生类型")
    private Integer jobType;

    @Schema(description = "否开启图文自动抢单： 0否 1是", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("否开启图文自动抢单")
    private Integer autoGrabStatus;

    @Schema(description = "合作状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("合作状态")
    private Integer cooperation;

    @Schema(description = "专业职称")
    @ExcelProperty("专业职称")
    private Integer titleCode;

    @Schema(description = "职称名称")
    @ExcelProperty("职称名称")
    private String titleName;

    @Schema(description = "第一职业机构科室")
    @ExcelProperty("第一职业机构科室")
    private String firstPracticeDeptName;

    @Schema(description = "医生渠道", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("医生渠道")
    private Integer canal;

    @Schema(description = "邀请人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("邀请人姓名")
    private String inviterName;

    @Schema(description = "邀请人工号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("邀请人工号")
    private String inviterNo;

    @Schema(description = "医生医保编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("医生医保编码")
    private String doctorMedicareNo;

    /**
     * 陕西监管备案状态 0:未备案 1:已备案 see {@link DoctorFillingStatusEnum}
     */
    @Schema(description = "陕西监管备案状态 0:未备案 1:已备案")
    @ExcelProperty("陕西监管备案状态")
    private Integer fillingStatus4ShaanxiRegulatory;

    @Schema(description = "擅长专业,eg:擅长神经内科诊疗", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("擅长专业")
    private String professionalDec;

    @Schema(description = "个人简介", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("个人简介")
    private String biography;

    @Schema(description = "医院医生关系列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<InquiryHospitalDeptDoctorConfigRespVO> hospitalDoctorItems;

    @Schema(description = "头像", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("头像")
    private String photo;

    @Schema(description = "查证结果", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("查证结果")
    private String verifiyImgUrl;

    @Schema(description = "职称证", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("职称证")
    private String titleImgUrl;

    @Schema(description = "个人证明", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("个人证明")
    private String personalImgUrl;

    @Schema(description = "执业证", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("执业证")
    private List<String> occupationImgUrls;

    @Schema(description = "资格证", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("资格证")
    private List<String> qualificationImgUrls;

    @Schema(description = "海南合同", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("海南合同")
    private List<String> hnContractImgUrls;

    @Schema(description = "成都合同", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("成都合同")
    private List<String> cdContractImgUrls;

    @Schema(description = "武汉合同", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("武汉合同")
    private List<String> whContractImgUrls;

    @Schema(description = "医生电子签章", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("医生电子签章")
    private String doctorElectronSignChapterUrl;

    @Schema(description = "医生电子签名", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("医生电子签名")
    private String doctorElectronSignImgUrl;

    @Schema(description = "身份证正面", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("身份证正面")
    private String idCardFrontImgUrl;


    @Schema(description = "身份证反面", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("身份证反面")
    private String idCardReverseImgUrl;


    @Schema(description = "民族", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("民族")
    private Integer nationCode;

    @Schema(description = "学历", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("学历")
    private Integer formalLevel;

    @Schema(description = "省份", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("省份")
    private String orgProvinceCode;

    @Schema(description = "通信地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("通信地址")
    private String address;


    @Schema(description = "工作履历信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DoctorWorkRecordRespVO> jobItems;


    @Schema(description = "专业职称证书编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("专业职称证书编号")
    private String titleNo;

    @Schema(description = "专业职称证书取得时间，例如：2016-03-20")
    @ExcelProperty("专业职称证书取得时间")
    private LocalDateTime titleTime;

    @Schema(description = "开始执业时间，例如：2021-03-20")
    @ExcelProperty("开始执业时间")
    private LocalDateTime startPracticeTime;

    @Schema(description = "执业结束时间，例如：2029-03-20")
    @ExcelProperty("执业结束时间")
    private LocalDateTime endPracticeDate;

    @Schema(description = "执业证书号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("执业证书号")
    private String professionalNo;

    @Schema(description = "从业时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("从业时间")
    private Integer practiceTime;

    @Schema(description = "接诊人数", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("接诊人数")
    private String receptionNum;

    @Schema(description = "好评率", requiredMode = Schema.RequiredMode.REQUIRED, example = "99")
    @ExcelProperty("好评率")
    private String goodCommentRate;

    @Schema(description = "执业证书取得时间，例如：2016-03-20")
    @ExcelProperty("执业证书取得时间")
    private LocalDateTime professionalTime;

    @Schema(description = "资格证书号")
    @ExcelProperty("资格证书号")
    private String qualificationNo;

    @Schema(description = "资格证书取得时间，例如：2016-03-20")
    @ExcelProperty("资格证书取得时间")
    private LocalDateTime qualificationTime;

    @Schema(description = "收款人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("收款人姓名")
    private String payeeName;

    @Schema(description = "收款人身份证号码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("收款人身份证号码")
    private String payeeIdCard;

    @Schema(description = "收款人手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("收款人手机号")
    private String payeeTelPhone;

    @Schema(description = "银行卡号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("银行卡号")
    private String payeeBankNo;

    @Schema(description = "开户行", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("开户行")
    private String payeeBankName;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "8822")
    private Long userId;

    @Schema(description = "医生审核记录列表")
    private List<DoctorAuditedRecordRespVO> auditedRecords;

    @Schema(description = "环境标志：prod-真实数据；test-测试数据；show-线上演示数据")
    private String envTag;

}