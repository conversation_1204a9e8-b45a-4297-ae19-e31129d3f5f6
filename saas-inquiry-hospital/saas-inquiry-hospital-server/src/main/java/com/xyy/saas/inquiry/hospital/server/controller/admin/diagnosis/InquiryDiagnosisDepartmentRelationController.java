package com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryDiagnosisDepartmentRelationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryDiagnosisDepartmentRelationRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryDiagnosisDepartmentRelationSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisSimpleVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.diagnosis.InquiryDiagnosisDepartmentRelationDO;
import com.xyy.saas.inquiry.hospital.server.service.diagnosis.InquiryDiagnosisDepartmentRelationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "管理后台 - 科室诊断关联")
@RestController
@RequestMapping("/hospital/diagnosis-department-relation")
@Validated
public class InquiryDiagnosisDepartmentRelationController {

    @Resource
    private InquiryDiagnosisDepartmentRelationService inquiryDiagnosisDepartmentRelationService;

    @PostMapping("/create")
    @Operation(summary = "创建科室诊断关联")
    @PreAuthorize("@ss.hasPermission('hospital:diagnosis-department-relation:create')")
    public CommonResult<Boolean> createInquiryDiagnosisDepartmentRelation(@Valid @RequestBody InquiryDiagnosisDepartmentRelationSaveReqVO createReqVO) {
        inquiryDiagnosisDepartmentRelationService.createInquiryDiagnosisDepartmentRelation(createReqVO);
        return success(true);
    }

    @PutMapping("/update")
    @Operation(summary = "更新科室诊断关联")
    @PreAuthorize("@ss.hasPermission('hospital:diagnosis-department-relation:update')")
    public CommonResult<Boolean> updateInquiryDiagnosisDepartmentRelation(@Valid @RequestBody InquiryDiagnosisDepartmentRelationSaveReqVO updateReqVO) {
        inquiryDiagnosisDepartmentRelationService.updateInquiryDiagnosisDepartmentRelation(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除科室诊断关联")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hospital:diagnosis-department-relation:delete')")
    public CommonResult<Boolean> deleteInquiryDiagnosisDepartmentRelation(@RequestParam("id") Long id) {
        inquiryDiagnosisDepartmentRelationService.deleteInquiryDiagnosisDepartmentRelation(id);
        return success(true);
    }

    @GetMapping("/query-diagnosis-by-deptId")
    @Operation(summary = "获得科室诊断关联")
    @Parameter(name = "deptId", description = "科室id", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hospital:diagnosis-department-relation:query')")
    public CommonResult<List<InquiryDiagnosisSimpleVO>> queryDiagnosisByOfficeId(@RequestParam("deptId") Long deptId) {
        List<InquiryDiagnosisSimpleVO> diagnosis = inquiryDiagnosisDepartmentRelationService.queryDiagnosisByOfficeId(deptId);
        return success(diagnosis);
    }


    @GetMapping("/page")
    @Operation(summary = "获得科室诊断关联分页")
    @PreAuthorize("@ss.hasPermission('hospital:diagnosis-department-relation:query')")
    public CommonResult<PageResult<InquiryDiagnosisDepartmentRelationRespVO>> getInquiryDiagnosisDepartmentRelationPage(@Valid InquiryDiagnosisDepartmentRelationPageReqVO pageReqVO) {
        PageResult<InquiryDiagnosisDepartmentRelationDO> pageResult = inquiryDiagnosisDepartmentRelationService.getInquiryDiagnosisDepartmentRelationPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InquiryDiagnosisDepartmentRelationRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出科室诊断关联 Excel")
    @PreAuthorize("@ss.hasPermission('hospital:diagnosis-department-relation:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInquiryDiagnosisDepartmentRelationExcel(@Valid InquiryDiagnosisDepartmentRelationPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InquiryDiagnosisDepartmentRelationDO> list = inquiryDiagnosisDepartmentRelationService.getInquiryDiagnosisDepartmentRelationPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "科室诊断关联.xls", "数据", InquiryDiagnosisDepartmentRelationRespVO.class,
            BeanUtils.toBean(list, InquiryDiagnosisDepartmentRelationRespVO.class));
    }

}