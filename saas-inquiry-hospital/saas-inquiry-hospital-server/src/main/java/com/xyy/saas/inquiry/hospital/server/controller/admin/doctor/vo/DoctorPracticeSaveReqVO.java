package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 医生执业信息新增/修改 Request VO")
@Data
public class DoctorPracticeSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "32594")
    private Long id;

    @Schema(description = "医生GUID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12774")
    @NotEmpty(message = "医生GUID不能为空")
    private String guid;

    @Schema(description = "第一执业机构名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "第一执业机构名称不能为空")
    private String firstPracticeName;

    @Schema(description = "第一执业机构等级，例如：0=三甲, 1=三乙 ...", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "第一执业机构等级，例如：0=三甲, 1=三乙 ...不能为空")
    private Integer firstPracticeLevel;

    @Schema(description = "第一执业机构科室GUID", requiredMode = Schema.RequiredMode.REQUIRED, example = "204")
    @NotEmpty(message = "第一执业机构科室GUID不能为空")
    private String deptGuid;

    @Schema(description = "专业职称代码，例如：2", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "专业职称代码，例如：2不能为空")
    private String titleCode;

    @Schema(description = "专业职称名称，例如：副主任医师", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "专业职称名称，例如：副主任医师不能为空")
    private String titleName;

    @Schema(description = "专业职称证书编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "专业职称证书编号不能为空")
    private String titleNo;

    @Schema(description = "专业职称证书取得时间，例如：2016-03-20")
    private LocalDateTime titleTime;

    @Schema(description = "开始执业时间，例如：2021-03-20")
    private LocalDateTime startPracticeTime;

    @Schema(description = "执业结束时间，例如：2029-03-20")
    private LocalDateTime endPracticeDate;

    @Schema(description = "执业证书号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "执业证书号不能为空")
    private String professionalNo;

    @Schema(description = "执业证书取得时间，例如：2016-03-20")
    private LocalDateTime professionalTime;

    @Schema(description = "资格证书号")
    private String qualificationNo;

    @Schema(description = "资格证书取得时间，例如：2016-03-20")
    private LocalDateTime qualificationTime;

}