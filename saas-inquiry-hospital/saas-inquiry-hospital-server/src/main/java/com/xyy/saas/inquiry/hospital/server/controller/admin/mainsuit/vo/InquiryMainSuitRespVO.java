package com.xyy.saas.inquiry.hospital.server.controller.admin.mainsuit.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 主诉信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InquiryMainSuitRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "27948")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "主诉名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("主诉名称")
    private String mainSuitName;

    @Schema(description = "状态 0启用 1禁用", example = "2")
    @ExcelProperty("状态 0启用 1禁用")
    private Integer status;

    @Schema(description = "性别限制：0无限制,1限男,2限女", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("性别限制：0无限制,1限男,2限女")
    private Integer sexLimit;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}