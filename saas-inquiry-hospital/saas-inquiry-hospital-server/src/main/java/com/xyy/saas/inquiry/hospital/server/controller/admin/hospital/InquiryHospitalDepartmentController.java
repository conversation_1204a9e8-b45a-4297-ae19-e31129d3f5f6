package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDepartmentDO;
import com.xyy.saas.inquiry.hospital.server.service.hospital.InquiryHospitalDepartmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "管理后台 - 科室字典")
@RestController
@RequestMapping("/hospital/inquiry-hospital-department")
@Validated
public class InquiryHospitalDepartmentController {

    @Resource
    private InquiryHospitalDepartmentService inquiryHospitalDepartmentService;

    @PostMapping("/create")
    @Operation(summary = "创建科室字典")
    public CommonResult<Long> createInquiryHospitalDepartment(@Valid @RequestBody InquiryHospitalDepartmentSaveReqVO createReqVO) {
        return success(inquiryHospitalDepartmentService.createInquiryHospitalDepartment(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新科室字典")
    public CommonResult<Boolean> updateInquiryHospitalDepartment(@Valid @RequestBody InquiryHospitalDepartmentSaveReqVO updateReqVO) {
        inquiryHospitalDepartmentService.updateInquiryHospitalDepartment(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除科室字典")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteInquiryHospitalDepartment(@RequestParam("id") Long id) {
        inquiryHospitalDepartmentService.deleteInquiryHospitalDepartment(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得科室字典")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<InquiryHospitalDepartmentRespVO> getInquiryHospitalDepartment(@RequestParam("id") Long id) {
        InquiryHospitalDepartmentDO inquiryHospitalDepartment = inquiryHospitalDepartmentService.getInquiryHospitalDepartment(id);
        return success(BeanUtils.toBean(inquiryHospitalDepartment, InquiryHospitalDepartmentRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得科室字典分页")
    public CommonResult<PageResult<InquiryHospitalDepartmentRespVO>> getInquiryHospitalDepartmentPage(@Valid InquiryHospitalDepartmentPageReqVO pageReqVO) {
        PageResult<InquiryHospitalDepartmentDO> pageResult = inquiryHospitalDepartmentService.getInquiryHospitalDepartmentPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InquiryHospitalDepartmentRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出科室字典 Excel")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInquiryHospitalDepartmentExcel(@Valid InquiryHospitalDepartmentPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InquiryHospitalDepartmentDO> list = inquiryHospitalDepartmentService.getInquiryHospitalDepartmentPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "科室字典.xls", "数据", InquiryHospitalDepartmentRespVO.class,
            BeanUtils.toBean(list, InquiryHospitalDepartmentRespVO.class));
    }

    @GetMapping("/queryAll")
    @Operation(summary = "查询全部科室信息")
    public CommonResult<List<InquiryHospitalDepartmentRespVO>> getInquiryHospitalDepartmentAllList(@Valid InquiryHospitalDepartmentPageReqVO pageReqVO) {
        List<InquiryHospitalDepartmentDO> list = inquiryHospitalDepartmentService.selectDeptmentList(pageReqVO);
        return success(BeanUtils.toBean(list, InquiryHospitalDepartmentRespVO.class));
    }

    @GetMapping("/queryFirstDeptList")
    @Operation(summary = "查询一级科室信息")
    public CommonResult<List<InquiryHospitalDepartmentRespVO>> queryFirstDeptList() {
        List<InquiryHospitalDepartmentDO> list = inquiryHospitalDepartmentService.queryFirstDeptList();
        return success(BeanUtils.toBean(list, InquiryHospitalDepartmentRespVO.class));
    }

}