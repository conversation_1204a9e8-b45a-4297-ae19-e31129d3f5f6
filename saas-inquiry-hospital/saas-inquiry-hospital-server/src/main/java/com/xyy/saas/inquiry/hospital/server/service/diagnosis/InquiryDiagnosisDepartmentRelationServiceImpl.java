package com.xyy.saas.inquiry.hospital.server.service.diagnosis;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_DIAGNOSIS_DEPARTMENT_RELATION_NOT_EXISTS;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_DIAGNOSIS_DUPLICATED;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_HOSPITAL_DEPARTMENT_NOT_EXISTS;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDepartmentRelationReqDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisSimpleVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryDiagnosisDepartmentRelationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryDiagnosisDepartmentRelationSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.convert.hospital.InquiryDiagnosisDepartmentConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.diagnosis.InquiryDiagnosisDepartmentRelationDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDepartmentDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.diagnosis.InquiryDiagnosisDepartmentRelationMapper;
import com.xyy.saas.inquiry.hospital.server.service.hospital.InquiryHospitalDepartmentService;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.HashSet;
import java.util.Collections;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

/**
 * 科室诊断关联 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InquiryDiagnosisDepartmentRelationServiceImpl implements InquiryDiagnosisDepartmentRelationService {

    @Resource
    private InquiryDiagnosisDepartmentRelationMapper inquiryDiagnosisDepartmentRelationMapper;

    @Resource
    private InquiryHospitalDepartmentService inquiryHospitalDepartmentService;

    @Override
    public void createInquiryDiagnosisDepartmentRelation(InquiryDiagnosisDepartmentRelationSaveReqVO createReqVO) {
        // 校验前端重复
        Map<String, List<InquiryDiagnosisSimpleVO>> diaMap = createReqVO.getDiagnosisVOList().stream().collect(Collectors.groupingBy(InquiryDiagnosisSimpleVO::getDiagnosisCode));
        if (diaMap.values().stream().anyMatch(v -> v.size() > 1)) {
            throw exception(INQUIRY_DIAGNOSIS_DUPLICATED);
        }
        InquiryHospitalDepartmentDO departmentDO = inquiryHospitalDepartmentService.getByPref(createReqVO.getDeptPref());
        if (departmentDO == null) {
            throw exception(INQUIRY_HOSPITAL_DEPARTMENT_NOT_EXISTS);
        }

        List<InquiryDiagnosisDepartmentRelationDO> relationDOS = InquiryDiagnosisDepartmentConvert.INSTANCE.convertSaveVOs(createReqVO, departmentDO);
        inquiryDiagnosisDepartmentRelationMapper.insertOrUpdateBatch(relationDOS);
    }

    @Override
    public void updateInquiryDiagnosisDepartmentRelation(InquiryDiagnosisDepartmentRelationSaveReqVO updateReqVO) {
        // 校验存在
        validateInquiryDiagnosisDepartmentRelationExists(updateReqVO.getId());
        // 更新
        InquiryDiagnosisDepartmentRelationDO updateObj = BeanUtils.toBean(updateReqVO, InquiryDiagnosisDepartmentRelationDO.class);
        // 上面查询已经过滤已删除的数据了
        // updateObj.setDeleted(false);
        inquiryDiagnosisDepartmentRelationMapper.updateById(updateObj);
    }

    @Override
    public void deleteInquiryDiagnosisDepartmentRelation(Long id) {
        // 校验存在
        validateInquiryDiagnosisDepartmentRelationExists(id);
        // 删除
        inquiryDiagnosisDepartmentRelationMapper.deleteById(id);
    }

    private void validateInquiryDiagnosisDepartmentRelationExists(Long id) {
        if (inquiryDiagnosisDepartmentRelationMapper.selectById(id) == null) {
            throw exception(INQUIRY_DIAGNOSIS_DEPARTMENT_RELATION_NOT_EXISTS);
        }
    }

    @Override
    public InquiryDiagnosisDepartmentRelationDO getInquiryDiagnosisDepartmentRelation(Long id) {
        return inquiryDiagnosisDepartmentRelationMapper.selectById(id);
    }

    @Override
    public PageResult<InquiryDiagnosisDepartmentRelationDO> getInquiryDiagnosisDepartmentRelationPage(
        InquiryDiagnosisDepartmentRelationPageReqVO pageReqVO) {
        return inquiryDiagnosisDepartmentRelationMapper.selectPage(pageReqVO);
    }

    @Override
    public List<InquiryDiagnosisDepartmentRelationDO> queryDiagnosisDepartmentRelation(
        InquiryDiagnosisDepartmentRelationReqDto req) {
        List<InquiryDiagnosisDepartmentRelationDO> result = new ArrayList<>();
        // 如果 diagnosisCodes 为空则返回空
        if (req.getDiagnosisCodes() == null || req.getDiagnosisCodes().isEmpty()) {
            return List.of();
        }
        // <diagnosisCode,[deptPref1,deptPref2]> 用于存储每个 diagnosisCode 对应的 deptPref 集合
        Map<String,List<String>> diagDeptMap = new HashMap<>();
        // <deptPref,科室信息>
        Map<String,InquiryDiagnosisDepartmentRelationDO> deptMap = new HashMap<>();
        // 遍历每个 diagnosisCode 进行查询
        for (String code : req.getDiagnosisCodes()) {
            // 查询单个诊断编码对应的关联关系
            List<InquiryDiagnosisDepartmentRelationDO> relations = inquiryDiagnosisDepartmentRelationMapper.queryDiagnosisDepartmentRelation(InquiryDiagnosisDepartmentRelationReqDto.builder().diagnosisCode(code).build());
            if(CollectionUtils.isEmpty(relations)) {
                continue;
            }
            diagDeptMap.put(code,relations.stream().map(InquiryDiagnosisDepartmentRelationDO::getDeptPref).collect(Collectors.toList()));
            relations.forEach(r -> deptMap.put(r.getDeptPref(), r));
        }
        // 计算所有 List 的交集
        Set<String> intersection = diagDeptMap.values().stream()
            .map(HashSet::new)
            .reduce((set1, set2) -> {
                set1.retainAll(set2);
                return set1;
            })
            .orElse(new HashSet<>());
        new ArrayList<>(intersection).forEach(deptPref->{
            InquiryDiagnosisDepartmentRelationDO departmentRelationDO = deptMap.get(deptPref);
            if(ObjectUtil.isEmpty(departmentRelationDO)) {
                return;
            }
            result.add(departmentRelationDO);
        });
        return result;
    }

    @Override
    public List<InquiryDiagnosisSimpleVO> queryDiagnosisByOfficeId(Long deptId) {
        List<InquiryDiagnosisDepartmentRelationDO> relationDOS = inquiryDiagnosisDepartmentRelationMapper.queryDiagnosisDepartmentRelation(InquiryDiagnosisDepartmentRelationReqDto.builder().deptId(deptId).build());
        return InquiryDiagnosisDepartmentConvert.INSTANCE.convertVO(relationDOS);
    }

    public static void main(String[] args) {
        // 示例数据
        Map<String, List<String>> diagDeptMap = new HashMap<>();
        diagDeptMap.put("a", Arrays.asList("1", "2", "3"));
        diagDeptMap.put("b", Arrays.asList("2", "3", "4"));

        // 计算所有 List 的交集
        Set<String> intersection = diagDeptMap.values().stream()
            .map(HashSet::new)          // 将每个 List 转为 HashSet（去重 + 优化查询）
            .reduce((set1, set2) -> {   // 递归取交集
                set1.retainAll(set2);   // 保留共同元素
                return set1;
            })
            .orElse(new HashSet<>());   // 若 Map 为空，返回空集合

        // 输出结果（Set 可直接用，或转为 List）
        System.out.println("交集结果（Set）: " + intersection);
        List<String> intersectionList = new ArrayList<>(intersection);
        System.out.println("交集结果（List）: " + intersectionList);
    }
}