package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 科室诊断关联分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InquiryDiagnosisDepartmentRelationPageReqVO extends PageParam {

    @Schema(description = "诊断编码")
    private String diagnosisCode;

    @Schema(description = "诊断名称", example = "芋艿")
    private String diagnosisName;

    @Schema(description = "展示诊断名称", example = "王五")
    private String showName;

    @Schema(description = "医院科室id", example = "18581")
    private Long deptId;

    @Schema(description = "科室编码,eg:101")
    private String deptPref;

    @Schema(description = "科室名称,eg:内科", example = "赵六")
    private String deptName;

    @Schema(description = "是否禁用")
    private Boolean disable;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}