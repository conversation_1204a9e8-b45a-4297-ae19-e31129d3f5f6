package com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 问诊诊断信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InquiryDiagnosisPageReqVO extends PageParam {

    @Schema(description = "诊断编码")
    private String diagnosisCode;

    @Schema(description = "诊断名称", example = "芋艿")
    private String diagnosisName;

    @Schema(description = "诊断类型：0-默认(西医),1-中医", example = "1")
    private Integer diagnosisType;

    @Schema(description = "展示诊断名称", example = "芋艿")
    private String showName;

    @Schema(description = "名称或者code模糊搜索", example = "芋艿")
    private String nameCode;

    @Schema(description = "状态 0启用 1禁用", example = "2")
    private Integer status;

    @Schema(description = "性别限制：0无限制,1限男,2限女", example = "1")
    private Integer sexLimit;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    /**
     * 数据类型：1-常规, 2-系统默认 3-推荐
     */
    private Integer dataType;

    @Schema(description = "处方类型")
    private Integer prescriptionType;

    @Schema(description = "门店ID")
    private Long tenantId;

    private List<Long> noIds;

    private List<Long> ids;

}