package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "管理后台 - 医生录屏记录新增/修改 Request VO")
@Data
public class InquiryDoctorVideoSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "28737")
    private Long id;

    @Schema(description = "录屏编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String pref;

    @Schema(description = "医生pref")
    @NotEmpty(message = "医生编码不能为空")
    private String doctorPref;

    @Schema(description = "医生端问诊视频url地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @NotEmpty(message = "医生端问诊视频url地址不能为空")
    private String videoUrl;

    @Schema(description = "视频md5")
    private String md5;

    @Schema(description = "启用：0是，1否 ", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

}