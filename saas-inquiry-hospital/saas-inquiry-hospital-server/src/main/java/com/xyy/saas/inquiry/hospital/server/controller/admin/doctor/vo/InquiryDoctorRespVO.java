package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorExtDto;
import com.xyy.saas.inquiry.hospital.enums.DictTypeConstants;
import com.xyy.saas.inquiry.hospital.enums.DoctorFillingStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Schema(description = "管理后台 - 医生信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InquiryDoctorRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "20449")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "医生编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1276")
    @ExcelProperty("医生编码")
    private String pref;

    @Schema(description = "医生名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("医生名称")
    private String name;

    @Schema(description = "性别 1男 2女", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("性别 1男 2女")
    private Integer sex;

    @Schema(description = "身份证号码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("身份证号码")
    private String idCard;

    @Schema(description = "手机号")
    @ExcelProperty("手机号")
    private String mobile;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "8822")
    @ExcelProperty("用户ID")
    private Long userId;

    @Schema(description = "审核状态 0、待审核  1、审核通过  2、审核驳回", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("审核状态")
    private Integer auditStatus;

    @Schema(description = "合作状态：0未合作 1 合作中 2禁用合作 3过期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("合作状态")
    private Integer cooperation;

    @Schema(description = "自动开方问诊方式", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("自动开方问诊方式")
    private List<Integer> autoInquiryWayType;

    @Schema(description = "问诊方式", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("问诊方式")
    private List<Integer> inquiryWayType;

    @Schema(description = "在线状态 0离线 1在线", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("在线状态")
    private Integer onlineStatus;

    @Schema(description = "开始接诊时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("开始接诊时间")
    private LocalDateTime startInquiryTime;

    @Schema(description = "结束停诊时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("结束停诊时间")
    private LocalDateTime endInquiryTime;

    @Schema(description = "环境标志：prod-真实数据；test-测试数据；show-线上演示数据", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("环境标志")
    private String envTag;

    @Schema(description = "证件照地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("证件照地址")
    private String photo;

    @Schema(description = "个人简介", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("个人简介")
    private String biography;

    @Schema(description = "擅长专业,eg:擅长神经内科诊疗", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("擅长专业")
    private String professionalDec;

    @Schema(description = "医生类型： 1全职医生 2兼职医生", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("医生类型")
    private Integer jobType;

    @Schema(description = "是否开启密码,0:不开启,1:开启", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("是否开启密码")
    private Boolean prescriptionPasswordStatus;

    @Schema(description = "开方密码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("开方密码")
    private String prescriptionPassword;

    @Schema(description = "是否禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否禁用")
    private Boolean disable;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "实名认证状态 0: 待认证，1: 认证完成，2: 认证失败", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "实名认证状态 0: 待认证，1: 认证完成，2: 认证失败", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.INQUIRY_SIGNATURE_CERTIFY_STATUS)
    private Integer certifyStatus;

    @Schema(description = "签名状态 0: 未签名，1: 已签名", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("签名状态 0: 未签名，1: 已签名")
    private Integer signatureStatus;

    @Schema(description = "签名图片Url")
    private String signatureUrl;

    @Schema(description = "免签授权状态 0: 未授权，1: 已授权，", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("免签授权状态 0: 未授权，1: 已授权，")
    private Integer authorizeFreeSignStatus;

    @Schema(description = "签章平台 0-自签署 1-法大大", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer signaturePlatform;

    @Schema(description = "免签授权截止时间")
    @ExcelProperty("免签授权截止时间")
    private LocalDateTime authorizeFreeSignDdl;

    // 用户账号状态
    @Schema(description = "用户账号状态 0: 启用 1: 禁用  2注销", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "账号状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.SYSTEM_USER_ACCOUNT_STATUS)
    private Integer userAccountStatus;

    /**
     * 拓展字段
     */
    @Schema(description = "拓展信息")
    private InquiryDoctorExtDto ext;
    /**
     * 陕西监管备案状态 0:未备案 1:已备案
     * see {@link DoctorFillingStatusEnum}
     */
    @Schema(description = "陕西监管备案状态 0:未备案 1:已备案")
    @ExcelProperty(value = "陕西监管备案状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.SHAANXI_REGULATORY_FILLING_STATUS)
    private Integer fillingStatus4ShaanxiRegulatory;

    public InquiryDoctorExtDto flattenExt() {
        if (ext == null) {
            ext = new InquiryDoctorExtDto();
        }
        this.fillingStatus4ShaanxiRegulatory = ext.getFillingStatus4ShaanxiRegulatory();
        return ext;
    }
}