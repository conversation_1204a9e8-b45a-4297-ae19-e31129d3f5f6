package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 医院绑定分页请求VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 医院绑定分页请求VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HospitalBindPageReqVO extends PageParam {

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long userId;

    @Schema(description = "医院编码", example = "001")
    private String hospitalPref;

    @Schema(description = "医院名称", example = "北京医院")
    private String hospitalName;

    @Schema(description = "绑定状态", example = "1")
    private Integer bindStatus;

}