package com.xyy.saas.inquiry.hospital.server.controller.admin.job;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.hospital.server.job.HospitalRedisCacheCleanupJob;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 医院缓存任务管理控制器
 * 
 * <AUTHOR>
 * @date 2024/12/27
 */
@Tag(name = "管理后台 - 医院缓存任务")
@RestController
@RequestMapping("/hospital/cache-job")
@Slf4j
public class HospitalCacheJobController {

    @Autowired
    private HospitalRedisCacheCleanupJob hospitalRedisCacheCleanupJob;

    @PostMapping("/manual-cleanup")
    @Operation(summary = "手动触发Redis缓存清理")
    @PreAuthorize("@ss.hasPermission('hospital:cache:cleanup')")
    public CommonResult<String> manualCleanup() {
        try {
            hospitalRedisCacheCleanupJob.manualCleanup();
            return CommonResult.success("Redis缓存清理任务已触发");
        } catch (Exception e) {
            log.error("手动触发Redis缓存清理失败", e);
            return CommonResult.error("清理任务执行失败: " + e.getMessage());
        }
    }
} 