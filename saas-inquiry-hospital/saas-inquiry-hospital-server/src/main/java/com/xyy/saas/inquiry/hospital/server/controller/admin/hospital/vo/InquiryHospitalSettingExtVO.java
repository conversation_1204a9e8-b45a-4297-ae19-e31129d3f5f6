package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * @Author:chenxiaoyi
 * @Date:2024/09/30 10:46
 */
@Data
@Accessors(chain = true)
public class InquiryHospitalSettingExtVO implements Serializable {

    @Schema(description = "特定处方笺")
    private List<InquiryHospitalSpecificPrescriptionTemplateVO> specificPrescriptionTemplates;

    @Schema(description = "特定CA证书")
    private List<InquiryHospitalSpecificPrescriptionCaVO> specificPrescriptionCas;
}
