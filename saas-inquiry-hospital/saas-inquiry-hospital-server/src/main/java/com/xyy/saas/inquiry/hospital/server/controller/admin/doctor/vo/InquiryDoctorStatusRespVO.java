package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import cn.iocoder.yudao.module.system.api.dict.dto.DictDataRespDTO;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "管理后台 - 医生出诊状态关系 Response VO")
@Data
@ExcelIgnoreUnannotated
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InquiryDoctorStatusRespVO {

    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11224")
    private Long userId;

    @Schema(description = "可选接诊权限 1:图文 2:视频 3:电话", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private List<DictDataRespDTO> inquiryWayTypeDicts;

    @Schema(description = "开方类型 1:图文 2:视频 3:电话", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private List<Integer> inquiryWayTypeItems;

    @Schema(description = "自动抢单状态： 0、关闭 1、自动抢单", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer autoGrabStatus;

    @Schema(description = "出停诊状态 0、停诊  1、出诊", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer onlineStatus;
}