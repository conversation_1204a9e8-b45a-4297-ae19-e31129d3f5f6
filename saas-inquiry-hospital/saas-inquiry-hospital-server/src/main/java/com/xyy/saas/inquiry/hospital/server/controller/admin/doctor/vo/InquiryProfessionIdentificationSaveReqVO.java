package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 问诊职业(医生药师)证件信息新增/修改 Request VO")
@Data
public class InquiryProfessionIdentificationSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "26450")
    private Long id;

    @Schema(description = "医生GUID", requiredMode = Schema.RequiredMode.REQUIRED, example = "30951")
    @NotEmpty(message = "医生GUID不能为空")
    private String guid;

    @Schema(description = "类型,1医生,2药师", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "类型,1医生,2药师不能为空")
    private Integer doctorType;

    @Schema(description = "证件类型 1、头像 2、查证结果 3、职称证明 4、执业证 5、资格证", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "证件类型 1、头像 2、查证结果 3、职称证明 4、执业证 5、资格证不能为空")
    private Integer certificateType;

    @Schema(description = "证件名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "证件名称不能为空")
    private String certificateName;

    @Schema(description = "证件号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "证件号不能为空")
    private String certificateNo;

    @Schema(description = "证件地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @NotEmpty(message = "证件地址不能为空")
    private String certificateImgUrl;

    @Schema(description = "注册发证日期")
    private LocalDateTime registerTime;

    @Schema(description = "有效期")
    private LocalDateTime validTime;

}