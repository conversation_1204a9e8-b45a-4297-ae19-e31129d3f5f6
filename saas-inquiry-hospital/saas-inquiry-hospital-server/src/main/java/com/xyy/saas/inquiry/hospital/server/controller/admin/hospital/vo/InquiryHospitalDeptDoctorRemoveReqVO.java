package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName：InquiryHospitalDeptDoctorRemoveReqVO
 * @Author: xucao
 * @Date: 2024/11/20 15:13
 * @Description: 医院科室关联医生删除
 */
@Schema(description = "管理后台 - 医院科室移除医生 Request VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InquiryHospitalDeptDoctorRemoveReqVO {

    @Schema(description = "医院科室关系id", requiredMode = Schema.RequiredMode.REQUIRED, example = "23333")
    @NotNull(message = "医院科室关系id不能为空")
    private Long hospitalDeptRelationId;

    @Schema(description = "医生编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "23333")
    @NotEmpty(message = "医生编码不能为空")
    private String doctorPref;
}
