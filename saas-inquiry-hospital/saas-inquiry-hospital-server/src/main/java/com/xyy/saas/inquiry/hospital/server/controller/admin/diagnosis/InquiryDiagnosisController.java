package com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisExcelVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.diagnosis.InquiryDiagnosisDO;
import com.xyy.saas.inquiry.hospital.server.service.diagnosis.InquiryDiagnosisService;
import com.xyy.saas.inquiry.pojo.excel.ImportResultDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


@Tag(name = "管理后台 - 问诊诊断信息")
@RestController
@RequestMapping("/hospital/inquiry-diagnosis")
@Validated
public class InquiryDiagnosisController {

    @Resource
    private InquiryDiagnosisService inquiryDiagnosisService;

    @PostMapping("/create")
    @Operation(summary = "创建问诊诊断信息")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-diagnosis:create')")
    public CommonResult<Long> createInquiryDiagnosis(@Valid @RequestBody InquiryDiagnosisSaveReqVO createReqVO) {
        return success(inquiryDiagnosisService.createInquiryDiagnosis(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新问诊诊断信息")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-diagnosis:update')")
    public CommonResult<Boolean> updateInquiryDiagnosis(@Valid @RequestBody InquiryDiagnosisSaveReqVO updateReqVO) {
        inquiryDiagnosisService.updateInquiryDiagnosis(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除问诊诊断信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-diagnosis:delete')")
    public CommonResult<Boolean> deleteInquiryDiagnosis(@RequestParam("id") Long id) {
        inquiryDiagnosisService.deleteInquiryDiagnosis(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得问诊诊断信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-diagnosis:query')")
    public CommonResult<InquiryDiagnosisRespVO> getInquiryDiagnosis(@RequestParam("id") Long id) {
        InquiryDiagnosisDO inquiryDiagnosis = inquiryDiagnosisService.getInquiryDiagnosis(id);
        return success(BeanUtils.toBean(inquiryDiagnosis, InquiryDiagnosisRespVO.class));
    }

    @GetMapping("/query-list")
    @Operation(summary = "获得诊断信息列表")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-diagnosis:query')")
    public CommonResult<List<InquiryDiagnosisRespVO>> queryInquiryDiagnosis(InquiryDiagnosisDto diagnosisDto) {
        return success(inquiryDiagnosisService.queryInquiryDiagnosis(diagnosisDto));
    }

    @GetMapping("/page")
    @Operation(summary = "获得问诊诊断信息分页")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-diagnosis:query')")
    public CommonResult<PageResult<InquiryDiagnosisRespVO>> getInquiryDiagnosisPage(@Valid InquiryDiagnosisPageReqVO pageReqVO) {
        PageResult<InquiryDiagnosisDO> pageResult = inquiryDiagnosisService.getDiagnosisPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InquiryDiagnosisRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出问诊诊断信息 Excel")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-diagnosis:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInquiryDiagnosisExcel(@Valid InquiryDiagnosisPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InquiryDiagnosisDO> list = inquiryDiagnosisService.getDiagnosisPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "问诊诊断信息.xls", "数据", InquiryDiagnosisRespVO.class,
            BeanUtils.toBean(list, InquiryDiagnosisRespVO.class));
    }


    @GetMapping("/get-import-template")
    @Operation(summary = "获得诊断数据导入模板")
    public void importTemplate(HttpServletResponse response) throws IOException {
        // 手动创建导出 demo
        List<InquiryDiagnosisExcelVO> list = Collections.singletonList(
            InquiryDiagnosisExcelVO.builder().build()
        );
        // 输出
        ExcelUtils.write(response, "诊断数据导入模板.xls", "诊断数据导入模板", InquiryDiagnosisExcelVO.class, list);
    }

    @PostMapping("/import")
    @Operation(summary = "诊断数据导入模板")
    @Parameters({
        @Parameter(name = "file", description = "Excel 文件", required = true),
        @Parameter(name = "updateSupport", description = "是否支持更新，默认为 false", example = "true")
    })
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-diagnosis:export')")
    public CommonResult<ImportResultDto> importExcel(@RequestParam("file") MultipartFile file,
        @RequestParam(value = "updateSupport", required = false, defaultValue = "false") Boolean updateSupport) throws Exception {
        List<InquiryDiagnosisExcelVO> list = ExcelUtils.read(file, InquiryDiagnosisExcelVO.class);
        return success(inquiryDiagnosisService.importDiagnosisList(list, updateSupport));
    }

}