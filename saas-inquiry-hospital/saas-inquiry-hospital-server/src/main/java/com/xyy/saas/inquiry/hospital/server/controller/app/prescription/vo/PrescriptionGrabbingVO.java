package com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 处方抢单抓取VO
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/17 14:42
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PrescriptionGrabbingVO implements Serializable {

    @Schema(description = "问诊单号", example = "100028")
    @NotEmpty(message = "问诊单号不可为空")
    private String inquiryPref;


    @Schema(description = "自动抢派单状态 0、常规派单接诊  1、自动抢单接诊", example = "1")
    private Integer autoGrabStatus;
    /**
     * 医生编码 - 自动开方传入
     */
    private String doctorPref;

    @Schema(description = "客户端渠类型 0、app  1、pc  2、小程序 ", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer clientChannelType;

    @Schema(description = "客户端系统类型 eg : ios  ,  android", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String clientOsType;
}
