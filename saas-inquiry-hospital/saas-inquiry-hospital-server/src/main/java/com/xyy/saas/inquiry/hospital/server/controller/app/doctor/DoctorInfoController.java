package com.xyy.saas.inquiry.hospital.server.controller.app.doctor;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.getLoginUserId;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_DOCTOR_NOT_EXISTS;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.alibaba.excel.util.StringUtils;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorDetailRespVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName：DoctorBaseInfoController
 * @Author: xucao
 * @Date: 2024/11/11 14:08
 * @Description: 医生基础信息相关接口
 */
@Tag(name = "APP - 医生基础信息相关接口")
@RestController
@RequestMapping(value = {"/admin-api/kernel/hospital/doctor-info", "/app-api/kernel/hospital/doctor-info"})
@Validated
public class DoctorInfoController {

    @Resource
    private InquiryDoctorService inquiryDoctorService;

    @GetMapping("/get")
    @Operation(summary = "获取当前医生信息")
    public CommonResult<InquiryDoctorDetailRespVO> getInquiryDoctor(@RequestParam(value = "doctorPref", required = false) String doctorPref) {
        // 获取当前医生信息
        InquiryDoctorDO doctorDO = null;
        if(StringUtils.isNotBlank(doctorPref)){
            doctorDO = inquiryDoctorService.getInquiryDoctorByDoctorPref(doctorPref);
        }else{
            doctorDO = inquiryDoctorService.getInquiryDoctorByUserId(getLoginUserId());
        }
        if (ObjectUtils.isEmpty(doctorDO)) {
            throw exception(INQUIRY_DOCTOR_NOT_EXISTS);
        }
        return success(inquiryDoctorService.getInquiryDoctor(doctorDO.getId()));
    }


    @GetMapping("/log-off-check")
    @Operation(summary = "医生账号注销前检查  true: 可以注销 不可注销时直接展示失败原因")
    public CommonResult<Boolean> logOffCheck() {
        return success(inquiryDoctorService.logOffCheck());
    }

}
