package com.xyy.saas.inquiry.hospital.server.service.prescription.dto;

import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionDetailDO;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import java.io.Serializable;
import java.util.List;
import lombok.Builder;
import lombok.Data;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2025/08/05 16:11
 */
@Data
@Builder
public class InquiryPrescriptionFlushDto implements Serializable {

    private InquiryPrescriptionDO prescriptionDO;

    private List<InquiryPrescriptionDetailDO> inquiryPrescriptionDetailDOS;

    private InquiryRecordDetailDto inquiryRecordDto;

    /**
     * 是否填充签名人
     */
    private boolean fillSignUser;
}
