package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "管理后台 - 医生审核记录新增/修改 Request VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DoctorAuditedRecordSaveReqVO {

    @Schema(description = "医生id", requiredMode = Schema.RequiredMode.REQUIRED, example = "26750")
    private Long doctorId;

    @Schema(description = "审核人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    private String auditorName;

    @Schema(description = "审核人工号", requiredMode = Schema.RequiredMode.REQUIRED, example = "31169")
    private Long auditorId;

    @Schema(description = "审核结果  1、审核通过  2、审核驳回", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "审核结果  1、审核通过  2、审核驳回不能为空")
    private Integer auditResult;

    @Schema(description = "审核驳回原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "31169")
    @Size(max = 255, message = "审核驳回原因长度不能超过255")
    private String diffReason;
}