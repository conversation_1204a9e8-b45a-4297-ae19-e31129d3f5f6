package com.xyy.saas.inquiry.hospital.server.controller.app.prescription;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo.InquiryPrescriptionCountVO;
import com.xyy.saas.inquiry.hospital.server.service.prescription.InquiryPrescriptionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName：PrescriptionController
 * @Author: xucao
 * @Date: 2024/10/30 15:13
 * @Description: app端处方相关接口
 */
@Tag(name = "APP+PC - 处方查询相关接口")
@RestController
@RequestMapping(value = {"/admin-api/kernel/hospital/prescription", "/app-api/kernel/hospital/prescription"})
@Validated
public class PrescriptionQueryController {

    @Resource
    private InquiryPrescriptionService inquiryPrescriptionService;


    @GetMapping("/page")
    @Operation(summary = "获得处方记录 - 分页")
    public CommonResult<PageResult<InquiryPrescriptionRespVO>> getInquiryPrescriptionPage(@Valid InquiryPrescriptionPageReqVO pageReqVO) {
        pageReqVO.isAppQuery();
        return success(inquiryPrescriptionService.getInquiryPrescriptionPage(pageReqVO));
    }

    @GetMapping("/count")
    @Operation(summary = "获得处方记录统计数据")
    public CommonResult<InquiryPrescriptionCountVO> getInquiryPrescriptionCount() {
        return success(inquiryPrescriptionService.getInquiryPrescriptionCount());
    }
}
