package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 医生收款信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DoctorBillingPageReqVO extends PageParam {

    @Schema(description = "医生ID", example = "21865")
    private Long doctorId;

    @Schema(description = "收款人姓名", example = "张三")
    private String payeeName;

    @Schema(description = "收款人身份证号码")
    private String payeeIdCard;

    @Schema(description = "收款人手机号")
    private String payeeTelPhone;

    @Schema(description = "银行卡号")
    private String payeeBankNo;

    @Schema(description = "开户行", example = "李四")
    private String payeeBankName;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}