package com.xyy.saas.inquiry.hospital.server.service.prescription.strategy.query;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.xyy.saas.inquiry.hospital.server.constant.QuerySceneEnum;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPageReqVO;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/10/30 20:51
 * @Description: 门店查询处方的参数组装策略
 */
@Component
public class DrugstoreQueryStrategy implements QueryStrategy {

    @Autowired
    private TenantApi tenantApi;

    /**
     * 门店查询问诊处方，查询参数设置
     *
     * @param pageReqVO 请求参数
     */
    @Override
    public void setParam(InquiryPrescriptionPageReqVO pageReqVO) {
        // 门店查询问诊处方，查询参数设置
        pageReqVO.setTenantIds(handleQueryTenantIds(tenantApi::getTenantIdsByHeadId, pageReqVO.getTenantId()));
        pageReqVO.setTenantId(null);
        pageReqVO.setEnable(Optional.ofNullable(pageReqVO.getEnable()).orElse(CommonStatusEnum.ENABLE.getStatus())); // 为空查可用的
    }

    /**
     * 获取当前策略对应的查询场景
     *
     * @return 查询场景
     */
    @Override
    public QuerySceneEnum getQueryScene() {
        return QuerySceneEnum.DRUGSTORE;
    }
}
