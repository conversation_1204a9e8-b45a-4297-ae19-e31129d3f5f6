package com.xyy.saas.inquiry.hospital.server.controller.admin.mainsuit.vo;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import com.xyy.saas.inquiry.enums.diagnosis.SexLimitEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Schema(description = "管理后台 - 主诉信息新增/修改 Request VO")
@Data
public class InquiryMainSuitSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "27948")
    private Long id;

    @Schema(description = "主诉名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "主诉名称不能为空")
    @Length(max = 64, message = "主诉名称长度不能超过 64")
    private String mainSuitName;

    @Schema(description = "状态 0启用 1禁用", example = "2")
    @InEnum(CommonStatusEnum.class)
    private Integer status;

    @Schema(description = "性别限制：0无限制,1限男,2限女", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "性别限制不能为空")
    @InEnum(SexLimitEnum.class)
    private Integer sexLimit;

}