package com.xyy.saas.inquiry.hospital.server.controller.admin.medical;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.hospital.server.controller.admin.medical.vo.MedicalRegistrationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.medical.vo.MedicalRegistrationRespVO;
import com.xyy.saas.inquiry.hospital.server.service.medical.MedicalRegistrationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: xucao
 * @DateTime: 2025/7/21 13:11
 * @Description: 医保挂号相关接口
 **/
@Tag(name = "管理后台 - 医保挂号信息")
@RestController
@RequestMapping("/hospital/medical-register")
@Validated
public class MedicareRegisterController {

    @Resource
    private MedicalRegistrationService medicalRegisterService;

    /**
     * 查询医保挂号列表
     * @param pageReqVO
     * @return
     */
    @GetMapping("/page")
    @Operation(summary = "查询医保挂号列表")
    public CommonResult<PageResult<MedicalRegistrationRespVO>> getMedicalRegisterPage(@Valid MedicalRegistrationPageReqVO pageReqVO){
        return medicalRegisterService.queryRegistPage(pageReqVO);
    }

    /**
     * 撤销医保挂号
     * @param registrationPref 挂号流水号
     * @return
     */
    @PostMapping("/cancel")
    @Operation(summary = "撤销医保挂号")
    public CommonResult<Boolean> cancelMedicalRegistration(@RequestParam("registrationPref") String registrationPref) {
        return medicalRegisterService.cancelMedicalRegistration(registrationPref);
    }
}
