package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import lombok.Data;

/**
 * @ClassName：InquiryHospitalDeptDoctorGroupVO
 * @Author: xucao
 * @Date: 2024/11/18 11:19
 * @Description: 医院科室医生分组统计视图模型
 */
@Data
public class InquiryHospitalDeptDoctorGroupVO {

    private Long id;

    // 医院编码
    private String hospitalPref;

    // 医院名称
    private String hospitalName;

    // 部门编码
    private String deptPref;

    // 部门名称
    private String deptName;

    // 医生数量
    private int doctorCount;

    // 是否禁用  0 否 1是
    private Integer disabled;
}
