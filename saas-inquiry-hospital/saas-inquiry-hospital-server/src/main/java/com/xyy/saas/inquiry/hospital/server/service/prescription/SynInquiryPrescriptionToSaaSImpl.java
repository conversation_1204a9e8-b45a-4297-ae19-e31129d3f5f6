package com.xyy.saas.inquiry.hospital.server.service.prescription;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.util.date.DateUtils;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mchange.lang.IntegerUtils;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionStatusEnum;
import com.xyy.saas.inquiry.enums.tenant.BindPlatformEnum;
import com.xyy.saas.inquiry.exception.MqConsumerLaterException;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionQueryDTO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionRespVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionDetailDO;
import com.xyy.saas.inquiry.hospital.server.mq.consumer.prescription.dto.SynInquiryPrescriptionDto;
import com.xyy.saas.inquiry.hospital.server.mq.consumer.prescription.dto.SynInquiryPrescriptionDto.DrugInfoDto;
import com.xyy.saas.inquiry.hospital.server.util.DateUtil;
import com.xyy.saas.inquiry.mq.prescription.dto.PrescriptionMqCommonMessage;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.patient.api.patient.PatientApi;
import com.xyy.saas.inquiry.patient.api.patient.dto.InquiryPatientInfoRespDTO;
import com.xyy.saas.inquiry.pojo.SyncSaasResult;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.util.RedisUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.INQUIRY_PATIENT_INFO_NOT_EXISTS;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.INQUIRY_RECORD_NOT_EXISTS;


@Service
@Slf4j
public class SynInquiryPrescriptionToSaaSImpl implements SynInquiryPrescriptionToSaaS {


    @Resource
    private InquiryPrescriptionService inquiryPrescriptionService;

    @Resource
    private InquiryPrescriptionDetailService inquiryPrescriptionDetailService;

    @DubboReference
    private InquiryApi inquiryApi;

    @DubboReference
    private PatientApi patientApi;

    @Resource
    private TenantApi tenantApi;


    /**
     * 回传SAAS处方锁
     */
    private static final String  SYN_INQUIRY_PRESCRIPTION_TO_SAAS_LOCk = "syn.inquiry.prescription.to.saas.lock:";


    /**
     * 三方SaaS系统接口地址 - 可通过配置文件配置
     */
    @Value("${saas.third-party.prescription.sync-url:}")
    private String thirdPartySyncUrl;

    @Override
    public void toSaas(PrescriptionMqCommonMessage msg) {
        if (Objects.isNull(msg) || StrUtil.isBlank(msg.getPrescriptionPref())) {
            log.warn("【同步处方到SaaS】消息体为空或处方单号为空，跳过处理");
            return;
        }

        String lockKey =  SYN_INQUIRY_PRESCRIPTION_TO_SAAS_LOCk+ msg.getPrescriptionPref();
        String requestId = "1";
        try {
            if(!RedisUtils.tryLock(lockKey,requestId,30 * 1000)){
                log.info("【同步处方到SaaS】获取分布式锁失败，处方单号：{}", msg.getPrescriptionPref());
                return ;
            }
            log.info("【同步处方到SaaS】开始处理，处方单号：{}", msg.getPrescriptionPref());

            // 1. 根据处方单号查询处方
            InquiryPrescriptionRespVO prescription = queryPrescription(msg.getPrescriptionPref());
            if (Objects.isNull(prescription) || StringUtils.isBlank(prescription.getInquiryPref())) {
                log.warn("【同步处方到SaaS】处方不存在，处方单号：{}", msg.getPrescriptionPref());
                return;
            }

            if (!List.of(PrescriptionStatusEnum.WAITING.getStatusCode(), PrescriptionStatusEnum.APPROVAL.getStatusCode()
                    , PrescriptionStatusEnum.APPROVAL_REJECTED.getStatusCode(),PrescriptionStatusEnum.WAIT_APPROVAL.getStatusCode())
                .contains(prescription.getStatus())) {
                log.info("【同步处方到SaaS】状态不正确，处方单号：{}", msg.getPrescriptionPref());
                return;
            }

            // 未绑定智慧脸的门店无需回传
            TenantDto tenant = tenantApi.getTenant(prescription.getTenantId());
            if (!Objects.equals(BindPlatformEnum.BIND_SAAS.getCode(), tenant.getBindPlatform())) {
                return;
            }

            // 远程审方无需回传
            if(Objects.equals(prescription.getInquiryBizType(), InquiryBizTypeEnum.REMOTE_INQUIRY.getCode() )){
                return;
            }

            // 2. 构建发送给三方的数据
            Map<String, Object> requestData = buildThirdPartyRequestData(prescription, msg);

            // 3. 发送HTTP请求到三方SaaS系统
            boolean success = sendToThirdPartySaaS(requestData);

            if (success) {
                log.info("【同步处方到SaaS】成功，处方单号：{}", msg.getPrescriptionPref());
            } else {
                log.error("【同步处方到SaaS】失败，处方单号：{}", msg.getPrescriptionPref());
            }

        } catch (Exception e) {
            log.error("【同步处方到SaaS】异常，处方单号：{}，异常信息：{}", msg.getPrescriptionPref(), e.getMessage(), e);
            throw new MqConsumerLaterException(e.getMessage());
        }finally {
            RedisUtils.releaseLock(lockKey,requestId);
        }
    }


    /**
     * 查询处方
     */
    private InquiryPrescriptionRespVO queryPrescription(String prescriptionPref) {
        return inquiryPrescriptionService.queryByCondition(
            InquiryPrescriptionQueryDTO.builder().pref(prescriptionPref).build()
        );
    }

    /**
     * 构建发送给三方的请求数据
     */
    private Map<String, Object> buildThirdPartyRequestData(InquiryPrescriptionRespVO prescription, PrescriptionMqCommonMessage msg) {
        // 查询问诊单基础信息
        InquiryRecordDto inquiryRecord = inquiryApi.getInquiryDtoByPref(prescription.getInquiryPref());
        // 查询问诊单明细信息
        InquiryRecordDetailDto inquiryDetail = inquiryApi.getInquiryRecordDetail(prescription.getInquiryPref());
        // 查询患者详细信息
        InquiryPatientInfoRespDTO patientInfo = patientApi.getPatientInfoByPref(prescription.getPatientPref());
        // 查询处方详情
        List<InquiryPrescriptionDetailDO> inquiryPrescriptionDetails = inquiryPrescriptionDetailService.getInquiryPrescriptionDetailsByPref(prescription.getPref());

        if (inquiryRecord == null || inquiryDetail == null || CollUtil.isEmpty(inquiryPrescriptionDetails)) {
            throw exception(INQUIRY_RECORD_NOT_EXISTS);
        }
        if (patientInfo == null) {
            throw exception(INQUIRY_PATIENT_INFO_NOT_EXISTS);
        }

        // 组装问诊信息
        SynInquiryPrescriptionDto synInquiryPrescriptionDto = new SynInquiryPrescriptionDto();
        List<DrugInfoDto> drug_info = Lists.newArrayList();
        synInquiryPrescriptionDto.setApply_id(prescription.getInquiryPref());
        synInquiryPrescriptionDto.setPatient_name(inquiryDetail.getPatientName());
        synInquiryPrescriptionDto.setSex(Optional.ofNullable(inquiryDetail.getPatientSex()).orElse(1));
        synInquiryPrescriptionDto.setAge(IntegerUtils.parseInt(inquiryDetail.getPatientAge(), 0));
        synInquiryPrescriptionDto.setTelephone(Optional.ofNullable(inquiryDetail.getPatientMobile()).orElse(StringUtils.EMPTY));
        synInquiryPrescriptionDto.setAllergy(CollUtil.isNotEmpty(inquiryDetail.getAllergic()) ? "1" : "无");
        synInquiryPrescriptionDto.setApproval_status(prescription.getStatus());
        synInquiryPrescriptionDto.setBirthday(DateUtil.getCustomBirthdayByAge(IntegerUtils.parseInt(inquiryDetail.getPatientAge(), 0), DateUtils.of(patientInfo.getBirthday())));
        synInquiryPrescriptionDto.setCreate_time(DateUtils.of(prescription.getCreateTime()));
        synInquiryPrescriptionDto.setDepartments(prescription.getDeptName());
        synInquiryPrescriptionDto.setDiagnostic_content(StringUtils.join(prescription.getDiagnosisName(), "|"));
        synInquiryPrescriptionDto.setHospital(prescription.getHospitalName());
        synInquiryPrescriptionDto.setId_card(patientInfo.getIdCard());
        synInquiryPrescriptionDto.setMarried(3);// 婚否  灵芝没有此参数，传3
        synInquiryPrescriptionDto.setOrganSign(String.valueOf(prescription.getTenantId()));
        synInquiryPrescriptionDto.setOut_prescription_time(DateUtils.of(prescription.getOutPrescriptionTime()));
        synInquiryPrescriptionDto.setPatient_condition(StringUtils.join(prescription.getMainSuit(), "|"));
        synInquiryPrescriptionDto.setPrescription_pdf(msg.getPrescriptionPdfUrl());
        synInquiryPrescriptionDto.setDoctor_name(prescription.getDoctorName());
        synInquiryPrescriptionDto.setAuditType(prescription.getInquiryBizType());
        synInquiryPrescriptionDto.setHospital(inquiryRecord.getHospitalName());

        // 审核通过设置药师
        if (Objects.equals(PrescriptionStatusEnum.APPROVAL.getStatusCode(), prescription.getStatus())) {
            synInquiryPrescriptionDto.setApproval_user(prescription.getPharmacistName());
        }

        synInquiryPrescriptionDto.setNewInquiry("1"); // 新问诊标识

        // 废弃处方兼容
        if (Objects.equals(prescription.getEnable(), CommonStatusEnum.DISABLE.getStatus()) && Objects.equals(inquiryRecord.getEnable(), CommonStatusEnum.DISABLE.getStatus())) {
            synInquiryPrescriptionDto.setAuditType(99);
        }

        // 组装药品信息
        synInquiryPrescriptionDto.setDrug_info(drug_info);
        inquiryPrescriptionDetails.forEach(item -> {
            DrugInfoDto drugInfoDto = new DrugInfoDto();
            drugInfoDto.setDrug_name(item.getCommonName());
            drugInfoDto.setQuantity(item.getQuantity().toString());
            if(Objects.equals(prescription.getMedicineType(), MedicineTypeEnum.ASIAN_MEDICINE.getCode())){
                drugInfoDto.setDosage_info("一次%s".formatted(item.getSingleDose())+item.getSingleUnit());
                drugInfoDto.setUsage(item.getDirections());
                drugInfoDto.setFrequency(StringUtils.defaultIfBlank(item.getUseFrequency(),null));
            }
            drugInfoDto.setDrug_code(StringUtils.defaultIfBlank(item.getProductPref(), " "));
            drugInfoDto.setUnit(item.getPackageUnit());
            drugInfoDto.setUnit_price(item.getProductPrice().toString());
            //drugInfoDto.setDosage_info(item.getDirections());
            drugInfoDto.setSpecification(item.getAttributeSpecification());
            drug_info.add(drugInfoDto);
        });
        return JsonUtils.parseObject(JsonUtils.toJsonPrettyString(synInquiryPrescriptionDto), new TypeReference<>() {
        });
    }

    /**
     * 发送HTTP请求到三方SaaS系统
     */
    private boolean sendToThirdPartySaaS(Map<String, Object> requestData) {
        if (StrUtil.isBlank(thirdPartySyncUrl)) {
            log.warn("【同步处方到SaaS】三方接口地址未配置，跳过发送");
            return false;
        }

        try {
            HttpRequest request = HttpRequest.post(thirdPartySyncUrl)
                .header("Content-Type", "application/json")
                .timeout(30000); // 30秒超时

            log.info("【同步处方到SaaS】请求Param:{}", JsonUtils.toJsonString(requestData));
            // 发送请求
            try (HttpResponse response = request.body(JSONUtil.toJsonStr(requestData)).execute()) {

                log.info("【同步处方到SaaS】HTTP响应，状态码：{}，响应体：{}", response.getStatus(), response.body());

                // 判断响应是否成功（可根据实际三方接口规范调整）
                if (StringUtils.isNotBlank(response.body()) && JsonUtils.parseObject(response.body(), SyncSaasResult.class).isSuccess()) {
                    return true;
                }
            }

        } catch (Exception e) {
            log.error("【同步处方到SaaS】HTTP请求异常：{}", e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        }
        return false;
    }
}
