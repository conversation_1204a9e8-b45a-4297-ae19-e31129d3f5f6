package com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisSimpleVO;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

/**
 * 处方开具VO
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/17 14:42
 */
@Data
@Accessors(chain = true)
public class PrescriptionIssuesVO implements Serializable {

    @Schema(description = "问诊单号")
    @NotEmpty(message = "问诊单号不可为空")
    private String inquiryPref;

    @Schema(description = "问诊药品Dto", example = "{\"inquiryProductInfos\":[{\"commonName\":\"阿莫西林胶囊\",\"attributeSpecification\":\"0.125g*100s\",\"quantity\":1,\"isClick\":true,\"directions\":\"口服\",\"singleDose\":\"4\",\"singleUnit\":\"粒\",\"useFrequency\":\"3次/天\",\"pref\":\"847778\",\"productName\":\"阿莫西林胶囊\",\"type\":1,\"prescriptionYn\":\"1\",\"specifications\":\"0.125g*100s\",\"manufacturer\":\"中山市力恩普制药有限公司\",\"unitName\":\"盒\",\"useFrequencyValue\":\"THREETIMESONEDAY\",\"singleDoseValue\":\"4\",\"singleUnitValue\":\"粒\",\"pharmacyPref\":\"国药准字H44023590\"}]}")
    @NotNull(message = "问诊药品信息不可为空")
    private InquiryProductDto inquiryProductDto;

    @Schema(description = "主诉")
    @NotEmpty(message = "主诉信息不可为空")
    private List<String> mainSuit;

    @Schema(description = "诊断编码", example = "[{\"diagnosisCode\":\"001\",\"diagnosisName\":\"感冒\"},{\"diagnosisCode\":\"002\",\"diagnosisName\":\"上呼吸道感染\"}]")
    @NotEmpty(message = "诊断信息不可为空")
    private List<InquiryDiagnosisSimpleVO> diagnosis;

    @Schema(description = "客户端渠类型 0、app  1、pc  2、小程序 ", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "接诊客户端类型不可为空")
    private Integer clientChannelType;

    @Schema(description = "客户端系统类型 eg : ios  ,  android", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String clientOsType;

    /**
     * 是否自动开方：0 否  、 1是
     */
    private Integer autoInquiry;

    /**
     * 医生userId
     */
    private Long doctorUserId;

    /**
     * 用药类型：0西药  、1中药 {@link MedicineTypeEnum}
     */
    private Integer medicineType;


    @Schema(description = "中医辨证代码")
    private String tcmSyndromeCode;


    @Schema(description = "中医辨证名称")
    private String tcmSyndromeName;


    @AssertTrue(message = "药品用法用量信息不可为空")
    @JsonIgnore
    public boolean isUseDoseValid() {
        if (Objects.equals(medicineType, MedicineTypeEnum.ASIAN_MEDICINE.getCode()) && inquiryProductDto != null && CollUtil.isNotEmpty(inquiryProductDto.getInquiryProductInfos())) {
            return inquiryProductDto.getInquiryProductInfos().stream().anyMatch(p -> StringUtils.isNoneBlank(p.getDirections(), p.getSingleDose(), p.getSingleUnit(), p.getUseFrequency()));
        }

        if (Objects.equals(medicineType, MedicineTypeEnum.CHINESE_MEDICINE.getCode()) && inquiryProductDto != null && CollUtil.isNotEmpty(inquiryProductDto.getInquiryProductInfos())) {
            return StringUtils.isNoneBlank(inquiryProductDto.getTcmDailyDosage(), /* inquiryProductDto.getTcmDaily(), */ inquiryProductDto.getTcmUsage(), inquiryProductDto.getTcmDailyDosage(), inquiryProductDto.getTcmDirections(),
                inquiryProductDto.getTcmProcessingMethod());
        }
        return true;
    }


}
