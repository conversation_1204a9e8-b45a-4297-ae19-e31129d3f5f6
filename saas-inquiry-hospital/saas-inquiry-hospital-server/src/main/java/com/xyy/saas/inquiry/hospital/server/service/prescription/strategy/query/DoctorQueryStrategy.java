package com.xyy.saas.inquiry.hospital.server.service.prescription.strategy.query;

import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;

import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryEnum;
import com.xyy.saas.inquiry.hospital.server.constant.QuerySceneEnum;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorService;
import jakarta.annotation.Resource;
import java.util.Objects;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/10/31 9:40
 * @Description: 医生app查询处方的参数组装策略
 */
@Component
public class DoctorQueryStrategy implements QueryStrategy {

    @Resource
    private InquiryDoctorService inquiryDoctorService;


    /**
     * app查处方-医生角色设置相关参数
     *
     * @param pageReqVO 查询参数
     */
    @Override
    public void setParam(InquiryPrescriptionPageReqVO pageReqVO) {
        //根据userid 查询医生信息
        InquiryDoctorDO doctorDO = inquiryDoctorService.getInquiryDoctorByUserId(Objects.requireNonNull(getLoginUser()).getId());
        //设置医生guid
        pageReqVO.setDoctorPref(doctorDO.getPref());
        // 医生app查询开方记录，只查真人开具的
        pageReqVO.setAutoInquiry(AutoInquiryEnum.NO.getCode());
    }

    /**
     * 获取当前策略对应的查询场景
     *
     * @return 查询场景
     */
    @Override
    public QuerySceneEnum getQueryScene() {
        return QuerySceneEnum.DOCTOR;
    }
}
