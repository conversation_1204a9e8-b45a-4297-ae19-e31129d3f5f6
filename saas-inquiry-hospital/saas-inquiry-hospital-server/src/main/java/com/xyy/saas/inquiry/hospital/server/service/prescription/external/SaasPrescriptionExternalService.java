package com.xyy.saas.inquiry.hospital.server.service.prescription.external;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.external.vo.SaasPrescriptionExternalPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.external.vo.SaasPrescriptionExternalRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.external.vo.SaasPrescriptionExternalSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.external.SaasPrescriptionExternalDO;
import com.xyy.saas.inquiry.mq.prescription.dto.PrescriptionMqCommonMessage;
import jakarta.validation.Valid;

/**
 * 外配(电子)处方记录 Service 接口
 *
 * <AUTHOR>
 */
public interface SaasPrescriptionExternalService {

    /**
     * 创建外配(电子)处方记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    SaasPrescriptionExternalDO saveSaasPrescriptionExternal(@Valid SaasPrescriptionExternalSaveReqVO createReqVO);


    /**
     * 获得外配(电子)处方记录
     *
     * @param id 编号
     * @return 外配(电子)处方记录
     */
    SaasPrescriptionExternalDO getSaasPrescriptionExternal(Long id);

    /**
     * 电子处方监管 Q310 Q300 Q430 - 重庆
     *
     * @param prescriptionPref 处方编号
     */
    void externalSupervision(String prescriptionPref);

    /**
     * 电子处方监管 - 定义标准化流程
     *
     * @param prescriptionPref 处方编号
     */
    void externalSupervision1(PrescriptionMqCommonMessage prescriptionMqCommonMessage);

    /**
     * 删除医保电子处方
     *
     * @param id 编号
     */
    void deleteSaasPrescriptionExternal(Long id);

    /**
     * 获得医保电子处方分页
     *
     * @param pageReqVO 分页查询
     * @return 医保电子处方分页
     */
    PageResult<SaasPrescriptionExternalRespVO> getSaasPrescriptionExternalPage(SaasPrescriptionExternalPageReqVO pageReqVO);

    /**
     * 重试上传电子处方
     *
     * @param id
     * @return
     */
    void retryUpload(Long id);
}