package com.xyy.saas.inquiry.hospital.server.controller.app.clinicalcase;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.idempotent.core.annotation.Idempotent;
import com.xyy.saas.inquiry.hospital.server.controller.app.clinicalcase.vo.InquiryClinicalCaseRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.clinicalcase.vo.InquiryClinicalCaseSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.service.clinicalcase.InquiryClinicalCaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "APP+PC - 病例")
@RestController
@RequestMapping(value = {"/admin-api/kernel/hospital/inquiry-clinical-case", "/app-api/kernel/hospital/inquiry-clinical-case"})
@Validated
public class InquiryClinicalCaseController {

    @Resource
    private InquiryClinicalCaseService inquiryClinicalCaseService;

    @PostMapping("/save")
    @Operation(summary = "保存门诊病例")
    @Idempotent(message = "病例开具中，请勿重复提交")
    public CommonResult<String> createInquiryClinicalCase(@Valid @RequestBody InquiryClinicalCaseSaveReqVO createReqVO) {
        return success(inquiryClinicalCaseService.saveInquiryClinicalCase(createReqVO));
    }


    @GetMapping("/get")
    @Operation(summary = "获得门诊病例")
    @Parameter(name = "inquiryPref", description = "问诊编号", required = true, example = "1024")
    public CommonResult<InquiryClinicalCaseRespVO> getInquiryClinicalCase(@RequestParam("inquiryPref") String inquiryPref) {
        InquiryClinicalCaseRespVO inquiryClinicalCase = inquiryClinicalCaseService.getInquiryClinicalCase(inquiryPref);
        return success(inquiryClinicalCase);
    }


}