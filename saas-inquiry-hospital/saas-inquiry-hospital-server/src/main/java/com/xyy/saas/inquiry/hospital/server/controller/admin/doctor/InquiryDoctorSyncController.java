package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorDetailRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.forward.InquiryDoctorForwardRespVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.service.doctor.DoctorSyncService;
import com.xyy.saas.inquiry.hospital.server.service.doctor.dto.DoctorSyncQueryDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "管理后台 - 医生信息")
@RestController
@RequestMapping("/hospital/inquiry-doctor")
@Validated
public class InquiryDoctorSyncController {

    @Resource
    private DoctorSyncService doctorSyncService;


    @GetMapping("/page-sync")
    @Operation(summary = "获得旧系统医生信息分页")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-doctor:query')")
    public CommonResult<PageResult<InquiryDoctorForwardRespVO>> queryUserDoctorPhysicianLists(DoctorSyncQueryDto dto) {
        return doctorSyncService.queryUserDoctorPhysicianLists(dto);
    }


    @GetMapping("/query-sync")
    @Operation(summary = "获得旧系统医生信息")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-doctor:query')")
    public CommonResult<InquiryDoctorDetailRespVO> queryUserDoctorPhysician(@RequestParam(value = "guid") String guid) {
        return doctorSyncService.queryUserDoctorPhysician(guid);
    }

    @PostMapping("/sync-create")
    @Operation(summary = "同步创建医生信息")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-doctor:sync')")
    public CommonResult<InquiryDoctorDO> createInquiryDoctor(@Valid @RequestBody InquiryDoctorSaveReqVO createReqVO) {
        return doctorSyncService.createInquiryDoctor(createReqVO);
    }


}