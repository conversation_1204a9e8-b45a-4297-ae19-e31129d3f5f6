package com.xyy.saas.inquiry.hospital.server.controller.admin.medical.vo;

import com.xyy.saas.inquiry.pojo.registration.MedicalRegistrationExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import lombok.Data;

@Schema(description = "管理后台 - 医疗就诊登记(挂号)新增/修改 Request VO")
@Data
public class MedicalRegistrationSaveReqVO {

    @Schema(description = "主键", example = "17423")
    private Long id;

    @Schema(description = "就诊编号")
    private String pref;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "业务id", example = "10898")
    @NotEmpty(message = "业务id不能为空")
    private String bizId;

    @Schema(description = "业务类型 0-问诊,1-智慧脸...", example = "2")
    @NotNull(message = "业务类型 0-问诊,1-智慧脸...不能为空")
    private Integer bizType;

    @Schema(description = "患者pref")
    private String patientPref;

    @Schema(description = "患者姓名", example = "赵六")
    private String patientName;

    @Schema(description = "患者手机号")
    private String patientMobile;

    @Schema(description = "患者身份证号")
    private String patientIdCard;

    @Schema(description = "业务就诊流水号", example = "30060")
    private String bizVisitId;

    @Schema(description = "医保就诊id", example = "17452")
    private String medicalVisitId;

    @Schema(description = "医保就诊登记时间")
    private LocalDateTime medicalVisitDate;

    @Schema(description = "医疗类别码", example = "2")
    private String medType;

    @Schema(description = "参保地编号")
    private String insuredAreaNo;

    @Schema(description = "就医地编号")
    private String tenantAreaNo;

    @Schema(description = "参保人员编号")
    private String psnNo;

    @Schema(description = "住院/门诊号")
    private String iptOtpNo;

    @Schema(description = "单据状态", example = "1")
    private Integer status;

    @Schema(description = "登记科室编号")
    private String deptPref;

    @Schema(description = "登记科室名称", example = "李四")
    private String deptName;

    @Schema(description = "医院编号")
    private String hospitalPref;

    @Schema(description = "医院名称", example = "王五")
    private String hospitalName;

    @Schema(description = "个人账户使用标志 0|不使用,1|使用")
    private Integer acctUsedFlag;

    @Schema(description = "预约登记时间")
    private LocalDateTime bookTime;

    @Schema(description = "预约执行日期")
    private LocalDateTime planTime;

    @Schema(description = "就诊挂号登记扩展字段")
    private MedicalRegistrationExtDto ext;

}