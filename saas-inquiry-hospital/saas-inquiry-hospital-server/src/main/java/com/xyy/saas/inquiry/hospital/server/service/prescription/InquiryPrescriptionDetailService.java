package com.xyy.saas.inquiry.hospital.server.service.prescription;

import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionDetailQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionDetailRespDTO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionDetailSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionDetailDO;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 处方记录详情 Service 接口
 *
 * <AUTHOR>
 */
public interface InquiryPrescriptionDetailService {

    /**
     * 创建处方记录详情
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInquiryPrescriptionDetail(@Valid InquiryPrescriptionDetailSaveReqVO createReqVO);

    /**
     * 批量创建处方明细
     *
     * @param createReqVO 创建处方vo
     * @return 操作行数
     */
    List<InquiryPrescriptionDetailRespDTO> batchCreateInquiryPrescriptionDetail(@Valid List<InquiryPrescriptionDetailSaveReqVO> createReqVO);

    /**
     * 更新处方记录详情
     *
     * @param updateReqVO 更新信息
     */
    void updateInquiryPrescriptionDetail(@Valid InquiryPrescriptionDetailSaveReqVO updateReqVO);

    /**
     * 删除处方记录详情
     *
     * @param id 编号
     */
    void deleteInquiryPrescriptionDetail(Long id);

    /**
     * 获得处方记录详情
     *
     * @param id 编号
     * @return 处方记录详情
     */
    InquiryPrescriptionDetailDO getInquiryPrescriptionDetail(Long id);


    /**
     * 查询处方明细信息
     *
     * @param queryDTO
     * @return
     */
    List<InquiryPrescriptionDetailDO> getInquiryPrescriptionDetails(InquiryPrescriptionDetailQueryDTO queryDTO);

    /**
     * 根据处方单号查询处方明细
     *
     * @param prescriptionPref 处方单号
     * @return
     */
    List<InquiryPrescriptionDetailDO> getInquiryPrescriptionDetailsByPref(String prescriptionPref);


}