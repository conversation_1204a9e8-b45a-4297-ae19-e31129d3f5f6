package com.xyy.saas.inquiry.hospital.server.service.prescription.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 开方配置Dto
 *
 * @Author:chenxia<PERSON>i
 * @Date:2025/03/07 15:28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class IssuesPrescriptionConfigDto implements Serializable {


    @Schema(description = "处方笺模板")
    private Long prescriptionTemplateId;

    @Schema(description = "开方前置平台 为空默认自绘")
    private Integer signaturePlatform;

    @Schema(description = "签章平台配置id")
    private Integer signaturePlatformConfigId;


}
