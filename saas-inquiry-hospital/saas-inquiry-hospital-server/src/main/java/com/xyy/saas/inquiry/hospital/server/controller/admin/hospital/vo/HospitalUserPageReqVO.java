package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * @Author: xucao
 * @DateTime: 2025/7/22 15:08
 * @Description: 医院员工查询入参对象
 **/
@Schema(description = "管理后台 - 医院员工分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HospitalUserPageReqVO extends PageParam {

    @Schema(description = "用户名，模糊匹配", example = "张三")
    private String nickname;

    @Schema(description = "登录账号，模糊匹配", example = "zhangsan")
    private String username;

    @Schema(description = "账号状态", example = "1")
    private Integer status;

    @Schema(description = "医院编码", example = "H001")
    @NotEmpty(message = "医院编码不能为空")
    private String hospitalPref;

    @Schema(description = "医院名称", example = "上海大学")
    private String hospitalName;

    @Schema(description = "userId", example = "1")
    private Long userId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
