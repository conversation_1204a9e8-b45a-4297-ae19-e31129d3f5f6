package com.xyy.saas.inquiry.hospital.server.controller.admin.medical.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.annotation.InquiryDateType;
import com.xyy.saas.inquiry.pojo.registration.MedicalRegistrationExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

@Schema(description = "管理后台 - 医疗就诊登记(挂号) Response VO")
@Data
@ExcelIgnoreUnannotated
public class MedicalRegistrationRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "17423")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "就诊编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("就诊编号")
    private String pref;

    @Schema(description = "业务id", requiredMode = Schema.RequiredMode.REQUIRED, example = "10898")
    @ExcelProperty("业务id")
    private String bizId;

    @Schema(description = "业务类型 0-问诊,1-智慧脸...", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("业务类型 0-问诊,1-智慧脸...")
    private Integer bizType;

    @Schema(description = "患者pref")
    @ExcelProperty("患者pref")
    private String patientPref;

    @Schema(description = "患者姓名", example = "赵六")
    @ExcelProperty("患者姓名")
    private String patientName;

    @Schema(description = "患者手机号")
    @ExcelProperty("患者手机号")
    private String patientMobile;

    @Schema(description = "患者身份证号")
    @ExcelProperty("患者身份证号")
    private String patientIdCard;

    @Schema(description = "业务就诊流水号", requiredMode = Schema.RequiredMode.REQUIRED, example = "30060")
    @ExcelProperty("业务就诊流水号")
    private String bizVisitId;

    @Schema(description = "医保就诊id", requiredMode = Schema.RequiredMode.REQUIRED, example = "17452")
    @ExcelProperty("医保就诊id")
    private String medicalVisitId;

    @Schema(description = "医保就诊登记时间")
    @ExcelProperty("医保就诊登记时间")
    private LocalDateTime medicalVisitDate;

    @Schema(description = "医疗类别码", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("医疗类别码")
    private String medType;

    @Schema(description = "参保地编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("参保地编号")
    private String insuredAreaNo;

    @Schema(description = "就医地编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("就医地编号")
    private String tenantAreaNo;

    @Schema(description = "参保人员编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("参保人员编号")
    private String psnNo;

    @Schema(description = "住院/门诊号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("住院/门诊号")
    private String iptOtpNo;

    @Schema(description = "单据状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("单据状态")
    private Integer status;

    @Schema(description = "登记科室编号")
    @ExcelProperty("登记科室编号")
    private String deptPref;

    @Schema(description = "登记科室名称", example = "李四")
    @ExcelProperty("登记科室名称")
    private String deptName;

    @Schema(description = "医院编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("医院编号")
    private String hospitalPref;

    @Schema(description = "医院名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("医院名称")
    private String hospitalName;

    @Schema(description = "个人账户使用标志 0|不使用,1|使用")
    @ExcelProperty("个人账户使用标志 0|不使用,1|使用")
    private Integer acctUsedFlag;

    @Schema(description = "预约登记时间")
    @ExcelProperty("预约登记时间")
    private LocalDateTime bookTime;

    @Schema(description = "预约执行日期")
    @ExcelProperty("预约执行日期")
    private LocalDateTime planTime;

    @Schema(description = "就诊挂号登记扩展字段")
    @ExcelProperty("就诊挂号登记扩展字段")
    private MedicalRegistrationExtDto ext;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    @InquiryDateType("createTime")
    private String createTimeStr;

}