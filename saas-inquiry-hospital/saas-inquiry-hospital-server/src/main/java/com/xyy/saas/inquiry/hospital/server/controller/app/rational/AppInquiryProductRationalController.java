package com.xyy.saas.inquiry.hospital.server.controller.app.rational;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.hospital.server.controller.app.rational.vo.InquiryLimitAuditReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.rational.vo.InquiryRationalDictConfigPageVo;
import com.xyy.saas.inquiry.hospital.server.controller.app.rational.vo.InquiryRationalDictConfigVo;
import com.xyy.saas.inquiry.hospital.server.controller.app.rational.vo.RationalTipsVO;
import com.xyy.saas.inquiry.hospital.server.service.rational.InquiryRationalService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author:chenxiaoyi
 * @Date:2024/11/26 15:56
 */
@Tag(name = "APP+PC - 过敏史+合理用药")
@RestController
@RequestMapping(value = {"/admin-api/kernel/hospital/rational", "/app-api/kernel/hospital/rational"})
@Validated
public class AppInquiryProductRationalController {

    @Resource
    private InquiryRationalService inquiryRationalService;


    @GetMapping("/irritability/getDefaultAllergy")
    @Operation(summary = "获取页面默认过敏列表")
    public CommonResult<List<InquiryRationalDictConfigVo>> getDefaultAllergy() {
        return inquiryRationalService.getDefaultAllergy();
    }

    @GetMapping("/irritability/getRecommendAllergy")
    @Operation(summary = "获取推荐过敏列表")
    public CommonResult<List<InquiryRationalDictConfigVo>> getRecommendAllergy() {
        return inquiryRationalService.getRecommendAllergy();
    }

    @GetMapping("/irritability/queryList")
    @Operation(summary = "分页获取过敏列表")
    public CommonResult<PageResult<InquiryRationalDictConfigVo>> queryAllergyList(InquiryRationalDictConfigPageVo pageVO) {
        return inquiryRationalService.queryAllergyList(pageVO);
    }


    @PostMapping("/limitDrugAudit")
    @Operation(summary = "合理用药审核接口")
    public CommonResult<List<RationalTipsVO>> limitDrugAudit(@Valid @RequestBody InquiryLimitAuditReqVO reqVO) {
        return inquiryRationalService.limitDrugAudit(reqVO);
    }


}
