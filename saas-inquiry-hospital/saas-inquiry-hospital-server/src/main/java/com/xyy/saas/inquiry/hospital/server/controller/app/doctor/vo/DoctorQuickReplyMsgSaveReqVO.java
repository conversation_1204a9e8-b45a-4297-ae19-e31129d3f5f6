package com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Schema(description = "管理后台 - 医生快捷回复语新增/修改 Request VO")
@Data
public class DoctorQuickReplyMsgSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "21432")
    private Long id;

    @Schema(description = "父级id", example = "21577")
    private Long parentId;

    @Schema(description = "医生id", requiredMode = Schema.RequiredMode.REQUIRED, example = "7595")
    @NotNull(message = "医生id不能为空")
    private Long doctorId;

    @Schema(description = "分类标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "分类标题不能为空")
    @Length(max = 40)
    private String title;

    @Schema(description = "常用语内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "常用语内容不能为空")
    @Length(max = 500)
    private String content;

    @Schema(description = "排序")
    private Integer sorted;

}