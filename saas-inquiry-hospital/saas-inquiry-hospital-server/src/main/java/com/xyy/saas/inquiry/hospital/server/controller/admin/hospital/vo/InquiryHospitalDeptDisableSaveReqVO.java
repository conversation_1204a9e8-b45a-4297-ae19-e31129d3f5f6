package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @ClassName：InquiryHospitalDeptDisableSaveReqVO
 * @Author: xucao
 * @Date: 2024/11/18 20:29
 * @Description: 管理后台 - 启/禁用医院科室入参
 */
@Schema(description = "管理后台 - 启/禁用医院科室")
@Data
public class InquiryHospitalDeptDisableSaveReqVO {

    @Schema(description = "医院科室关系id", requiredMode = Schema.RequiredMode.REQUIRED, example = "23333")
    @NotNull(message = "医院科室关系id不能为空")
    private Long id;

    @Schema(description = "当前科室状态 0 启用 1 禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "启用状态不能为空")
    private Integer disabled;
}
