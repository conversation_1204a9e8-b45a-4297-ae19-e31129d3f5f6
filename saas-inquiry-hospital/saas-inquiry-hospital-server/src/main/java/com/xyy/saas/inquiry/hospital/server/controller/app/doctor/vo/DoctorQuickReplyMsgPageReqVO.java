package com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 医生快捷回复语分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class DoctorQuickReplyMsgPageReqVO extends PageParam {

    @Schema(description = "父级id", example = "21577")
    private Long parentId;

    @Schema(description = "GUID(医生)", example = "7595")
    @NotNull
    private Long doctorId;

    @Schema(description = "分类标题")
    private String title;

    @Schema(description = "常用语内容")
    private String content;

    @Schema(description = "排序")
    private Integer sorted;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}