package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 医生备案信息新增/修改 Request VO")
@Data
public class DoctorFilingSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "8615")
    private Long id;

    @Schema(description = "医生GUID", requiredMode = Schema.RequiredMode.REQUIRED, example = "26807")
    @NotEmpty(message = "医生GUID不能为空")
    private String guid;

    @Schema(description = "民族编码 1、汉族 ....", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "民族编码 1、汉族 ....不能为空")
    private Integer nationCode;

    @Schema(description = "民族名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "民族名称不能为空")
    private String nationName;

    @Schema(description = "通信地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "通信地址不能为空")
    private String address;

    @Schema(description = "学历，eg:1、博士研究生 2、硕士研究生 3、本科 ...")
    private Integer formalLevel;

    @Schema(description = "学历，eg:1、博士研究生 2、硕士研究生 3、本科 ...", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "学历，eg:1、博士研究生 2、硕士研究生 3、本科 ...不能为空")
    private String formalName;

    @Schema(description = "机构所在省份编码，eg: 420000", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "机构所在省份编码，eg: 420000不能为空")
    private String orgProvinceCode;

    @Schema(description = "机构所在省份名称，eg: 新疆维吾尔族自治区", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "机构所在省份名称，eg: 新疆维吾尔族自治区不能为空")
    private String orgProvinceName;

    @Schema(description = "备案状态 1、待审核  2、审核中  3、审核通过  4、审核驳回", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "备案状态 1、待审核  2、审核中  3、审核通过  4、审核驳回不能为空")
    private Integer recordStatus;

}