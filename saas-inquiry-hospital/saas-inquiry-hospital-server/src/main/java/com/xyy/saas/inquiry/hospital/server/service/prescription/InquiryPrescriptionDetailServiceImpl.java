package com.xyy.saas.inquiry.hospital.server.service.prescription;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_PRESCRIPTION_DETAIL_NOT_EXISTS;

import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionDetailQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionDetailRespDTO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionDetailSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.convert.prescription.InquiryPrescriptionDetailConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionDetailDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.prescription.InquiryPrescriptionDetailMapper;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 处方记录详情 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InquiryPrescriptionDetailServiceImpl implements InquiryPrescriptionDetailService {

    @Resource
    private InquiryPrescriptionDetailMapper inquiryPrescriptionDetailMapper;

    @Override
    public Long createInquiryPrescriptionDetail(InquiryPrescriptionDetailSaveReqVO createReqVO) {
        // 插入
        InquiryPrescriptionDetailDO inquiryPrescriptionDetail = BeanUtils.toBean(createReqVO, InquiryPrescriptionDetailDO.class);
        inquiryPrescriptionDetailMapper.insert(inquiryPrescriptionDetail);
        // 返回
        return inquiryPrescriptionDetail.getId();
    }


    @Override
    public List<InquiryPrescriptionDetailRespDTO> batchCreateInquiryPrescriptionDetail(List<InquiryPrescriptionDetailSaveReqVO> createReqVO) {
        List<InquiryPrescriptionDetailDO> detailDOS = InquiryPrescriptionDetailConvert.INSTANCE.convertDO(createReqVO);
        inquiryPrescriptionDetailMapper.insertBatch(detailDOS);
        return InquiryPrescriptionDetailConvert.INSTANCE.convertDTOs(detailDOS);
    }

    @Override
    public List<InquiryPrescriptionDetailDO> getInquiryPrescriptionDetailsByPref(String prescriptionPref) {
        return inquiryPrescriptionDetailMapper.selectByPrescriptionPref(prescriptionPref);
    }


    @Override
    public List<InquiryPrescriptionDetailDO> getInquiryPrescriptionDetails(InquiryPrescriptionDetailQueryDTO queryDTO) {
        return inquiryPrescriptionDetailMapper.selectByCondition(queryDTO);
    }

    @Override
    public void updateInquiryPrescriptionDetail(InquiryPrescriptionDetailSaveReqVO updateReqVO) {
        // 校验存在
        validateInquiryPrescriptionDetailExists(updateReqVO.getId());
        // 更新
        InquiryPrescriptionDetailDO updateObj = BeanUtils.toBean(updateReqVO, InquiryPrescriptionDetailDO.class);
        inquiryPrescriptionDetailMapper.updateById(updateObj);
    }

    @Override
    public void deleteInquiryPrescriptionDetail(Long id) {
        // 校验存在
        validateInquiryPrescriptionDetailExists(id);
        // 删除
        inquiryPrescriptionDetailMapper.deleteById(id);
    }

    private void validateInquiryPrescriptionDetailExists(Long id) {
        if (inquiryPrescriptionDetailMapper.selectById(id) == null) {
            throw exception(INQUIRY_PRESCRIPTION_DETAIL_NOT_EXISTS);
        }
    }

    @Override
    public InquiryPrescriptionDetailDO getInquiryPrescriptionDetail(Long id) {
        return inquiryPrescriptionDetailMapper.selectById(id);
    }


}