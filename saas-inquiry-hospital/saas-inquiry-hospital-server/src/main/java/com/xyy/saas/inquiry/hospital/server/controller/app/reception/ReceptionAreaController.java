package com.xyy.saas.inquiry.hospital.server.controller.app.reception;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.hospital.server.service.reception.HospitalReceptionAreaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Desc 接诊大厅
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/9/4 下午5:48
 */
@Tag(name = "医院接诊大厅")
@RestController
@RequestMapping(value = {"/admin-api/kernel/hospital/reception", "/app-api/kernel/hospital/reception"})
@Validated
public class ReceptionAreaController {

    @Resource
    private HospitalReceptionAreaService hospitalReceptionAreaService;

    @PutMapping("/auto-video-dispatch")
    @Operation(summary = "视频问诊完成后开方调度")
    @Parameter(name = "inquiryPref", description = "问诊编码", required = true, example = "110335")
    public CommonResult autoVideoDispatch(@RequestParam("inquiryPref") String inquiryPref) {
        hospitalReceptionAreaService.autoVideoDispatch(inquiryPref);
        return CommonResult.success("");
    }
}
