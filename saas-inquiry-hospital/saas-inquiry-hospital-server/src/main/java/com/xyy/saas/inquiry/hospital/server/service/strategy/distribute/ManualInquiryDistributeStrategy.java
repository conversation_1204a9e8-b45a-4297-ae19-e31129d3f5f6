package com.xyy.saas.inquiry.hospital.server.service.strategy.distribute;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.constant.InquiryConstant;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryEnum;
import com.xyy.saas.inquiry.enums.inquiry.DistributeModelEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryGrabStatusEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo.PrescriptionGrabbingVO;
import com.xyy.saas.inquiry.hospital.server.dal.redis.doctor.DoctorRedisDao;
import com.xyy.saas.inquiry.hospital.server.mq.consumer.doctor.DoctorDistributeConsumer;
import com.xyy.saas.inquiry.hospital.server.mq.message.doctor.DoctorDistributeEvent;
import com.xyy.saas.inquiry.hospital.server.mq.message.doctor.dto.DoctorDistributeMessage;
import com.xyy.saas.inquiry.hospital.server.mq.producer.doctor.DoctorDistributeProducer;
import com.xyy.saas.inquiry.hospital.server.service.message.DoctorImService;
import com.xyy.saas.inquiry.hospital.server.service.prescription.PrescriptionService;
import com.xyy.saas.inquiry.im.enums.PushContentEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.util.MathUtil;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/12/26 10:29
 * @Description: 真人手动开方调度策略
 */
@Slf4j
@Component
public class ManualInquiryDistributeStrategy implements DistributeInquiryStrategy {

    @Resource
    private DoctorRedisDao doctorRedisDao;

    @Resource
    private DoctorDistributeProducer doctorDistributeProducer;

    @Resource
    private PrescriptionService prescriptionService;

    private ManualInquiryDistributeStrategy getSelf() {
        return SpringUtil.getBean(this.getClass());
    }

    @Resource
    private ConfigApi configApi;

    @Resource
    private DoctorImService doctorImService;

    /**
     * 问诊派单
     *
     * @param inquiryDto 问诊单信息
     * @param doctorList 医生列表
     */
    @Override
    @TraceNode(node = TraceNodeEnum.DOCTOR_DISTRIBUTE_MANUAL_INQUIRY, prefLocation = "inquiryDto.pref")
    public void distributeInquiry(InquiryRecordDto inquiryDto, List<String> doctorList) {
        log.info("接诊大厅普通调度开始，问诊单:{}，医生：{}", inquiryDto.getPref(), JSON.toJSONString(doctorList));
        // 1、问诊派单缓存更新
        doctorRedisDao.onSendInquiry(inquiryDto.getPref(), doctorList, inquiryDto.getEnv());
        // 2、消息推送医生端
        pushMsgToDoctorForSendInquiry(doctorList, inquiryDto);
        // 3、自动抢单模式检查--执行自动抢单
        if (ObjectUtil.equals(inquiryDto.getDistributeModelEnum(), DistributeModelEnum.AUTO_GRAB_DISTRIBUTE) && getSelf().autoGrab(inquiryDto, doctorList)) {
            return;
        }
        // 4、发送医生调度轮询mq 10秒后执行
        doctorDistributeProducer.sendMessage(DoctorDistributeEvent.builder().msg(DoctorDistributeMessage.builder().hospitalDeptDto(inquiryDto.getHospitalDeptDto()).inquiryPref(inquiryDto.getPref()).doctorList(doctorList).build()).build(),
            LocalDateTime.now().plusSeconds(MathUtil.formatNumberWithDefault(configApi.getConfigValueByKey(InquiryConstant.INQUIRY_DISTRIBUTE_POLL_INTERVAL), 10)));
    }

    /**
     * 判断当前问诊是否为自动抢单模式-如果是，则执行自动抢单
     *
     * @param inquiryDto
     * @param doctorList
     * @return
     */
    @TraceNode(node = TraceNodeEnum.DOCTOR_AUTOAUTOGRAB_INQUIRY, prefLocation = "inquiryDto.pref")
    public boolean autoGrab(InquiryRecordDto inquiryDto, List<String> doctorList) {
        log.info("问诊单:{}，调度模式为自动抢单", inquiryDto.getPref());
        CommonResult<?> result = prescriptionService.grabbingPrescriptionByDoctor(
            PrescriptionGrabbingVO.builder().inquiryPref(inquiryDto.getPref()).doctorPref(doctorList.getFirst()).clientChannelType(inquiryDto.getClientChannelType()).autoGrabStatus(InquiryGrabStatusEnum.GRAB.getCode())
                .clientOsType(inquiryDto.getClientOsType()).build());
        log.info("问诊单:{}，自动抢单结果", JSON.toJSONString(result));
        boolean autoGrabResult = result.isSuccess();
        // 4、自动抢单成功时，直接返回
        if (autoGrabResult) {
            // 写入医生自动抢单缓存
            doctorRedisDao.pushDoctorCurrentAutoGrabList(doctorList.getFirst(), inquiryDto.getEnv(), inquiryDto.getPref());
            // 推送自动抢单成功通知消息
            doctorImService.batchPushNotifyMessage(Collections.singletonList(doctorList.getFirst()), PushContentEnum.GRAB_ORDER, inquiryDto);
            log.info("问诊单pref:{},doctor:{}，自动抢单成功", inquiryDto.getPref(), doctorList.getFirst());
            return true;
        }
        return false;
    }

    /**
     * 推送派单消息给医生端
     *
     * @param doctorList 派单医生列表
     */
    private void pushMsgToDoctorForSendInquiry(List<String> doctorList, InquiryRecordDto inquiryDto) {
        // 推送派单IM消息
        doctorImService.batchNotifyDoctorForInquiryChange(doctorList);
        // 自动抢单模式下无需给医生推送新派单通知
        if (ObjectUtil.equals(inquiryDto.getDistributeModelEnum(), DistributeModelEnum.AUTO_GRAB_DISTRIBUTE)) {
            return;
        }
        // 推送派单通知消息
        doctorImService.batchPushNotifyMessage(doctorList, PushContentEnum.NEW_ORDER, inquiryDto);
    }

    @Override
    public AutoInquiryEnum getInquiryType() {
        return AutoInquiryEnum.NO;
    }
}
