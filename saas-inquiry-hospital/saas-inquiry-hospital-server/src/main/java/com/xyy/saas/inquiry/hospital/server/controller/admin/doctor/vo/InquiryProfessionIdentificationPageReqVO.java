package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 问诊职业(医生药师)证件信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InquiryProfessionIdentificationPageReqVO extends PageParam {

    @Schema(description = "医生|药师 id", example = "30951")
    private Long personId;

    @Schema(description = "类型,1医生,2药师", example = "1")
    private Integer doctorType;

    @Schema(description = "证件类型 1、头像 2、查证结果 3、职称证明 4、执业证 5、资格证", example = "2")
    private Integer certificateType;

    @Schema(description = "证件名称", example = "李四")
    private String certificateName;

    @Schema(description = "证件号")
    private String certificateNo;

    @Schema(description = "证件地址", example = "https://www.iocoder.cn")
    private String certificateImgUrl;

    @Schema(description = "注册发证日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] registerTime;

    @Schema(description = "有效期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] validTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}