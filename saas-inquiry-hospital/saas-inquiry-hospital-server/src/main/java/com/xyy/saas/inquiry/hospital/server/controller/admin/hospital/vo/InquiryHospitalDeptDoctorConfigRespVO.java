package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName：InquiryHospitalDeptDoctorConfigRespVO
 * @Author: xucao
 * @Date: 2024/11/20 17:12
 * @Description: 必须描述类做什么事情, 实现什么功能
 */
@Data
@Schema(description = "管理后台 - 医院科室医生配置信息 Response VO")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InquiryHospitalDeptDoctorConfigRespVO {

    @Schema(description = "医院编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "23333")
    private String hospitalPref;

    @Schema(description = "医院科室医生关系id", requiredMode = Schema.RequiredMode.REQUIRED, example = "23333")
    private Long hospitalDeptRelationId;

    @Schema(description = "医生在当前医院的编码", example = "23333")
    private String doctorHospitalPref;

    @Schema(description = "医生编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "23333")
    private String doctorPref;

    @Schema(description = "自动接诊方式", requiredMode = Schema.RequiredMode.REQUIRED, example = "23333")
    private List<Integer> autoInquiryWayType;

    @Schema(description = "接诊方式", requiredMode = Schema.RequiredMode.REQUIRED, example = "23333")
    private List<Integer> inquiryWayType;

    @Schema(description = "自动开方时段", requiredMode = Schema.RequiredMode.REQUIRED, example = "23333")
    private List<String> autoInquiryTime;
}
