package com.xyy.saas.inquiry.hospital.server.controller.app.clinicalcase.vo;

import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisSimpleVO;
import com.xyy.saas.inquiry.pojo.inquiry.ClinicalCaseExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import lombok.Data;

@Schema(description = "管理后台 - 门诊病例新增/修改 Request VO")
@Data
public class InquiryClinicalCaseSaveReqVO {

    @Schema(description = "主键", example = "4747")
    private Long id;

    @Schema(description = "病例号")
    private String pref;

    /**
     * 门店id
     */
    @Schema(description = "门店ID")
    private Long tenantId;

    /**
     * 租户名称
     */
    @Schema(description = "门店名称")
    private String tenantName;

    @Schema(description = "问诊编号")
    @NotEmpty(message = "问诊编号不能为空")
    private String inquiryPref;

    @Schema(description = "医院编号")
    private String hospitalPref;

    @Schema(description = "医院名称")
    private String hospitalName;

    @Schema(description = "医生编号")
    private String doctorPref;

    @Schema(description = "医生姓名")
    private String doctorName;

    @Schema(description = "科室编码")
    private String deptPref;

    @Schema(description = "科室名称")
    private String deptName;

    @Schema(description = "患者pref")
    private String patientPref;

    @Schema(description = "患者姓名", example = "赵六")
    private String patientName;

    @Schema(description = "患者身份证号")
    private String patientIdCard;

    /**
     * 患者年龄
     */
    @Schema(description = "患者年龄")
    private String patientAge;

    @Schema(description = "患者性别")
    private Integer patientSex;
    /**
     * 主诉
     */
    @Schema(description = "主诉")
    private List<String> mainSuit;
    /**
     * 过敏史  eg：青霉素|头孢
     */
    @Schema(description = "过敏史")
    private List<String> allergic;

    /**
     * 既往史
     */
    @Schema(description = "既往史")
    private String patientHisDesc;
    /**
     * 现病史
     */
    @Schema(description = "现病史")
    private String currentIllnessDesc;


    @Schema(description = "复诊标识 0初次 1复诊")
    private Integer followUp;

    @Schema(description = "主要症状")
    private String mainSymptoms;

    @Schema(description = "门诊诊断说明")
    private String outpatientDiagnosisDesc;

    @Schema(description = "处理措施")
    private String measures;

    @Schema(description = "是否需要留院观察 0否 1是")
    private Integer observation;

    @Schema(description = "转诊标识 0非转诊")
    private Integer referral;

    @Schema(description = "诊断")
    private List<InquiryDiagnosisSimpleVO> diagnosis;

    @Schema(description = "中药西医诊断")
    private List<InquiryDiagnosisSimpleVO> tcmDiagnosis;


    @Schema(description = "中医辨证代码")
    private String tcmSyndromeCode;

    @Schema(description = "中医辨证名称")
    private String tcmSyndromeName;

    @Schema(description = "中医治法代码")
    private String tcmTreatmentMethodCode;

    @Schema(description = "中医治法名称")
    private String tcmTreatmentMethodName;

    @Schema(description = "病例扩展字段")
    private ClinicalCaseExtDto ext;


}