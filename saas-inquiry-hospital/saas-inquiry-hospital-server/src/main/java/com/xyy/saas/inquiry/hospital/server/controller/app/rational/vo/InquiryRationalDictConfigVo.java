package com.xyy.saas.inquiry.hospital.server.controller.app.rational.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:chenxiaoyi
 * @Date:2023/11/06 18:40
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InquiryRationalDictConfigVo implements Serializable {

    /**
     * ID
     */
    private Integer id;
    /**
     * 业务id 相同的id 为统一业务，代码枚举
     */
    private Integer type;
    /**
     * 名称 eg:新生儿/抗生素
     */
    private String name;
    /**
     * 值,eg:1 / 规则解析表达式(单位:天)  0,365
     */
    private String value;
    /**
     * 描述(说明) eg: X<28天
     */
    private String description;
    /**
     * 状态：0禁用，1启用
     */
    private Integer status;
    /**
     * 有效：0否，1是
     */
    private Integer yn;
    /**
     * 系统业务类型 1-默认, 2-系统字典 3-推荐字典
     */
    private Integer sysType;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 修改人
     */
    private String updateUser;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 相关中台六级分类集合
     */
    // List<SaasCategoryVo> categorys;

    /**
     * 提示级别：0默认空不限,1提醒,2慎用,3忌用,4禁用
     */
    private Integer caution;
}
