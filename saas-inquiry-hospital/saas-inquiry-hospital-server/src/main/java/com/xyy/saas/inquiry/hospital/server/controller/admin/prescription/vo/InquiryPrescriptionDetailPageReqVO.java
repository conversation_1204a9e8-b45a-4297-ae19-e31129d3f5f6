package com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 处方记录详情分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InquiryPrescriptionDetailPageReqVO extends PageParam {

    @Schema(description = "处方编号")
    private String prescriptionPref;

    @Schema(description = "问诊单pref")
    private String inquiryPref;

    @Schema(description = "租户名称", example = "李四")
    private String tenantName;

    @Schema(description = "商品编码")
    private String productPref;

    @Schema(description = "标准库id", example = "8400")
    private String standardId;

    @Schema(description = "商品名称", example = "王五")
    private String productName;

    @Schema(description = "通用名称", example = "王五")
    private String commonName;

    @Schema(description = "用药方法eg:口服")
    private String directions;

    @Schema(description = "单次剂量 eg:1")
    private String singleDose;

    @Schema(description = "单次剂量单位 eg:片")
    private String singleUnit;

    @Schema(description = "使用频率 eg:一日三次")
    private String useFrequency;

    @Schema(description = "药品类型：0西药，1中药", example = "1")
    private Integer medicineType;

    @Schema(description = "数量")
    private BigDecimal quantity;

    @Schema(description = "商品规格")
    private String attributeSpecification;

    @Schema(description = "生产厂家")
    private String manufacturer;

    @Schema(description = "产地")
    private String producingArea;

    @Schema(description = "批准文号")
    private String approvalNumber;

    @Schema(description = "包装单位名称")
    private String packageUnit;

    @Schema(description = "商品系统类型", example = "1")
    private Integer productSystemType;

    @Schema(description = "是否处方药:0否，1是")
    private Integer prescriptionYn;

    @Schema(description = "商品价格", example = "8211")
    private BigDecimal productPrice;

    @Schema(description = "实收金额")
    private BigDecimal actualAmount;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}