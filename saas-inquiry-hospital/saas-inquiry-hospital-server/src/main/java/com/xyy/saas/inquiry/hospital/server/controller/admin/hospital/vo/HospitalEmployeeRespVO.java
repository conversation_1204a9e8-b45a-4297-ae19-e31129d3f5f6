package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * 医院员工响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 医院员工响应 VO")
@Data
public class HospitalEmployeeRespVO {

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "用户账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "yudao")
    private String username;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    private String nickname;

    @Schema(description = "备注", example = "我是一个用户")
    private String remark;

    @Schema(description = "部门ID", example = "我是一个用户")
    private Long deptId;

    @Schema(description = "部门名称", example = "IT 部")
    private String deptName;

    @Schema(description = "岗位编号数组", example = "1")
    private Set<Long> postIds;

    @Schema(description = "用户邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "手机号码", example = "15601691300")
    private String mobile;

    @Schema(description = "身份证号", example = "******************")
    private String idCard;

    @Schema(description = "用户性别，参见 SexEnum 枚举类", example = "1")
    @DictFormat(DictTypeConstants.USER_SEX)
    private Integer sex;

    @Schema(description = "用户头像", example = "https://www.iocoder.cn/xxx.png")
    private String avatar;

    @Schema(description = "状态，参见 CommonStatusEnum 枚举类", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @DictFormat(DictTypeConstants.COMMON_STATUS)
    private Integer status;

    @Schema(description = "账号状态，参见 CommonStatusEnum 枚举类", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @DictFormat(DictTypeConstants.COMMON_STATUS)
    private Integer accountStatus;

    /**
     * 是否需要打卡 (0是 1否) {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "是否需要打卡 (0是 1否)")
    private Integer needClockIn;

    /**
     * 是否打卡 (0是 1否) {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "是否打卡 (0是 1否)")
    private Integer clockInStatus;


    @Schema(description = "最后登录 IP", requiredMode = Schema.RequiredMode.REQUIRED, example = "***********")
    private String loginIp;

    @Schema(description = "最后登录时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "时间戳格式")
    private LocalDateTime loginDate;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "时间戳格式")
    private LocalDateTime createTime;

    // 门店相关信息
    @Schema(description = "所属门店id")
    private Long tenantId;

    @Schema(description = "门店数量")
    private Long tenantCount;

    @Schema(description = "门店名称")
    private String tenantName;

    @Schema(description = "角色名称集合")
    private Set<String> roleNames;

    @Schema(description = "角色code集合")
    private Set<String> roleCodes;

    @Schema(description = "角色ids")
    private Set<Long> roleIds;

    @Schema(description = "门店用户关系id")
    private Long tenantUserRelationId;

    @Schema(description = "门店管理员id")
    private Long tenantAdminUserId;

    @Schema(description = "当前用户是否仅关联了系统租户")
    private Boolean onlyDefaultTenant;
}