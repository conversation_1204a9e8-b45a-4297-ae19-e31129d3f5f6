package com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: xucao
 * @Date: 2025/02/05 19:11
 * @Description: 医生工作台查询出参
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DoctorOperatFloorResultVO {

    @Schema(description = "医生工作台待接诊数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "3")
    private Integer waitNum;

    @Schema(description = "医生工作台已接诊数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "3")
    private Integer acceptNum;

    @Schema(description = "医生工作台列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DoctorOperatFloorRespVO> inquiryList;
}
