package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 医院绑定信息响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 医院绑定信息响应 VO")
@Data
public class HospitalBindRespVO {

    @Schema(description = "医院员工绑定关系id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "医院编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "HOSPITAL_001")
    private String hospitalPref;

    @Schema(description = "医院名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "XX医院")
    private String hospitalName;

    @Schema(description = "绑定状态，0-绑定，1-解绑", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer bindStatus;

    @Schema(description = "绑定时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime bindTime;

}