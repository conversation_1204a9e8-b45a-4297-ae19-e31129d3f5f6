package com.xyy.saas.inquiry.hospital.server.hospital;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalRespDto;
import com.xyy.saas.inquiry.hospital.server.BaseIntegrationTest;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.hospital.InquiryHospitalMapper;
import com.xyy.saas.inquiry.hospital.server.service.hospital.InquiryHospitalServiceImpl;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.*;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_HOSPITAL_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;


/**
 * {@link InquiryHospitalServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(InquiryHospitalServiceImpl.class)
public class InquiryHospitalServiceImplTest extends BaseIntegrationTest {

    @Resource
    private InquiryHospitalServiceImpl inquiryHospitalService;

    @Resource
    private InquiryHospitalMapper inquiryHospitalMapper;


    @BeforeEach
    public void before() {
        RequestAttributes requestAttributes = new ServletRequestAttributes(new MockHttpServletRequest());
        requestAttributes.setAttribute("login_user_id", 0L, RequestAttributes.SCOPE_REQUEST);
        RequestContextHolder.setRequestAttributes(requestAttributes);
//        TenantContextHolder.setTenantId();
    }

    @Test
    public void testCreateInquiryHospital_success() {
        // 准备参数
        InquiryHospitalSaveReqVO createReqVO = randomPojo(InquiryHospitalSaveReqVO.class);
        createReqVO.setLevel(2);
        // 调用
        Long inquiryHospitalId = inquiryHospitalService.createInquiryHospital(createReqVO);
        // 断言
        assertNotNull(inquiryHospitalId);
        // 校验记录的属性是否正确
        InquiryHospitalDO inquiryHospital = inquiryHospitalMapper.selectById(inquiryHospitalId);
        assertPojoEquals(createReqVO, inquiryHospital, "id");
    }

    @Test
    public void testUpdateInquiryHospital_success() {
        // mock 数据
        InquiryHospitalDO dbInquiryHospital = randomPojo(InquiryHospitalDO.class);
        dbInquiryHospital.setLevel(2);
        inquiryHospitalMapper.insert(dbInquiryHospital);// @Sql: 先插入出一条存在的数据
        // 准备参数
        InquiryHospitalSaveReqVO updateReqVO = randomPojo(InquiryHospitalSaveReqVO.class, o -> {
            o.setId(dbInquiryHospital.getId()); // 设置更新的 ID
        });
        updateReqVO.setLevel(3);
        // 调用
        inquiryHospitalService.updateInquiryHospital(updateReqVO);
        // 校验是否更新正确
        InquiryHospitalDO inquiryHospital = inquiryHospitalMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, inquiryHospital);
    }

    @Test
    public void testUpdateInquiryHospital_notExists() {
        // 准备参数
        InquiryHospitalSaveReqVO updateReqVO = randomPojo(InquiryHospitalSaveReqVO.class);
        updateReqVO.setLevel(4);
        // 调用, 并断言异常
        assertServiceException(() -> inquiryHospitalService.updateInquiryHospital(updateReqVO), INQUIRY_HOSPITAL_NOT_EXISTS);
    }

    @Test
    public void testDeleteInquiryHospital_success() {
        // mock 数据
        InquiryHospitalDO dbInquiryHospital = randomPojo(InquiryHospitalDO.class);
        dbInquiryHospital.setLevel(5);
        inquiryHospitalMapper.insert(dbInquiryHospital);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbInquiryHospital.getId();

        // 调用
        inquiryHospitalService.deleteInquiryHospital(id);
        // 校验数据不存在了
        assertNull(inquiryHospitalMapper.selectById(id));
    }

    @Test
    public void testDeleteInquiryHospital_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> inquiryHospitalService.deleteInquiryHospital(id), INQUIRY_HOSPITAL_NOT_EXISTS);
    }

    @Test
    public void testGetInquiryHospitalPage() {
        // mock 数据
        InquiryHospitalDO dbInquiryHospital = randomPojo(InquiryHospitalDO.class, o -> { // 等会查询到
            o.setName("test-hospital");
            o.setLevel(2);
            o.setAddress("test-address");
            o.setPhone("test-phone");
            o.setEmail("test-email");
            o.setWebsite("test-webSite");
//            o.setHasMedicare(true);
//            o.setDisable(false);
            o.setCreateTime(LocalDateTime.now());
        });
        inquiryHospitalMapper.insert(dbInquiryHospital);
        // 测试 name 不匹配
        inquiryHospitalMapper.insert(cloneIgnoreId(dbInquiryHospital, o -> o.setName("")));
        // 测试 level 不匹配
        inquiryHospitalMapper.insert(cloneIgnoreId(dbInquiryHospital, o -> o.setLevel(o.getLevel() + 1)));
        // 测试 address 不匹配
        inquiryHospitalMapper.insert(cloneIgnoreId(dbInquiryHospital, o -> o.setAddress("")));
        // 测试 phone 不匹配
        inquiryHospitalMapper.insert(cloneIgnoreId(dbInquiryHospital, o -> o.setPhone("")));
        // 测试 email 不匹配
        inquiryHospitalMapper.insert(cloneIgnoreId(dbInquiryHospital, o -> o.setEmail("")));
        // 测试 website 不匹配
        inquiryHospitalMapper.insert(cloneIgnoreId(dbInquiryHospital, o -> o.setWebsite("")));
        // 测试 hasMedicare 不匹配
//        inquiryHospitalMapper.insert(cloneIgnoreId(dbInquiryHospital, o -> o.setHasMedicare(false)));
        // 测试 disable 不匹配
//        inquiryHospitalMapper.insert(cloneIgnoreId(dbInquiryHospital, o -> o.setDisable(false)));
        // 测试 createTime 不匹配
        inquiryHospitalMapper.insert(cloneIgnoreId(dbInquiryHospital, o -> o.setCreateTime(null)));
        // 准备参数
        InquiryHospitalPageReqVO reqVO = new InquiryHospitalPageReqVO();
        reqVO.setLevel(3);
        // 调用
        PageResult<InquiryHospitalRespDto> pageResult = inquiryHospitalService.getInquiryHospitalPage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        /*assertPojoEquals(dbInquiryHospital, pageResult.getList().get(0));*/
    }

}