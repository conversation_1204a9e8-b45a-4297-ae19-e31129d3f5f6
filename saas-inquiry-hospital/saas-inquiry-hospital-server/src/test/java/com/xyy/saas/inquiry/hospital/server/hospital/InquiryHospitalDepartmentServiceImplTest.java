package com.xyy.saas.inquiry.hospital.server.hospital;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.xyy.saas.inquiry.hospital.server.BaseIntegrationTest;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDepartmentDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.hospital.InquiryHospitalDepartmentMapper;
import com.xyy.saas.inquiry.hospital.server.service.hospital.InquiryHospitalDepartmentServiceImpl;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_HOSPITAL_DEPARTMENT_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link InquiryHospitalDepartmentServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(InquiryHospitalDepartmentServiceImpl.class)
public class InquiryHospitalDepartmentServiceImplTest extends BaseIntegrationTest {

    @Resource
    private InquiryHospitalDepartmentServiceImpl inquiryHospitalDepartmentService;

    @Resource
    private InquiryHospitalDepartmentMapper inquiryHospitalDepartmentMapper;

    @BeforeEach
    public void before() {
        RequestAttributes requestAttributes = new ServletRequestAttributes(new MockHttpServletRequest());
        requestAttributes.setAttribute("login_user_id", 0L, RequestAttributes.SCOPE_REQUEST);
        RequestContextHolder.setRequestAttributes(requestAttributes);
//        TenantContextHolder.setTenantId();
    }

    @Test
    public void testCreateInquiryHospitalDepartment_success() {
        // 准备参数
        InquiryHospitalDepartmentSaveReqVO createReqVO = randomPojo(InquiryHospitalDepartmentSaveReqVO.class);
        createReqVO.setId(null);

        // 调用
        Long inquiryHospitalDepartmentId = inquiryHospitalDepartmentService.createInquiryHospitalDepartment(createReqVO);
        // 断言
        assertNotNull(inquiryHospitalDepartmentId);
        // 校验记录的属性是否正确
        InquiryHospitalDepartmentDO inquiryHospitalDepartment = inquiryHospitalDepartmentMapper.selectById(inquiryHospitalDepartmentId);
        assertPojoEquals(createReqVO, inquiryHospitalDepartment, "id");
    }

    @Test
    public void testUpdateInquiryHospitalDepartment_success() {
        // mock 数据
        InquiryHospitalDepartmentDO dbInquiryHospitalDepartment = randomPojo(InquiryHospitalDepartmentDO.class);
        dbInquiryHospitalDepartment.setId(null);
        inquiryHospitalDepartmentMapper.insert(dbInquiryHospitalDepartment);// @Sql: 先插入出一条存在的数据
        // 准备参数
        InquiryHospitalDepartmentSaveReqVO updateReqVO = randomPojo(InquiryHospitalDepartmentSaveReqVO.class, o -> {
            o.setId(dbInquiryHospitalDepartment.getId()); // 设置更新的 ID
        });

        // 调用
        inquiryHospitalDepartmentService.updateInquiryHospitalDepartment(updateReqVO);
        // 校验是否更新正确
        InquiryHospitalDepartmentDO inquiryHospitalDepartment = inquiryHospitalDepartmentMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, inquiryHospitalDepartment);
    }

    @Test
    public void testUpdateInquiryHospitalDepartment_notExists() {
        // 准备参数
        InquiryHospitalDepartmentSaveReqVO updateReqVO = randomPojo(InquiryHospitalDepartmentSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> inquiryHospitalDepartmentService.updateInquiryHospitalDepartment(updateReqVO), INQUIRY_HOSPITAL_DEPARTMENT_NOT_EXISTS);
    }

    @Test
    public void testDeleteInquiryHospitalDepartment_success() {
        // mock 数据
        InquiryHospitalDepartmentDO dbInquiryHospitalDepartment = randomPojo(InquiryHospitalDepartmentDO.class);
        dbInquiryHospitalDepartment.setId(null);
        inquiryHospitalDepartmentMapper.insert(dbInquiryHospitalDepartment);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbInquiryHospitalDepartment.getId();

        // 调用
        inquiryHospitalDepartmentService.deleteInquiryHospitalDepartment(id);
        // 校验数据不存在了
        assertNull(inquiryHospitalDepartmentMapper.selectById(id));
    }

    @Test
    public void testDeleteInquiryHospitalDepartment_notExists() {
        // 准备参数
//        Integer id = randomInteger();
//
//        // 调用, 并断言异常
//        assertServiceException(() -> inquiryHospitalDepartmentService.deleteInquiryHospitalDepartment(id), INQUIRY_HOSPITAL_DEPARTMENT_NOT_EXISTS);
    }

    @Test
    public void testGetInquiryHospitalDepartmentPage() {
        // mock 数据
        InquiryHospitalDepartmentDO dbInquiryHospitalDepartment = randomPojo(InquiryHospitalDepartmentDO.class, o -> { // 等会查询到
            o.setDeptName("内科");
            o.setDeptOrder(11);
            o.setStatus(0);
            o.setCreateTime(LocalDateTime.now());
        });
        inquiryHospitalDepartmentMapper.insert(dbInquiryHospitalDepartment);
        // 测试 deptName 不匹配
        inquiryHospitalDepartmentMapper.insert(cloneIgnoreId(dbInquiryHospitalDepartment, o -> o.setDeptName("消化内科")));
        // 测试 deptParentId 不匹配
        // 测试 deptOrder 不匹配
        inquiryHospitalDepartmentMapper.insert(cloneIgnoreId(dbInquiryHospitalDepartment, o -> o.setDeptOrder(12)));
        // 测试 status 不匹配
        inquiryHospitalDepartmentMapper.insert(cloneIgnoreId(dbInquiryHospitalDepartment, o -> o.setStatus(0)));
        // 测试 createTime 不匹配
        inquiryHospitalDepartmentMapper.insert(cloneIgnoreId(dbInquiryHospitalDepartment, o -> o.setCreateTime(LocalDateTime.now())));
        // 准备参数
        InquiryHospitalDepartmentPageReqVO reqVO = new InquiryHospitalDepartmentPageReqVO();
        reqVO.setDeptName("消化内科");

        // 调用
        PageResult<InquiryHospitalDepartmentDO> pageResult = inquiryHospitalDepartmentService.getInquiryHospitalDepartmentPage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        // assertPojoEquals(dbInquiryHospitalDepartment, pageResult.getList().get(0));
    }

}