package com.xyy.saas.inquiry.hospital.server.diagnosis;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_DIAGNOSIS_DEPARTMENT_RELATION_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.xyy.saas.inquiry.hospital.server.BaseIntegrationTest;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryDiagnosisDepartmentRelationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryDiagnosisDepartmentRelationSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.diagnosis.InquiryDiagnosisDepartmentRelationDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.diagnosis.InquiryDiagnosisDepartmentRelationMapper;
import com.xyy.saas.inquiry.hospital.server.service.diagnosis.InquiryDiagnosisDepartmentRelationServiceImpl;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

/**
 * {@link InquiryDiagnosisDepartmentRelationServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(InquiryDiagnosisDepartmentRelationServiceImpl.class)
public class InquiryDiagnosisDepartmentRelationServiceImplTest extends BaseIntegrationTest {

    @Resource
    private InquiryDiagnosisDepartmentRelationServiceImpl inquiryDiagnosisDepartmentRelationService;

    @Resource
    private InquiryDiagnosisDepartmentRelationMapper inquiryDiagnosisDepartmentRelationMapper;

    @Test
    public void testCreateInquiryDiagnosisDepartmentRelation_success() {
        // 准备参数
        InquiryDiagnosisDepartmentRelationSaveReqVO createReqVO = randomPojo(InquiryDiagnosisDepartmentRelationSaveReqVO.class);
        createReqVO.setId(null);

        // 调用
        inquiryDiagnosisDepartmentRelationService.createInquiryDiagnosisDepartmentRelation(createReqVO);
        // 断言
        // assertNotNull(inquiryDiagnosisDepartmentRelationId);
        // 校验记录的属性是否正确
        // InquiryDiagnosisDepartmentRelationDO inquiryDiagnosisDepartmentRelation = inquiryDiagnosisDepartmentRelationMapper.selectById(inquiryDiagnosisDepartmentRelationId);
        // assertPojoEquals(createReqVO, inquiryDiagnosisDepartmentRelation, "id");
    }

    @Test
    public void testUpdateInquiryDiagnosisDepartmentRelation_success() {
        // mock 数据
        InquiryDiagnosisDepartmentRelationDO dbInquiryDiagnosisDepartmentRelation = randomPojo(InquiryDiagnosisDepartmentRelationDO.class);
        inquiryDiagnosisDepartmentRelationMapper.insert(dbInquiryDiagnosisDepartmentRelation);// @Sql: 先插入出一条存在的数据
        // 准备参数
        InquiryDiagnosisDepartmentRelationSaveReqVO updateReqVO = randomPojo(InquiryDiagnosisDepartmentRelationSaveReqVO.class, o -> {
            o.setId(dbInquiryDiagnosisDepartmentRelation.getId()); // 设置更新的 ID
        });

        // 调用
        inquiryDiagnosisDepartmentRelationService.updateInquiryDiagnosisDepartmentRelation(updateReqVO);
        // 校验是否更新正确
        InquiryDiagnosisDepartmentRelationDO inquiryDiagnosisDepartmentRelation = inquiryDiagnosisDepartmentRelationMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, inquiryDiagnosisDepartmentRelation);
    }

    @Test
    public void testUpdateInquiryDiagnosisDepartmentRelation_notExists() {
        // 准备参数
        InquiryDiagnosisDepartmentRelationSaveReqVO updateReqVO = randomPojo(InquiryDiagnosisDepartmentRelationSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> inquiryDiagnosisDepartmentRelationService.updateInquiryDiagnosisDepartmentRelation(updateReqVO), INQUIRY_DIAGNOSIS_DEPARTMENT_RELATION_NOT_EXISTS);
    }

    @Test
    public void testDeleteInquiryDiagnosisDepartmentRelation_success() {
        // mock 数据
        InquiryDiagnosisDepartmentRelationDO dbInquiryDiagnosisDepartmentRelation = randomPojo(InquiryDiagnosisDepartmentRelationDO.class);
        inquiryDiagnosisDepartmentRelationMapper.insert(dbInquiryDiagnosisDepartmentRelation);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbInquiryDiagnosisDepartmentRelation.getId();

        // 调用
        inquiryDiagnosisDepartmentRelationService.deleteInquiryDiagnosisDepartmentRelation(id);
        // 校验数据不存在了
        assertNull(inquiryDiagnosisDepartmentRelationMapper.selectById(id));
    }

    @Test
    public void testDeleteInquiryDiagnosisDepartmentRelation_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> inquiryDiagnosisDepartmentRelationService.deleteInquiryDiagnosisDepartmentRelation(id), INQUIRY_DIAGNOSIS_DEPARTMENT_RELATION_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetInquiryDiagnosisDepartmentRelationPage() {
        // mock 数据
        InquiryDiagnosisDepartmentRelationDO dbInquiryDiagnosisDepartmentRelation = randomPojo(InquiryDiagnosisDepartmentRelationDO.class, o -> { // 等会查询到
            o.setDiagnosisCode(null);
            o.setDiagnosisName(null);
            o.setDeptId(null);
            o.setDeptPref(null);
            o.setDeptName(null);
            o.setCreateTime(null);
        });
        inquiryDiagnosisDepartmentRelationMapper.insert(dbInquiryDiagnosisDepartmentRelation);
        // 测试 diagnosisCode 不匹配
        inquiryDiagnosisDepartmentRelationMapper.insert(cloneIgnoreId(dbInquiryDiagnosisDepartmentRelation, o -> o.setDiagnosisCode(null)));
        // 测试 diagnosisName 不匹配
        inquiryDiagnosisDepartmentRelationMapper.insert(cloneIgnoreId(dbInquiryDiagnosisDepartmentRelation, o -> o.setDiagnosisName(null)));
        // 测试 deptId 不匹配
        inquiryDiagnosisDepartmentRelationMapper.insert(cloneIgnoreId(dbInquiryDiagnosisDepartmentRelation, o -> o.setDeptId(null)));
        // 测试 deptPref 不匹配
        inquiryDiagnosisDepartmentRelationMapper.insert(cloneIgnoreId(dbInquiryDiagnosisDepartmentRelation, o -> o.setDeptPref(null)));
        // 测试 deptName 不匹配
        inquiryDiagnosisDepartmentRelationMapper.insert(cloneIgnoreId(dbInquiryDiagnosisDepartmentRelation, o -> o.setDeptName(null)));
        // 测试 createTime 不匹配
        inquiryDiagnosisDepartmentRelationMapper.insert(cloneIgnoreId(dbInquiryDiagnosisDepartmentRelation, o -> o.setCreateTime(null)));
        // 准备参数
        InquiryDiagnosisDepartmentRelationPageReqVO reqVO = new InquiryDiagnosisDepartmentRelationPageReqVO();
        reqVO.setDiagnosisCode(null);
        reqVO.setDiagnosisName(null);
        reqVO.setShowName(null);
        reqVO.setDeptId(null);
        reqVO.setDeptPref(null);
        reqVO.setDeptName(null);
        reqVO.setDisable(null);
        reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

        // 调用
        PageResult<InquiryDiagnosisDepartmentRelationDO> pageResult = inquiryDiagnosisDepartmentRelationService.getInquiryDiagnosisDepartmentRelationPage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbInquiryDiagnosisDepartmentRelation, pageResult.getList().get(0));
    }

}