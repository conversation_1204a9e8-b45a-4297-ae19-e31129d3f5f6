package com.xyy.saas.inquiry.hospital.server.prescription;

import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_PRESCRIPTION_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.xyy.saas.inquiry.hospital.server.BaseIntegrationTest;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.prescription.InquiryPrescriptionMapper;
import com.xyy.saas.inquiry.hospital.server.service.prescription.InquiryPrescriptionServiceImpl;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

/**
 * {@link InquiryPrescriptionServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(InquiryPrescriptionServiceImpl.class)
public class InquiryPrescriptionServiceImplTest extends BaseIntegrationTest {

    @Resource
    private InquiryPrescriptionServiceImpl inquiryPrescriptionService;

    @Resource
    private InquiryPrescriptionMapper inquiryPrescriptionMapper;

    @Test
    public void testCreateInquiryPrescription_success() {
        // 准备参数
        InquiryPrescriptionSaveReqVO createReqVO = randomPojo(InquiryPrescriptionSaveReqVO.class);
        createReqVO.setId(null);

        // 调用
        // InquiryPrescriptionRespDTO inquiryPrescriptionId = inquiryPrescriptionService.createInquiryPrescription(createReqVO);
        // // 断言
        // assertNotNull(inquiryPrescriptionId);
        // // 校验记录的属性是否正确
        // InquiryPrescriptionDO inquiryPrescription = inquiryPrescriptionMapper.selectById(inquiryPrescriptionId);
        // assertPojoEquals(createReqVO, inquiryPrescription, "id");
    }

    @Test
    public void testUpdateInquiryPrescription_success() {
        // mock 数据
        InquiryPrescriptionDO dbInquiryPrescription = randomPojo(InquiryPrescriptionDO.class);
        inquiryPrescriptionMapper.insert(dbInquiryPrescription);// @Sql: 先插入出一条存在的数据
        // 准备参数
        InquiryPrescriptionSaveReqVO updateReqVO = randomPojo(InquiryPrescriptionSaveReqVO.class, o -> {
            o.setId(dbInquiryPrescription.getId()); // 设置更新的 ID
        });

        // 调用
        inquiryPrescriptionService.updateInquiryPrescription(updateReqVO);
        // 校验是否更新正确
        InquiryPrescriptionDO inquiryPrescription = inquiryPrescriptionMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, inquiryPrescription);
    }

    @Test
    public void testUpdateInquiryPrescription_notExists() {
        // 准备参数
        InquiryPrescriptionSaveReqVO updateReqVO = randomPojo(InquiryPrescriptionSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> inquiryPrescriptionService.updateInquiryPrescription(updateReqVO), INQUIRY_PRESCRIPTION_NOT_EXISTS);
    }

    @Test
    public void testDeleteInquiryPrescription_success() {
        // mock 数据
        InquiryPrescriptionDO dbInquiryPrescription = randomPojo(InquiryPrescriptionDO.class);
        inquiryPrescriptionMapper.insert(dbInquiryPrescription);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbInquiryPrescription.getId();

        // 调用
        inquiryPrescriptionService.deleteInquiryPrescription(id);
        // 校验数据不存在了
        assertNull(inquiryPrescriptionMapper.selectById(id));
    }

    @Test
    public void testDeleteInquiryPrescription_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> inquiryPrescriptionService.deleteInquiryPrescription(id), INQUIRY_PRESCRIPTION_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetInquiryPrescriptionPage() {
        // mock 数据
        InquiryPrescriptionDO dbInquiryPrescription = randomPojo(InquiryPrescriptionDO.class, o -> { // 等会查询到
            o.setDeptName(null);
            o.setPatientName(null);
            o.setPatientSex(null);
            o.setPatientAge(null);
            o.setPatientMobile(null);
            o.setDoctorName(null);
            o.setAutoInquiry(null);
            o.setAuditLevel(null);
            o.setPref(null);
            o.setStatus(null);
            o.setMedicineType(null);
            o.setUseStatus(null);
            o.setInvalidReason(null);
            o.setInvalidTime(null);
            o.setFeeType(null);
            o.setOutPrescriptionTime(null);
            o.setOrderNo(null);
            o.setMainSuit(null);
            o.setDiagnosisCode(null);
            o.setDeptName(null);
            o.setPrescriptionImgUrl(null);
            o.setPrescriptionPdfUrl(null);
            o.setCaseImgUrl(null);
            o.setInquiryWayType(null);
            o.setInquiryBizType(null);
            o.setClientChannelType(null);
            o.setDoctorChannelType(null);
            o.setDoctorOsType(null);
            o.setBizChannelType(null);
            o.setPrintStatus(null);
            o.setExt(null);
            o.setCreateTime(null);
        });
        inquiryPrescriptionMapper.insert(dbInquiryPrescription);
        // 测试 deptName 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setDeptName(null)));
        // 测试 patientName 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setPatientName(null)));
        // 测试 patientSex 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setPatientSex(null)));
        // 测试 patientAge 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setPatientAge(null)));
        // 测试 patientMobile 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setPatientMobile(null)));
        // 测试 doctorName 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setDoctorName(null)));
        // 测试 autoInquiry 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setAutoInquiry(null)));
        // 测试 auditLevel 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setAuditLevel(null)));
        // 测试 pref 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setPref(null)));
        // 测试 status 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setStatus(null)));
        // 测试 medicineType 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setMedicineType(null)));
        // 测试 useStatus 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setUseStatus(null)));
        // 测试 invalidReason 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setInvalidReason(null)));
        // 测试 invalidTime 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setInvalidTime(null)));
        // 测试 feeType 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setFeeType(null)));
        // 测试 outPrescriptionTime 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setOutPrescriptionTime(null)));
        // 测试 prescriptionEndTime 不匹配
        // 测试 orderNo 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setOrderNo(null)));
        // 测试 mainSuit 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setMainSuit(null)));
        // 测试 diagnosisCode 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setDiagnosisCode(null)));
        // 测试 diagnosis 不匹配
        // 测试 prescriptionImgUrl 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setPrescriptionImgUrl(null)));
        // 测试 prescriptionPdfUrl 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setPrescriptionPdfUrl(null)));
        // 测试 caseImgUrl 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setCaseImgUrl(null)));
        // 测试 inquiryWayType 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setInquiryWayType(null)));
        // 测试 inquiryBizType 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setInquiryBizType(null)));
        // 测试 clinetChannelType 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setClientChannelType(null)));
        // 测试 doctorChannelType 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setDoctorChannelType(null)));
        // 测试 doctorOsType 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setDoctorOsType(null)));
        // 测试 bizChannelType 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setBizChannelType(null)));
        // 测试 printStatus 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setPrintStatus(null)));
        // 测试 ext 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setExt(null)));
        // 测试 createTime 不匹配
        inquiryPrescriptionMapper.insert(cloneIgnoreId(dbInquiryPrescription, o -> o.setCreateTime(null)));
        // 准备参数
        InquiryPrescriptionPageReqVO reqVO = new InquiryPrescriptionPageReqVO();
        reqVO.setPref(null);
        reqVO.setStatus(null);
        reqVO.setMedicineType(null);
        reqVO.setInquiryWayType(null);
        reqVO.setInquiryBizType(null);
        reqVO.setClientChannelType(null);
        reqVO.setBizChannelType(null);
        reqVO.setPrintStatus(null);

        // 调用
        PageResult<InquiryPrescriptionRespVO> pageResult = inquiryPrescriptionService.getInquiryPrescriptionPage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbInquiryPrescription, pageResult.getList().get(0));
    }

}