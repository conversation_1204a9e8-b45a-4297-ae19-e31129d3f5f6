package com.xyy.saas.inquiry.hospital.server.doctor;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.xyy.saas.inquiry.hospital.server.BaseIntegrationTest;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorBillingPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorBillingSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorBillingDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.DoctorBillingMapper;
import com.xyy.saas.inquiry.hospital.server.service.doctor.DoctorBillingServiceImpl;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.DOCTOR_BILLING_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link DoctorBillingServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(DoctorBillingServiceImpl.class)
public class DoctorBillingServiceImplTest extends BaseIntegrationTest {

    @Resource
    private DoctorBillingServiceImpl doctorBillingService;

    @Resource
    private DoctorBillingMapper doctorBillingMapper;


    @BeforeEach
    public void before() {
        RequestAttributes requestAttributes = new ServletRequestAttributes(new MockHttpServletRequest());
        requestAttributes.setAttribute("login_user_id", 0L, RequestAttributes.SCOPE_REQUEST);
        RequestContextHolder.setRequestAttributes(requestAttributes);
//        TenantContextHolder.setTenantId();
    }

    @Test
    public void testCreateDoctorBilling_success() {
        // 准备参数
        DoctorBillingSaveReqVO createReqVO = randomPojo(DoctorBillingSaveReqVO.class);
        createReqVO.setId(null);
        // 调用
        Long doctorBillingId = doctorBillingService.createDoctorBilling(createReqVO);
        // 断言
        assertNotNull(doctorBillingId);
        // 校验记录的属性是否正确
        DoctorBillingDO doctorBilling = doctorBillingMapper.selectById(doctorBillingId);
        assertPojoEquals(createReqVO, doctorBilling, "id");
    }

    @Test
    public void testUpdateDoctorBilling_success() {
        // mock 数据
        DoctorBillingDO dbDoctorBilling = randomPojo(DoctorBillingDO.class);
        doctorBillingMapper.insert(dbDoctorBilling);// @Sql: 先插入出一条存在的数据
        // 准备参数
        DoctorBillingSaveReqVO updateReqVO = randomPojo(DoctorBillingSaveReqVO.class, o -> {
            o.setId(dbDoctorBilling.getId()); // 设置更新的 ID
        });

        // 调用
        doctorBillingService.updateDoctorBilling(updateReqVO);
        // 校验是否更新正确
        DoctorBillingDO doctorBilling = doctorBillingMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, doctorBilling);
    }

    @Test
    public void testUpdateDoctorBilling_notExists() {
        // 准备参数
        DoctorBillingSaveReqVO updateReqVO = randomPojo(DoctorBillingSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> doctorBillingService.updateDoctorBilling(updateReqVO), DOCTOR_BILLING_NOT_EXISTS);
    }

    @Test
    public void testDeleteDoctorBilling_success() {
        // mock 数据
        DoctorBillingDO dbDoctorBilling = randomPojo(DoctorBillingDO.class);
        doctorBillingMapper.insert(dbDoctorBilling);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbDoctorBilling.getId();

        // 调用
        doctorBillingService.deleteDoctorBilling(id);
        // 校验数据不存在了
        assertNull(doctorBillingMapper.selectById(id));
    }

    @Test
    public void testDeleteDoctorBilling_notExists() {
        // 准备参数
//        Long id = randomLong();
//
//        // 调用, 并断言异常
//        assertServiceException(() -> doctorBillingService.deleteDoctorBilling(id), DOCTOR_BILLING_NOT_EXISTS);
    }

    @Test
    public void testGetDoctorBillingPage() {
        // mock 数据
        DoctorBillingDO dbDoctorBilling = randomPojo(DoctorBillingDO.class, o -> { // 等会查询到
            o.setPayeeName("张三");
            o.setPayeeIdCard("******************");
            o.setPayeeTelPhone("**********");
            o.setPayeeBankNo("*****************");
            o.setPayeeBankName("中国测试银行");
            o.setCreateTime(LocalDateTime.now());
        });
        doctorBillingMapper.insert(dbDoctorBilling);
        // 测试 guid 不匹配
        // 测试 payeeName 不匹配
        doctorBillingMapper.insert(cloneIgnoreId(dbDoctorBilling, o -> o.setPayeeName(null)));
        // 测试 payeeIdCard 不匹配
        doctorBillingMapper.insert(cloneIgnoreId(dbDoctorBilling, o -> o.setPayeeIdCard(null)));
        // 测试 payeeTelPhone 不匹配
        doctorBillingMapper.insert(cloneIgnoreId(dbDoctorBilling, o -> o.setPayeeTelPhone(null)));
        // 测试 payeeBankNo 不匹配
        doctorBillingMapper.insert(cloneIgnoreId(dbDoctorBilling, o -> o.setPayeeBankNo(null)));
        // 测试 payeeBankName 不匹配
        doctorBillingMapper.insert(cloneIgnoreId(dbDoctorBilling, o -> o.setPayeeBankName("光大银行")));
        // 测试 createTime 不匹配
        doctorBillingMapper.insert(cloneIgnoreId(dbDoctorBilling, o -> o.setCreateTime(null)));
        // 准备参数
        DoctorBillingPageReqVO reqVO = new DoctorBillingPageReqVO();
        reqVO.setPayeeBankName("光大银行");

        // 调用
        PageResult<DoctorBillingDO> pageResult = doctorBillingService.getDoctorBillingPage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
    }

}