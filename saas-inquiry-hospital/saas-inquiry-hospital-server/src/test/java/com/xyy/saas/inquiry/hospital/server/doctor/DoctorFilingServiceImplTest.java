package com.xyy.saas.inquiry.hospital.server.doctor;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.xyy.saas.inquiry.hospital.server.BaseIntegrationTest;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorFilingPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorFilingSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorFilingDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.DoctorFilingMapper;
import com.xyy.saas.inquiry.hospital.server.service.doctor.DoctorFilingServiceImpl;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.DOCTOR_FILING_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;


/**
 * {@link DoctorFilingServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(DoctorFilingServiceImpl.class)
public class DoctorFilingServiceImplTest extends BaseIntegrationTest {

    @Resource
    private DoctorFilingServiceImpl doctorFilingService;

    @Resource
    private DoctorFilingMapper doctorFilingMapper;

    @BeforeEach
    public void before() {
        RequestAttributes requestAttributes = new ServletRequestAttributes(new MockHttpServletRequest());
        requestAttributes.setAttribute("login_user_id", 0L, RequestAttributes.SCOPE_REQUEST);
        RequestContextHolder.setRequestAttributes(requestAttributes);
//        TenantContextHolder.setTenantId();
    }

    @Test
    public void testCreateDoctorFiling_success() {
        // 准备参数
        DoctorFilingSaveReqVO createReqVO = randomPojo(DoctorFilingSaveReqVO.class);
        createReqVO.setId(null);
        createReqVO.setNationCode(1);
        createReqVO.setFormalLevel(1);
        // 调用
        Long doctorFilingId = doctorFilingService.createDoctorFiling(createReqVO);
        // 断言
        assertNotNull(doctorFilingId);
        // 校验记录的属性是否正确
        DoctorFilingDO doctorFiling = doctorFilingMapper.selectById(doctorFilingId);
        assertPojoEquals(createReqVO, doctorFiling, "id");
    }

    @Test
    public void testUpdateDoctorFiling_success() {
        // mock 数据
        DoctorFilingDO dbDoctorFiling = randomPojo(DoctorFilingDO.class);
        dbDoctorFiling.setNationCode(1);
        dbDoctorFiling.setFormalLevel(1);
        doctorFilingMapper.insert(dbDoctorFiling);// @Sql: 先插入出一条存在的数据
        // 准备参数
        DoctorFilingSaveReqVO updateReqVO = randomPojo(DoctorFilingSaveReqVO.class, o -> {
            o.setId(dbDoctorFiling.getId()); // 设置更新的 ID
        });
        updateReqVO.setNationCode(1);
        updateReqVO.setFormalLevel(1);
        // 调用
        doctorFilingService.updateDoctorFiling(updateReqVO);
        // 校验是否更新正确
        DoctorFilingDO doctorFiling = doctorFilingMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, doctorFiling);
    }

    @Test
    public void testUpdateDoctorFiling_notExists() {
        // 准备参数
        DoctorFilingSaveReqVO updateReqVO = randomPojo(DoctorFilingSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> doctorFilingService.updateDoctorFiling(updateReqVO), DOCTOR_FILING_NOT_EXISTS);
    }

    @Test
    public void testDeleteDoctorFiling_success() {
        // mock 数据
        DoctorFilingDO dbDoctorFiling = randomPojo(DoctorFilingDO.class);
        dbDoctorFiling.setNationCode(1);
        dbDoctorFiling.setFormalLevel(1);
        doctorFilingMapper.insert(dbDoctorFiling);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbDoctorFiling.getId();

        // 调用
        doctorFilingService.deleteDoctorFiling(id);
        // 校验数据不存在了
        assertNull(doctorFilingMapper.selectById(id));
    }

    @Test
    public void testDeleteDoctorFiling_notExists() {
        // 准备参数
//        Long id = randomLong();
//
//        // 调用, 并断言异常
//        assertServiceException(() -> doctorFilingService.deleteDoctorFiling(id), DOCTOR_FILING_NOT_EXISTS);
    }

    @Test
    public void testGetDoctorFilingPage() {
        // mock 数据
        DoctorFilingDO dbDoctorFiling = randomPojo(DoctorFilingDO.class, o -> { // 等会查询到
            o.setNationCode(1);
            o.setAddress("测试地址52321sd21ffds");
            o.setFormalLevel(2);
            o.setOrgProvinceCode("420000");
            o.setRecordStatus(1);
            o.setCreateTime(LocalDateTime.now());
        });
        doctorFilingMapper.insert(dbDoctorFiling);
        // 测试 guid 不匹配
        // 测试 nationCode 不匹配
        doctorFilingMapper.insert(cloneIgnoreId(dbDoctorFiling, o -> o.setNationCode(null)));
        // 测试 address 不匹配
        doctorFilingMapper.insert(cloneIgnoreId(dbDoctorFiling, o -> o.setAddress(null)));
        // 测试 formalLevel 不匹配
        doctorFilingMapper.insert(cloneIgnoreId(dbDoctorFiling, o -> o.setFormalLevel(null)));
        // 测试 orgProvinceCode 不匹配
        doctorFilingMapper.insert(cloneIgnoreId(dbDoctorFiling, o -> o.setOrgProvinceCode(null)));
        // 测试 recordStatus 不匹配
        doctorFilingMapper.insert(cloneIgnoreId(dbDoctorFiling, o -> o.setRecordStatus(2)));
        // 测试 createTime 不匹配
        doctorFilingMapper.insert(cloneIgnoreId(dbDoctorFiling, o -> o.setCreateTime(null)));
        // 准备参数
        DoctorFilingPageReqVO reqVO = new DoctorFilingPageReqVO();
        reqVO.setRecordStatus(2);

        // 调用
        PageResult<DoctorFilingDO> pageResult = doctorFilingService.getDoctorFilingPage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
    }

}