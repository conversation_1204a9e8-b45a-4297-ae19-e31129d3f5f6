package com.xyy.saas.inquiry.hospital.server.doctor;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.xyy.saas.inquiry.hospital.server.BaseIntegrationTest;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorWorkRecordPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorWorkRecordSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorWorkRecordDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.DoctorWorkRecordMapper;
import com.xyy.saas.inquiry.hospital.server.service.doctor.DoctorWorkRecordServiceImpl;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.DOCTOR_WORK_RECORD_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link DoctorWorkRecordServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(DoctorWorkRecordServiceImpl.class)
public class DoctorWorkRecordServiceImplTest extends BaseIntegrationTest {

    @Resource
    private DoctorWorkRecordServiceImpl doctorWorkRecordService;

    @Resource
    private DoctorWorkRecordMapper doctorWorkRecordMapper;

    @BeforeEach
    public void before() {
        RequestAttributes requestAttributes = new ServletRequestAttributes(new MockHttpServletRequest());
        requestAttributes.setAttribute("login_user_id", 0L, RequestAttributes.SCOPE_REQUEST);
        RequestContextHolder.setRequestAttributes(requestAttributes);
//        TenantContextHolder.setTenantId();
    }

    @Test
    public void testCreateDoctorWorkRecord_success() {
        // 准备参数
        DoctorWorkRecordSaveReqVO createReqVO = randomPojo(DoctorWorkRecordSaveReqVO.class);
        createReqVO.setId(null);
        // 调用
        Long doctorWorkRecordId = doctorWorkRecordService.createDoctorWorkRecord(createReqVO);
        // 断言
        assertNotNull(doctorWorkRecordId);
        // 校验记录的属性是否正确
        DoctorWorkRecordDO doctorWorkRecord = doctorWorkRecordMapper.selectById(doctorWorkRecordId);
        assertPojoEquals(createReqVO, doctorWorkRecord, "id");
    }

    @Test
    public void testUpdateDoctorWorkRecord_success() {
        // mock 数据
        DoctorWorkRecordDO dbDoctorWorkRecord = randomPojo(DoctorWorkRecordDO.class);
        doctorWorkRecordMapper.insert(dbDoctorWorkRecord);// @Sql: 先插入出一条存在的数据
        // 准备参数
        DoctorWorkRecordSaveReqVO updateReqVO = randomPojo(DoctorWorkRecordSaveReqVO.class, o -> {
            o.setId(dbDoctorWorkRecord.getId()); // 设置更新的 ID
        });

        // 调用
        doctorWorkRecordService.updateDoctorWorkRecord(updateReqVO);
        // 校验是否更新正确
        DoctorWorkRecordDO doctorWorkRecord = doctorWorkRecordMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, doctorWorkRecord);
    }

    @Test
    public void testUpdateDoctorWorkRecord_notExists() {
        // 准备参数
        DoctorWorkRecordSaveReqVO updateReqVO = randomPojo(DoctorWorkRecordSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> doctorWorkRecordService.updateDoctorWorkRecord(updateReqVO), DOCTOR_WORK_RECORD_NOT_EXISTS);
    }

    @Test
    public void testDeleteDoctorWorkRecord_success() {
        // mock 数据
        DoctorWorkRecordDO dbDoctorWorkRecord = randomPojo(DoctorWorkRecordDO.class);
        doctorWorkRecordMapper.insert(dbDoctorWorkRecord);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbDoctorWorkRecord.getId();

        // 调用
        doctorWorkRecordService.deleteDoctorWorkRecord(id);
        // 校验数据不存在了
        assertNull(doctorWorkRecordMapper.selectById(id));
    }

    @Test
    public void testDeleteDoctorWorkRecord_notExists() {
        // 准备参数
//        Long id = randomLong();
//
//        // 调用, 并断言异常
//        assertServiceException(() -> doctorWorkRecordService.deleteDoctorWorkRecord(id), DOCTOR_WORK_RECORD_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetDoctorWorkRecordPage() {
        // mock 数据
        DoctorWorkRecordDO dbDoctorWorkRecord = randomPojo(DoctorWorkRecordDO.class, o -> { // 等会查询到
            o.setWorkUnitName("上海万事利集团");
            o.setJobPosition("高级工程师");
            o.setProver("张三");
            o.setStartDate(LocalDateTime.now());
            o.setEndDate(LocalDateTime.now());
            o.setCreateTime(LocalDateTime.now());
        });
        doctorWorkRecordMapper.insert(dbDoctorWorkRecord);
        // 测试 guid 不匹配
        // 测试 workUnitName 不匹配
        doctorWorkRecordMapper.insert(cloneIgnoreId(dbDoctorWorkRecord, o -> o.setWorkUnitName("上海花旗")));
        // 测试 jobPosition 不匹配
        doctorWorkRecordMapper.insert(cloneIgnoreId(dbDoctorWorkRecord, o -> o.setJobPosition(null)));
        // 测试 prover 不匹配
        doctorWorkRecordMapper.insert(cloneIgnoreId(dbDoctorWorkRecord, o -> o.setProver(null)));
        // 测试 startDate 不匹配
        doctorWorkRecordMapper.insert(cloneIgnoreId(dbDoctorWorkRecord, o -> o.setStartDate(null)));
        // 测试 endDate 不匹配
        doctorWorkRecordMapper.insert(cloneIgnoreId(dbDoctorWorkRecord, o -> o.setEndDate(null)));
        // 测试 createTime 不匹配
        doctorWorkRecordMapper.insert(cloneIgnoreId(dbDoctorWorkRecord, o -> o.setCreateTime(null)));
        // 准备参数
        DoctorWorkRecordPageReqVO reqVO = new DoctorWorkRecordPageReqVO();
        reqVO.setWorkUnitName("上海花旗");

        // 调用
        PageResult<DoctorWorkRecordDO> pageResult = doctorWorkRecordService.getDoctorWorkRecordPage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        // assertPojoEquals(dbDoctorWorkRecord, pageResult.getList().get(0));
    }

}