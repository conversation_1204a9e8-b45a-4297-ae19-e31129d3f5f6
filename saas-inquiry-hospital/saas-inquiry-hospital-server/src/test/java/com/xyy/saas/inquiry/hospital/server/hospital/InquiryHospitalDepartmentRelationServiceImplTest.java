package com.xyy.saas.inquiry.hospital.server.hospital;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.xyy.saas.inquiry.hospital.server.BaseIntegrationTest;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentRelationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentRelationSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDepartmentRelationDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.hospital.InquiryHospitalDepartmentRelationMapper;
import com.xyy.saas.inquiry.hospital.server.service.hospital.InquiryHospitalDepartmentRelationServiceImpl;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static org.junit.jupiter.api.Assertions.*;


/**
 * {@link InquiryHospitalDepartmentRelationServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(InquiryHospitalDepartmentRelationServiceImpl.class)
public class InquiryHospitalDepartmentRelationServiceImplTest extends BaseIntegrationTest {

    @Resource
    private InquiryHospitalDepartmentRelationServiceImpl inquiryHospitalDepartmentRelationService;

    @Resource
    private InquiryHospitalDepartmentRelationMapper inquiryHospitalDepartmentRelationMapper;


    @BeforeEach
    public void before() {
        RequestAttributes requestAttributes = new ServletRequestAttributes(new MockHttpServletRequest());
        requestAttributes.setAttribute("login_user_id", 0L, RequestAttributes.SCOPE_REQUEST);
        RequestContextHolder.setRequestAttributes(requestAttributes);
//        TenantContextHolder.setTenantId();
    }

    @Test
    public void testCreateInquiryHospitalDepartmentRelation_success() {
        // 准备参数
        InquiryHospitalDepartmentRelationSaveReqVO createReqVO = randomPojo(InquiryHospitalDepartmentRelationSaveReqVO.class);
        // 调用
        Long inquiryHospitalDepartmentRelationId = inquiryHospitalDepartmentRelationService.createInquiryHospitalDepartmentRelation(createReqVO);
        // 断言
        assertNotNull(inquiryHospitalDepartmentRelationId);
        // 校验记录的属性是否正确
        InquiryHospitalDepartmentRelationDO inquiryHospitalDepartmentRelation = inquiryHospitalDepartmentRelationMapper.selectById(inquiryHospitalDepartmentRelationId);
        assertPojoEquals(createReqVO, inquiryHospitalDepartmentRelation, "id");
    }


    @Test
    public void testDeleteInquiryHospitalDepartmentRelation_success() {
        // mock 数据
        InquiryHospitalDepartmentRelationDO dbInquiryHospitalDepartmentRelation = randomPojo(InquiryHospitalDepartmentRelationDO.class);
        inquiryHospitalDepartmentRelationMapper.insert(dbInquiryHospitalDepartmentRelation);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbInquiryHospitalDepartmentRelation.getId();

        // 调用
        inquiryHospitalDepartmentRelationService.deleteInquiryHospitalDepartmentRelation(id);
        // 校验数据不存在了
        assertNull(inquiryHospitalDepartmentRelationMapper.selectById(id));
    }

    @Test
    public void testDeleteInquiryHospitalDepartmentRelation_notExists() {
        // 准备参数
//        Integer id = randomInteger();
//
//        // 调用, 并断言异常
//        assertServiceException(() -> inquiryHospitalDepartmentRelationService.deleteInquiryHospitalDepartmentRelation(id), INQUIRY_HOSPITAL_DEPARTMENT_RELATION_NOT_EXISTS);
    }

    @Test
    public void testGetInquiryHospitalDepartmentRelationPage() {
        // mock 数据
        InquiryHospitalDepartmentRelationDO dbInquiryHospitalDepartmentRelation = randomPojo(InquiryHospitalDepartmentRelationDO.class, o -> { // 等会查询到
            o.setDeptName("内科");
            o.setDeptParentId(0L);
            o.setCreateTime(LocalDateTime.now());
        });
        inquiryHospitalDepartmentRelationMapper.insert(dbInquiryHospitalDepartmentRelation);
        // 测试 hospitalGuid 不匹配
//        inquiryHospitalDepartmentRelationMapper.insert(cloneIgnoreId(dbInquiryHospitalDepartmentRelation, o -> o.setHospitalGuid(null)));
        // 测试 deptName 不匹配
        inquiryHospitalDepartmentRelationMapper.insert(cloneIgnoreId(dbInquiryHospitalDepartmentRelation, o -> o.setDeptName(null)));
        // 测试 deptParentId 不匹配
        inquiryHospitalDepartmentRelationMapper.insert(cloneIgnoreId(dbInquiryHospitalDepartmentRelation, o -> o.setDeptParentId(null)));
        // 测试 createTime 不匹配
        inquiryHospitalDepartmentRelationMapper.insert(cloneIgnoreId(dbInquiryHospitalDepartmentRelation, o -> o.setCreateTime(null)));
        // 准备参数
        InquiryHospitalDepartmentRelationPageReqVO reqVO = new InquiryHospitalDepartmentRelationPageReqVO();

        reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

        // 调用
        PageResult<InquiryHospitalDepartmentRelationDO> pageResult = inquiryHospitalDepartmentRelationService.getInquiryHospitalDepartmentRelationPage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbInquiryHospitalDepartmentRelation, pageResult.getList().get(0));
    }

}