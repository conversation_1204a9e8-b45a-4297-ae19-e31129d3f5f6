# 首营商品审批列表查询功能实现

## 需求概述
在 saas-inquiry-product-server 中新增"首营商品审批列表查询"接口，返回内容需同时包含：
1. 审批流任务信息（流程实例 + 审批业务关联表数据）
2. 商品基本信息（ProductInfo）

## 实现方案
保持现有 BPM 相关查询链路（HistoryService → TaskService → ProcessDefinition 等），以业务类型 BpmBusinessTypeEnum.PRODUCT_FIRST_APPROVE 为固定过滤条件，从 saas_bpm_business_relation 取审批业务数据，取出业务表字段 business_pref，批量查询商品表 saas_product_info，拼装为最终结果。

## 文件/组件变更清单

### 新增文件
1. `src/main/java/com/xyy/saas/inquiry/product/server/controller/admin/bpm/ProductFirstApproveController.java`
   - 实现首营商品审批分页查询接口

2. `src/main/java/com/xyy/saas/inquiry/product/server/controller/admin/bpm/vo/ProductFirstApprovePageReqVO.java`
   - 继承 BpmBusinessRelationPageReqVO，增加商品维度查询字段

3. `src/main/java/com/xyy/saas/inquiry/product/server/controller/admin/bpm/vo/ProductFirstApproveRespVO.java`
   - 继承 BpmProcessInstanceRespVO，增加业务关联和商品信息字段

4. `src/test/java/com/xyy/saas/inquiry/product/server/controller/admin/bpm/ProductFirstApproveControllerTest.java`
   - 单元测试类

### 实现逻辑
1. 固定业务类型为 PRODUCT_FIRST_APPROVE
2. 调用 bpmBusinessRelationService.getBpmBusinessRelationPage 获取分页数据
3. 使用 HistoryService 等补全流程实例信息
4. 收集 business_pref 列表，调用 productInfoMapper.listByPref 查询商品信息
5. 组装最终返回结果：流程实例信息 + 审批业务 + 商品信息

### 权限控制
接口使用 `@PreAuthorize("@ss.hasPermission('saas:bpm:product-first-approve:query')")` 进行权限控制

## 测试验证
编写了单元测试覆盖空数据和有数据的场景，验证接口的正确性。

## 注意事项
1. 当审批业务行存在但商品行缺失时，productInfo 字段为 null
2. 当审批流历史实例查询为空时直接返回空分页 