# 审批验证扩展点实现

## 需求概述
在流程任务审批通过/拒绝时，增加验证扩展点，支持：
1. 验证审批用户的密码是否正确
2. 对接三方确认回调（如微信链接授权审批确认）

## 实现方案
创建BpmApprovalValidator接口和实现类，通过工厂类管理不同类型的验证器。在审批前调用验证器进行验证，验证通过后才能进行审批操作。

## 文件/组件变更清单

### 新增文件
1. `src/main/java/com/xyy/saas/inquiry/product/server/controller/admin/bpm/vo/BpmApprovalValidateResult.java`
   - 定义验证结果，包含是否通过、错误消息等

2. `src/main/java/com/xyy/saas/inquiry/product/server/service/bpm/validator/BpmApprovalValidator.java`
   - 定义验证接口，包含validate方法和supportBusinessTypes方法

3. `src/main/java/com/xyy/saas/inquiry/product/server/service/bpm/validator/impl/PasswordBpmApprovalValidator.java`
   - 实现密码验证逻辑

4. `src/main/java/com/xyy/saas/inquiry/product/server/service/bpm/validator/impl/ThirdPartyBpmApprovalValidator.java`
   - 实现三方确认验证逻辑

5. `src/main/java/com/xyy/saas/inquiry/product/server/service/bpm/validator/BpmApprovalValidatorFactory.java`
   - 验证器工厂类，根据业务类型和验证类型获取对应的验证器

6. `src/main/java/com/xyy/saas/inquiry/product/server/controller/admin/bpm/vo/BpmTaskApprovalValidateReqVO.java`
   - 验证请求参数VO

### 修改文件
1. `src/main/java/com/xyy/saas/inquiry/product/enums/ErrorCodeConstants.java`
   - 添加审批验证失败的错误码常量

2. `src/main/java/com/xyy/saas/inquiry/product/server/controller/admin/bpm/ProductFirstApproveController.java`
   - 添加验证接口，供前端调用

## 实现逻辑
1. 定义BpmApprovalValidator接口，包含验证方法和获取支持的业务类型方法
2. 实现PasswordBpmApprovalValidator和ThirdPartyBpmApprovalValidator两种验证器
3. 创建BpmApprovalValidatorFactory工厂类，管理不同类型的验证器
4. 在ProductFirstApproveController中添加validate接口，供前端调用
5. 前端在审批操作前调用validate接口进行验证，验证通过后才能进行审批操作

## 使用方式
1. 密码验证：
```json
{
  "taskId": "1024",
  "validatorType": "password",
  "params": {
    "password": "123456"
  }
}
```

2. 三方确认验证：
```json
{
  "taskId": "1024",
  "validatorType": "third_party",
  "params": {
    "token": "valid_token"
  }
}
```

## 扩展性
如果需要增加新的验证方式，只需要实现BpmApprovalValidator接口，并注册为Spring Bean即可，不需要修改其他代码。 