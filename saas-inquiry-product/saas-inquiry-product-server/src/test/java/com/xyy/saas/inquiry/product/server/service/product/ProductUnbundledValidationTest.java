package com.xyy.saas.inquiry.product.server.service.product;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.xyy.saas.inquiry.product.enums.ErrorCodeConstants;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductUnbundledBatchSaveReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductUnbundledSaveReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductInfoDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductInfoMapper;
import com.xyy.saas.inquiry.product.server.test.BaseIntegrationTest;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.jdbc.Sql;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 拆零商品重复拆零数量校验测试
 * 
 * <AUTHOR> Assistant
 */
class ProductUnbundledValidationTest extends BaseIntegrationTest {
    
    @Resource
    private ProductInfoService productInfoService;
    
    @Resource
    private ProductInfoMapper productInfoMapper;
    
    private static final Long TEST_TENANT_ID = 1L;
    private static final String SOURCE_PRODUCT_PREF = "SOURCE_001";
    private static final String SOURCE_PRODUCT_NAME = "测试源商品";
    
    @BeforeEach
    protected void setUp() {
        // 设置租户上下文
        TenantContextHolder.setTenantId(TEST_TENANT_ID);
        
        // 准备源商品数据
        prepareSourceProduct();
    }
    
    private void prepareSourceProduct() {
        ProductInfoDO sourceProduct = new ProductInfoDO();
        sourceProduct.setPref(SOURCE_PRODUCT_PREF);
        sourceProduct.setCommonName(SOURCE_PRODUCT_NAME);
        sourceProduct.setManufacturer("测试厂家");
        sourceProduct.setUnit("盒");
        sourceProduct.setSpec("10mg*20片");
        sourceProduct.setTenantId(TEST_TENANT_ID);
        sourceProduct.setStatus(1); // 使用中
        sourceProduct.setDisable(false);
        sourceProduct.setDeleted(false);
        
        productInfoMapper.insert(sourceProduct);
    }
    
    @Test
    void testSaveUnbundledProduct_Success() {
        // Given
        ProductUnbundledSaveReqVO request = createUnbundledRequest(10, "10片/袋", "袋");
        ProductUnbundledBatchSaveReqVO batchRequest = new ProductUnbundledBatchSaveReqVO();
        batchRequest.setProducts(List.of(request));
        
        // When
        List<Long> result = productInfoService.saveOrUpdateUnbundledProduct(batchRequest);
        
        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertNotNull(result.get(0));
        
        // 验证拆零商品已创建
        ProductInfoDO createdProduct = productInfoMapper.selectById(result.get(0));
        assertNotNull(createdProduct);
        assertEquals(SOURCE_PRODUCT_PREF, createdProduct.getSourceProductPref());
        assertEquals(10, createdProduct.getUnbundledQuantity());
        assertTrue(createdProduct.getCommonName().contains("（拆零）"));
    }
    
    @Test
    void testSaveUnbundledProduct_DuplicateQuantity_ThrowsException() {
        // Given - 先创建一个拆零商品
        ProductUnbundledSaveReqVO firstRequest = createUnbundledRequest(10, "10片/袋", "袋");
        ProductUnbundledBatchSaveReqVO firstBatch = new ProductUnbundledBatchSaveReqVO();
        firstBatch.setProducts(List.of(firstRequest));
        productInfoService.saveOrUpdateUnbundledProduct(firstBatch);
        
        // When & Then - 尝试创建相同拆零数量的商品
        ProductUnbundledSaveReqVO duplicateRequest = createUnbundledRequest(10, "10片/盒", "盒");
        ProductUnbundledBatchSaveReqVO duplicateBatch = new ProductUnbundledBatchSaveReqVO();
        duplicateBatch.setProducts(List.of(duplicateRequest));
        
        ServiceException exception = assertThrows(
            ServiceException.class,
            () -> productInfoService.saveOrUpdateUnbundledProduct(duplicateBatch)
        );
        
        assertEquals(ErrorCodeConstants.PRODUCT_UNBUNDLED_QUANTITY_DUPLICATED.getCode(), exception.getCode());
        assertTrue(exception.getMessage().contains(SOURCE_PRODUCT_NAME));
        assertTrue(exception.getMessage().contains("10"));
        assertTrue(exception.getMessage().contains("不可重复拆零"));
    }
    
    @Test
    void testSaveUnbundledProduct_DifferentQuantity_Success() {
        // Given - 先创建一个拆零商品
        ProductUnbundledSaveReqVO firstRequest = createUnbundledRequest(10, "10片/袋", "袋");
        ProductUnbundledBatchSaveReqVO firstBatch = new ProductUnbundledBatchSaveReqVO();
        firstBatch.setProducts(List.of(firstRequest));
        productInfoService.saveOrUpdateUnbundledProduct(firstBatch);
        
        // When - 创建不同拆零数量的商品
        ProductUnbundledSaveReqVO differentRequest = createUnbundledRequest(5, "5片/袋", "袋");
        ProductUnbundledBatchSaveReqVO differentBatch = new ProductUnbundledBatchSaveReqVO();
        differentBatch.setProducts(List.of(differentRequest));
        
        List<Long> result = productInfoService.saveOrUpdateUnbundledProduct(differentBatch);
        
        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertNotNull(result.get(0));
        
        // 验证两个拆零商品都存在
        List<ProductInfoDO> unbundledProducts = productInfoMapper.findUnbundledProductsBySourceProducts(
            List.of(SOURCE_PRODUCT_PREF)
        );
        assertEquals(2, unbundledProducts.size());
        
        List<Integer> quantities = unbundledProducts.stream()
            .map(ProductInfoDO::getUnbundledQuantity)
            .sorted()
            .toList();
        assertEquals(List.of(5, 10), quantities);
    }
    
    @Test
    void testSaveUnbundledProduct_BatchInternalDuplicate_ThrowsException() {
        // Given - 批次内部有重复的拆零数量
        ProductUnbundledSaveReqVO request1 = createUnbundledRequest(10, "10片/袋", "袋");
        ProductUnbundledSaveReqVO request2 = createUnbundledRequest(10, "10片/盒", "盒"); // 相同拆零数量
        
        ProductUnbundledBatchSaveReqVO batchRequest = new ProductUnbundledBatchSaveReqVO();
        batchRequest.setProducts(List.of(request1, request2));
        
        // When & Then
        ServiceException exception = assertThrows(
            ServiceException.class,
            () -> productInfoService.saveOrUpdateUnbundledProduct(batchRequest)
        );
        
        assertEquals(ErrorCodeConstants.PRODUCT_UNBUNDLED_QUANTITY_DUPLICATED.getCode(), exception.getCode());
        assertTrue(exception.getMessage().contains(SOURCE_PRODUCT_NAME));
        assertTrue(exception.getMessage().contains("10"));
    }
    
    @Test
    void testSaveUnbundledProduct_BatchDifferentQuantities_Success() {
        // Given - 批次内部有不同的拆零数量
        ProductUnbundledSaveReqVO request1 = createUnbundledRequest(5, "5片/袋", "袋");
        ProductUnbundledSaveReqVO request2 = createUnbundledRequest(10, "10片/袋", "袋");
        ProductUnbundledSaveReqVO request3 = createUnbundledRequest(20, "20片/袋", "袋");
        
        ProductUnbundledBatchSaveReqVO batchRequest = new ProductUnbundledBatchSaveReqVO();
        batchRequest.setProducts(List.of(request1, request2, request3));
        
        // When
        List<Long> result = productInfoService.saveOrUpdateUnbundledProduct(batchRequest);
        
        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.stream().allMatch(id -> id != null));
        
        // 验证所有拆零商品都创建成功
        List<ProductInfoDO> unbundledProducts = productInfoMapper.findUnbundledProductsBySourceProducts(
            List.of(SOURCE_PRODUCT_PREF)
        );
        assertEquals(3, unbundledProducts.size());
        
        List<Integer> quantities = unbundledProducts.stream()
            .map(ProductInfoDO::getUnbundledQuantity)
            .sorted()
            .toList();
        assertEquals(List.of(5, 10, 20), quantities);
    }
    
    @Test
    void testSaveUnbundledProduct_MultipleSourceProducts_Success() {
        // Given - 准备第二个源商品
        String sourceProduct2Pref = "SOURCE_002";
        String sourceProduct2Name = "测试源商品2";
        
        ProductInfoDO sourceProduct2 = new ProductInfoDO();
        sourceProduct2.setPref(sourceProduct2Pref);
        sourceProduct2.setCommonName(sourceProduct2Name);
        sourceProduct2.setManufacturer("测试厂家2");
        sourceProduct2.setUnit("瓶");
        sourceProduct2.setSpec("100ml");
        sourceProduct2.setTenantId(TEST_TENANT_ID);
        sourceProduct2.setStatus(1);
        sourceProduct2.setDisable(false);
        sourceProduct2.setDeleted(false);
        productInfoMapper.insert(sourceProduct2);
        
        // 创建不同源商品的相同拆零数量
        ProductUnbundledSaveReqVO request1 = createUnbundledRequest(SOURCE_PRODUCT_PREF, 10, "10片/袋", "袋");
        ProductUnbundledSaveReqVO request2 = createUnbundledRequest(sourceProduct2Pref, 10, "10ml/支", "支");
        
        ProductUnbundledBatchSaveReqVO batchRequest = new ProductUnbundledBatchSaveReqVO();
        batchRequest.setProducts(List.of(request1, request2));
        
        // When
        List<Long> result = productInfoService.saveOrUpdateUnbundledProduct(batchRequest);
        
        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.stream().allMatch(id -> id != null));
        
        // 验证两个不同源商品的拆零商品都创建成功
        List<ProductInfoDO> unbundledProducts1 = productInfoMapper.findUnbundledProductsBySourceProducts(
            List.of(SOURCE_PRODUCT_PREF)
        );
        List<ProductInfoDO> unbundledProducts2 = productInfoMapper.findUnbundledProductsBySourceProducts(
            List.of(sourceProduct2Pref)
        );
        
        assertEquals(1, unbundledProducts1.size());
        assertEquals(1, unbundledProducts2.size());
        assertEquals(10, unbundledProducts1.get(0).getUnbundledQuantity());
        assertEquals(10, unbundledProducts2.get(0).getUnbundledQuantity());
    }
    
    @Test
    void testSaveUnbundledProduct_NonExistentSourceProduct_ThrowsException() {
        // Given
        ProductUnbundledSaveReqVO request = createUnbundledRequest("NON_EXISTENT", 10, "10片/袋", "袋");
        ProductUnbundledBatchSaveReqVO batchRequest = new ProductUnbundledBatchSaveReqVO();
        batchRequest.setProducts(List.of(request));
        
        // When & Then
        ServiceException exception = assertThrows(
            ServiceException.class,
            () -> productInfoService.saveOrUpdateUnbundledProduct(batchRequest)
        );
        
        assertEquals(ErrorCodeConstants.PRODUCT_INFO_NOT_EXISTS.getCode(), exception.getCode());
    }
    
    @Test
    void testSaveUnbundledProduct_TenantIsolation() {
        // Given - 在不同租户下创建相同的拆零商品
        Long otherTenantId = 2L;
        
        // 先在当前租户创建拆零商品
        ProductUnbundledSaveReqVO request1 = createUnbundledRequest(10, "10片/袋", "袋");
        ProductUnbundledBatchSaveReqVO batch1 = new ProductUnbundledBatchSaveReqVO();
        batch1.setProducts(List.of(request1));
        productInfoService.saveOrUpdateUnbundledProduct(batch1);
        
        // 在另一个租户下准备相同的源商品
        TenantContextHolder.setTenantId(otherTenantId);
        ProductInfoDO sourceProductOtherTenant = new ProductInfoDO();
        sourceProductOtherTenant.setPref(SOURCE_PRODUCT_PREF);
        sourceProductOtherTenant.setCommonName(SOURCE_PRODUCT_NAME);
        sourceProductOtherTenant.setManufacturer("测试厂家");
        sourceProductOtherTenant.setUnit("盒");
        sourceProductOtherTenant.setSpec("10mg*20片");
        sourceProductOtherTenant.setTenantId(otherTenantId);
        sourceProductOtherTenant.setStatus(1);
        sourceProductOtherTenant.setDisable(false);
        sourceProductOtherTenant.setDeleted(false);
        productInfoMapper.insert(sourceProductOtherTenant);
        
        // When - 在另一个租户下创建相同拆零数量的商品（应该成功）
        ProductUnbundledSaveReqVO request2 = createUnbundledRequest(10, "10片/袋", "袋");
        ProductUnbundledBatchSaveReqVO batch2 = new ProductUnbundledBatchSaveReqVO();
        batch2.setProducts(List.of(request2));
        
        List<Long> result = productInfoService.saveOrUpdateUnbundledProduct(batch2);
        
        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertNotNull(result.get(0));
        
        // 恢复原租户上下文
        TenantContextHolder.setTenantId(TEST_TENANT_ID);
    }
    
    private ProductUnbundledSaveReqVO createUnbundledRequest(Integer unbundledQuantity, String spec, String unit) {
        return createUnbundledRequest(SOURCE_PRODUCT_PREF, unbundledQuantity, spec, unit);
    }
    
    private ProductUnbundledSaveReqVO createUnbundledRequest(String sourceProductPref, Integer unbundledQuantity, String spec, String unit) {
        ProductUnbundledSaveReqVO request = new ProductUnbundledSaveReqVO();
        request.setSourceProductPref(sourceProductPref);
        request.setUnbundledQuantity(unbundledQuantity);
        request.setUnbundledSpec(spec);
        request.setUnbundledUnit(unit);
        request.setDisable(false);
        request.setRemark("测试拆零商品");
        return request;
    }
    
    @Override
    protected void cleanupTestData() {
        super.cleanupTestData();
        TenantContextHolder.clear();
    }
}