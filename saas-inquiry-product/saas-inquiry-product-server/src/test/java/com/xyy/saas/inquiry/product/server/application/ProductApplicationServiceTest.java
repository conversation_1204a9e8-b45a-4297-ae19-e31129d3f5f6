package com.xyy.saas.inquiry.product.server.application;

import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.server.application.orchestrator.ProductSaveOrchestrator;
import com.xyy.saas.inquiry.product.server.application.service.ProductApplicationService;
import com.xyy.saas.inquiry.product.server.builder.ProductTestDataBuilder;
import com.xyy.saas.inquiry.product.server.test.BaseUnitTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.context.ApplicationEventPublisher;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 商品应用服务单元测试
 * 
 * <AUTHOR> Assistant
 */
class ProductApplicationServiceTest extends BaseUnitTest {
    
    @Mock
    private ProductSaveOrchestrator productSaveOrchestrator;
    
    @Mock
    private ApplicationEventPublisher eventPublisher;
    
    private ProductApplicationService productApplicationService;
    
    @BeforeEach
    @Override
    protected void setUp() {
        MockitoAnnotations.openMocks(this);
        productApplicationService = new ProductApplicationService(productSaveOrchestrator, eventPublisher);
    }
    
    @Test
    void testSaveOrUpdateProduct_Success() {
        // Given
        ProductInfoDto dto = getProductBuilder()
            .commonName("测试商品")
            .manufacturer("测试厂家")
            .buildDto();
        ProductBizTypeEnum bizType = ProductBizTypeEnum.ADD_PRODUCT;
        Long expectedProductId = 123L;
        
        when(productSaveOrchestrator.execute(any())).thenReturn(expectedProductId);
        
        // When
        Long result = productApplicationService.saveOrUpdateProduct(dto, bizType);
        
        // Then
        assertEquals(expectedProductId, result);
        verify(productSaveOrchestrator).execute(any());
    }
    
    @Test
    void testSaveOrUpdateProduct_WithNullDto_ThrowsException() {
        // Given
        ProductBizTypeEnum bizType = ProductBizTypeEnum.ADD_PRODUCT;
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> productApplicationService.saveOrUpdateProduct(null, bizType)
        );
        
        assertEquals("商品信息不能为空", exception.getMessage());
        verify(productSaveOrchestrator, never()).execute(any());
    }
    
    @Test
    void testSaveOrUpdateProduct_WithNullBizType_ThrowsException() {
        // Given
        ProductInfoDto dto = getProductBuilder().buildDto();
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> productApplicationService.saveOrUpdateProduct(dto, null)
        );
        
        assertEquals("业务类型不能为空", exception.getMessage());
        verify(productSaveOrchestrator, never()).execute(any());
    }
    
    @Test
    void testSaveOrUpdateProduct_OrchestratorThrowsException_PropagatesException() {
        // Given
        ProductInfoDto dto = getProductBuilder().buildDto();
        ProductBizTypeEnum bizType = ProductBizTypeEnum.ADD_PRODUCT;
        RuntimeException orchestratorException = new RuntimeException("编排器执行失败");
        
        when(productSaveOrchestrator.execute(any())).thenThrow(orchestratorException);
        
        // When & Then
        RuntimeException thrownException = assertThrows(
            RuntimeException.class,
            () -> productApplicationService.saveOrUpdateProduct(dto, bizType)
        );
        
        assertEquals("编排器执行失败", thrownException.getMessage());
        assertSame(orchestratorException, thrownException);
        verify(productSaveOrchestrator).execute(any());
    }
    
    @Test
    void testSaveOrUpdateProduct_CommandCreatedCorrectly() {
        // Given
        ProductInfoDto dto = getProductBuilder()
            .commonName("验证命令商品")
            .manufacturer("验证命令厂家")
            .productPref("CMD001")
            .buildDto();
        ProductBizTypeEnum bizType = ProductBizTypeEnum.EDIT_PRODUCT;
        Long expectedProductId = 456L;
        
        when(productSaveOrchestrator.execute(any())).thenAnswer(invocation -> {
            var command = invocation.getArgument(0, com.xyy.saas.inquiry.product.server.application.command.ProductSaveCommand.class);
            
            // 验证命令内容
            assertNotNull(command);
            assertEquals(dto, command.getDto());
            assertEquals(bizType, command.getBizType());
            assertEquals("验证命令商品", command.getDto().getCommonName());
            assertEquals("验证命令厂家", command.getDto().getManufacturer());
            assertEquals("CMD001", command.getDto().getProductPref());
            
            return expectedProductId;
        });
        
        // When
        Long result = productApplicationService.saveOrUpdateProduct(dto, bizType);
        
        // Then
        assertEquals(expectedProductId, result);
        verify(productSaveOrchestrator).execute(any());
    }
    
    @Test
    void testSaveOrUpdateProduct_DifferentBizTypes_Success() {
        // Test different business types
        ProductInfoDto dto = getProductBuilder().buildDto();
        Long expectedProductId = 789L;
        
        when(productSaveOrchestrator.execute(any())).thenReturn(expectedProductId);
        
        // Test ADD_PRODUCT
        Long addResult = productApplicationService.saveOrUpdateProduct(dto, ProductBizTypeEnum.ADD_PRODUCT);
        assertEquals(expectedProductId, addResult);
        
        // Test EDIT_PRODUCT
        Long editResult = productApplicationService.saveOrUpdateProduct(dto, ProductBizTypeEnum.EDIT_PRODUCT);
        assertEquals(expectedProductId, editResult);
        
        // Test FIRST_PRODUCT
        Long firstResult = productApplicationService.saveOrUpdateProduct(dto, ProductBizTypeEnum.FIRST_PRODUCT);
        assertEquals(expectedProductId, firstResult);
        
        // Verify orchestrator was called 3 times
        verify(productSaveOrchestrator, times(3)).execute(any());
    }
    
    @Test
    void testSaveOrUpdateProduct_WithComplexProductDto_Success() {
        // Given - 复杂的商品DTO
        ProductInfoDto dto = getProductBuilder()
            .commonName("复杂商品名称")
            .manufacturer("复杂厂家名称")
            .unit("片")
            .specification("0.25g*20片")
            .productPref("COMPLEX001")
            .barcode("1234567890123")
            .approvalNumber("国药准字*********")
            .dosageForm("片剂")
            .isOtc(false)
            .buildDto();
        
        ProductBizTypeEnum bizType = ProductBizTypeEnum.MID_STDLIB_ADD;
        Long expectedProductId = 999L;
        
        when(productSaveOrchestrator.execute(any())).thenReturn(expectedProductId);
        
        // When
        Long result = productApplicationService.saveOrUpdateProduct(dto, bizType);
        
        // Then
        assertEquals(expectedProductId, result);
        verify(productSaveOrchestrator).execute(any());
    }
    
    @Test
    void testSaveOrUpdateProduct_ReturnsNullProductId_Success() {
        // Given
        ProductInfoDto dto = getProductBuilder().buildDto();
        ProductBizTypeEnum bizType = ProductBizTypeEnum.ADD_PRODUCT;
        
        // 编排器返回null（某些业务场景可能不返回ID）
        when(productSaveOrchestrator.execute(any())).thenReturn(null);
        
        // When
        Long result = productApplicationService.saveOrUpdateProduct(dto, bizType);
        
        // Then
        assertNull(result);
        verify(productSaveOrchestrator).execute(any());
    }
    
    @Test
    void testSaveOrUpdateProduct_EventPublisherNotCalledDirectly() {
        // Given
        ProductInfoDto dto = getProductBuilder().buildDto();
        ProductBizTypeEnum bizType = ProductBizTypeEnum.ADD_PRODUCT;
        Long expectedProductId = 123L;
        
        when(productSaveOrchestrator.execute(any())).thenReturn(expectedProductId);
        
        // When
        productApplicationService.saveOrUpdateProduct(dto, bizType);
        
        // Then - 应用服务本身不直接发布事件，事件发布由领域层处理
        verifyNoInteractions(eventPublisher);
        verify(productSaveOrchestrator).execute(any());
    }
}