package com.xyy.saas.inquiry.product.server.integration;

import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.enums.ProductStatusEnum;
import com.xyy.saas.inquiry.product.server.application.service.ProductApplicationService;
import com.xyy.saas.inquiry.product.server.builder.ProductTestDataBuilder;
import com.xyy.saas.inquiry.product.server.test.BaseIntegrationTest;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 商品服务集成测试
 * 
 * <AUTHOR> Assistant
 */
class ProductServiceIntegrationTest extends BaseIntegrationTest {
    
    @Resource
    private ProductApplicationService productApplicationService;
    
    @Test
    void testSaveNewProduct_Success() {
        // Given
        ProductInfoDto dto = getProductBuilder()
            .commonName("集成测试商品")
            .manufacturer("集成测试厂家")
            .unit("盒")
            .specification("10mg*20片")
            .productPref("INT_TEST_001")
            .barcode("1234567890123")
            .buildDto();
        
        // When
        Long productId = productApplicationService.saveOrUpdateProduct(dto, ProductBizTypeEnum.ADD_PRODUCT);
        
        // Then
        assertNotNull(productId);
        assertTrue(productId > 0);
        
        // 验证数据库中的记录
        assertProductExists("INT_TEST_001");
        assertEquals("FIRST_AUDITING", getDatabaseTestUtil().getProductStatus("INT_TEST_001", DEFAULT_TENANT_ID));
        
        // 验证相关表数据
        assertRecordExists("product_use_info", "product_id = ?", productId);
        assertRecordExists("product_qualification", "product_id = ?", productId);
    }
    
    @Test
    void testEditExistingProduct_Success() {
        // Given - 先创建一个商品
        ProductInfoDto createDto = getProductBuilder()
            .commonName("待编辑商品")
            .manufacturer("原厂家")
            .productPref("EDIT_TEST_001")
            .status(ProductStatusEnum.USING)
            .buildDto();
        
        Long productId = productApplicationService.saveOrUpdateProduct(createDto, ProductBizTypeEnum.ADD_PRODUCT);
        assertNotNull(productId);
        
        // 准备编辑数据
        ProductInfoDto editDto = getProductBuilder()
            .id(productId)
            .commonName("编辑后商品")
            .manufacturer("新厂家")
            .productPref("EDIT_TEST_001")
            .status(ProductStatusEnum.USING)
            .buildDto();
        
        // When
        Long updatedProductId = productApplicationService.saveOrUpdateProduct(editDto, ProductBizTypeEnum.EDIT_PRODUCT);
        
        // Then
        assertEquals(productId, updatedProductId);
        
        // 验证商品信息已更新
        var productData = getDatabaseTestUtil().queryForMap(
            "SELECT common_name, manufacturer FROM product_info WHERE id = ?", 
            productId
        );
        assertEquals("编辑后商品", productData.get("common_name"));
        assertEquals("新厂家", productData.get("manufacturer"));
    }
    
    @Test
    void testFirstProductFlow_Success() {
        // Given
        ProductInfoDto dto = getProductBuilder()
            .commonName("首营商品")
            .manufacturer("首营厂家")
            .productPref("FIRST_001")
            .buildDto();
        
        // When
        Long productId = productApplicationService.saveOrUpdateProduct(dto, ProductBizTypeEnum.FIRST_PRODUCT);
        
        // Then
        assertNotNull(productId);
        assertProductExists("FIRST_001");
        
        // 验证首营商品的特殊处理逻辑
        var productData = getDatabaseTestUtil().queryForMap(
            "SELECT status FROM product_info WHERE product_pref = ? AND tenant_id = ?", 
            "FIRST_001", DEFAULT_TENANT_ID
        );
        // 首营商品应该设置为特定状态
        assertNotNull(productData.get("status"));
    }
    
    @Test
    void testMidStdlibAddFlow_Success() {
        // Given
        ProductInfoDto dto = getProductBuilder()
            .commonName("中台标准库商品")
            .manufacturer("中台厂家")
            .productPref("MID_001")
            .midProductPref("MID_STD_001")
            .buildDto();
        
        // When
        Long productId = productApplicationService.saveOrUpdateProduct(dto, ProductBizTypeEnum.MID_STDLIB_ADD);
        
        // Then
        assertNotNull(productId);
        assertProductExists("MID_001");
        
        // 验证标准库数据同时被创建
        assertTrue(getDatabaseTestUtil().stdlibProductExists("MID_STD_001", DEFAULT_TENANT_ID));
        
        // 验证中台标准库商品的特殊字段
        var stdlibData = getDatabaseTestUtil().queryForMap(
            "SELECT mid_product_pref FROM product_stdlib WHERE mid_product_pref = ? AND tenant_id = ?",
            "MID_STD_001", DEFAULT_TENANT_ID
        );
        assertEquals("MID_STD_001", stdlibData.get("mid_product_pref"));
    }
    
    @Test
    void testChainStoreAddFlow_Success() {
        // Given
        ProductInfoDto dto = getProductBuilder()
            .commonName("连锁店商品")
            .manufacturer("连锁店厂家")
            .productPref("CHAIN_001")
            .buildDto();
        
        // When
        Long productId = productApplicationService.saveOrUpdateProduct(dto, ProductBizTypeEnum.CHAIN_STORE_ADD);
        
        // Then
        assertNotNull(productId);
        assertProductExists("CHAIN_001");
        
        // 验证连锁店商品的特殊处理
        var productData = getDatabaseTestUtil().queryForMap(
            "SELECT status FROM product_info WHERE product_pref = ? AND tenant_id = ?",
            "CHAIN_001", DEFAULT_TENANT_ID
        );
        assertNotNull(productData.get("status"));
    }
    
    @Test
    void testDuplicateProductPref_ThrowsException() {
        // Given - 先创建一个商品
        ProductInfoDto firstDto = getProductBuilder()
            .commonName("第一个商品")
            .productPref("DUPLICATE_001")
            .buildDto();
        
        productApplicationService.saveOrUpdateProduct(firstDto, ProductBizTypeEnum.ADD_PRODUCT);
        
        // 尝试创建相同编码的商品
        ProductInfoDto duplicateDto = getProductBuilder()
            .commonName("重复编码商品")
            .productPref("DUPLICATE_001")
            .buildDto();
        
        // When & Then
        assertThrows(
            RuntimeException.class,
            () -> productApplicationService.saveOrUpdateProduct(duplicateDto, ProductBizTypeEnum.ADD_PRODUCT)
        );
        
        // 验证只有第一个商品存在
        assertRecordCount("product_info", 1);
    }
    
    @Test
    void testInvalidProductData_ThrowsException() {
        // Given - 无效的商品数据
        ProductInfoDto invalidDto = getProductBuilder()
            .commonName("") // 空商品名
            .manufacturer("有效厂家")
            .productPref("INVALID_001")
            .buildDto();
        
        // When & Then
        assertThrows(
            RuntimeException.class,
            () -> productApplicationService.saveOrUpdateProduct(invalidDto, ProductBizTypeEnum.ADD_PRODUCT)
        );
        
        // 验证没有数据被保存
        assertProductNotExists("INVALID_001");
    }
    
    @Test
    void testConcurrentProductCreation_Success() throws InterruptedException {
        // Given
        final int threadCount = 5;
        final Thread[] threads = new Thread[threadCount];
        final Long[] results = new Long[threadCount];
        final Exception[] exceptions = new Exception[threadCount];
        
        // When - 并发创建不同的商品
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                try {
                    runWithTenant(DEFAULT_TENANT_ID, () -> {
                        ProductInfoDto dto = getProductBuilder()
                            .commonName("并发商品" + index)
                            .productPref("CONCURRENT_" + String.format("%03d", index))
                            .buildDto();
                        
                        results[index] = productApplicationService.saveOrUpdateProduct(dto, ProductBizTypeEnum.ADD_PRODUCT);
                    });
                } catch (Exception e) {
                    exceptions[index] = e;
                }
            });
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }
        
        // Then - 验证所有商品都创建成功
        for (int i = 0; i < threadCount; i++) {
            assertNull(exceptions[i], "线程 " + i + " 不应该抛出异常");
            assertNotNull(results[i], "线程 " + i + " 应该返回商品ID");
            
            String productPref = "CONCURRENT_" + String.format("%03d", i);
            assertProductExists(productPref);
        }
        
        // 验证总记录数
        assertRecordCount("product_info", threadCount);
    }
    
    @Test
    void testTransactionRollback_OnException() {
        // Given - 模拟在处理过程中发生异常的场景
        ProductInfoDto dto = getProductBuilder()
            .commonName("事务回滚测试商品")
            .manufacturer("") // 这会导致验证失败
            .productPref("ROLLBACK_001")
            .buildDto();
        
        // When & Then
        assertThrows(
            RuntimeException.class,
            () -> productApplicationService.saveOrUpdateProduct(dto, ProductBizTypeEnum.ADD_PRODUCT)
        );
        
        // 验证事务回滚，没有数据被保存
        assertProductNotExists("ROLLBACK_001");
        assertRecordCount("product_info", 0);
        assertRecordCount("product_use_info", 0);
        assertRecordCount("product_qualification", 0);
    }
    
    @Test
    void testMultiTenantIsolation_Success() {
        // Given
        Long tenant1Id = 1L;
        Long tenant2Id = 2L;
        
        ProductInfoDto dto1 = getProductBuilder()
            .commonName("租户1商品")
            .productPref("TENANT1_001")
            .tenantId(tenant1Id)
            .buildDto();
        
        ProductInfoDto dto2 = getProductBuilder()
            .commonName("租户2商品") 
            .productPref("TENANT2_001")
            .tenantId(tenant2Id)
            .buildDto();
        
        // When - 在不同租户上下文中创建商品
        Long product1Id = runWithTenant(tenant1Id, () -> 
            productApplicationService.saveOrUpdateProduct(dto1, ProductBizTypeEnum.ADD_PRODUCT)
        );
        
        Long product2Id = runWithTenant(tenant2Id, () ->
            productApplicationService.saveOrUpdateProduct(dto2, ProductBizTypeEnum.ADD_PRODUCT)
        );
        
        // Then
        assertNotNull(product1Id);
        assertNotNull(product2Id);
        assertNotEquals(product1Id, product2Id);
        
        // 验证租户隔离
        assertProductExists("TENANT1_001", tenant1Id);
        assertProductExists("TENANT2_001", tenant2Id);
        assertProductNotExists("TENANT1_001", tenant2Id);
        assertProductNotExists("TENANT2_001", tenant1Id);
    }
    
    @Override
    protected void prepareTestData() {
        // 可以在这里准备一些基础测试数据
        super.prepareTestData();
        
        // 例如：插入租户信息、基础字典数据等
        log.debug("准备集成测试基础数据");
    }
}