package com.xyy.saas.inquiry.product.server.domain.valueobject;

import com.xyy.saas.inquiry.product.server.test.BaseUnitTest;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 商品标志值对象单元测试
 * 
 * <AUTHOR> Assistant
 */
class ProductFlagTest extends BaseUnitTest {
    
    @Test
    void testDefaultFlag_Success() {
        // When
        ProductFlag flag = ProductFlag.defaultFlag();
        
        // Then
        assertNotNull(flag);
        assertFalse(flag.getPurchaseDisabled());
        assertFalse(flag.getStopSale());
        assertFalse(flag.getSpecialPrice());
        assertFalse(flag.getIntegral());
        assertFalse(flag.getUnbundled());
        assertFalse(flag.getMidSyncSkipped());
        assertFalse(flag.getMidDeactivated());
    }
    
    @Test
    void testBuilder_Success() {
        // When
        ProductFlag flag = ProductFlag.builder()
            .purchaseDisabled(true)
            .stopSale(true)
            .specialPrice(false)
            .integral(true)
            .unbundled(false)
            .midSyncSkipped(true)
            .midDeactivated(false)
            .build();
        
        // Then
        assertTrue(flag.getPurchaseDisabled());
        assertTrue(flag.getStopSale());
        assertFalse(flag.getSpecialPrice());
        assertTrue(flag.getIntegral());
        assertFalse(flag.getUnbundled());
        assertTrue(flag.getMidSyncSkipped());
        assertFalse(flag.getMidDeactivated());
    }
    
    @Test
    void testFromOriginal_WithValidOriginal_Success() {
        // Given
        com.xyy.saas.inquiry.product.api.product.dto.ProductFlag original = 
            new com.xyy.saas.inquiry.product.api.product.dto.ProductFlag();
        original.setStopSale(true);
        original.setSpecialPrice(false);
        original.setIntegral(true);
        original.setUnbundled(false);
        original.setMidSyncSkipped(true);
        original.setMidDeactivated(false);
        
        // When
        ProductFlag flag = ProductFlag.fromOriginal(original);
        
        // Then
        assertTrue(flag.getStopSale());
        assertFalse(flag.getSpecialPrice());
        assertTrue(flag.getIntegral());
        assertFalse(flag.getUnbundled());
        assertTrue(flag.getMidSyncSkipped());
        assertFalse(flag.getMidDeactivated());
    }
    
    @Test
    void testFromOriginal_WithNullOriginal_ReturnsDefault() {
        // When
        ProductFlag flag = ProductFlag.fromOriginal(null);
        
        // Then
        assertNotNull(flag);
        assertFalse(flag.getStopSale());
        assertFalse(flag.getSpecialPrice());
        assertFalse(flag.getIntegral());
        assertFalse(flag.getUnbundled());
        assertFalse(flag.getMidSyncSkipped());
        assertFalse(flag.getMidDeactivated());
    }
    
    @Test
    void testToOriginal_Success() {
        // Given
        ProductFlag flag = ProductFlag.builder()
            .stopSale(true)
            .specialPrice(false)
            .integral(true)
            .unbundled(false)
            .midSyncSkipped(true)
            .midDeactivated(false)
            .build();
        
        // When
        com.xyy.saas.inquiry.product.api.product.dto.ProductFlag original = flag.toOriginal();
        
        // Then
        assertNotNull(original);
        assertTrue(original.getStopSale());
        assertFalse(original.getSpecialPrice());
        assertTrue(original.getIntegral());
        assertFalse(original.getUnbundled());
        assertTrue(original.getMidSyncSkipped());
        assertFalse(original.getMidDeactivated());
    }
    
    @Test
    void testWithPurchaseDisabled_Success() {
        // Given
        ProductFlag original = ProductFlag.defaultFlag();
        
        // When
        ProductFlag updated = original.withPurchaseDisabled(true);
        
        // Then
        assertNotSame(original, updated); // 确保是不可变对象
        assertFalse(original.getPurchaseDisabled()); // 原对象未改变
        assertTrue(updated.getPurchaseDisabled()); // 新对象已更新
        
        // 其他属性保持不变
        assertEquals(original.getStopSale(), updated.getStopSale());
        assertEquals(original.getSpecialPrice(), updated.getSpecialPrice());
        assertEquals(original.getIntegral(), updated.getIntegral());
    }
    
    @Test
    void testWithStopSale_Success() {
        // Given
        ProductFlag original = ProductFlag.defaultFlag();
        
        // When
        ProductFlag updated = original.withStopSale(true);
        
        // Then
        assertNotSame(original, updated);
        assertFalse(original.getStopSale());
        assertTrue(updated.getStopSale());
    }
    
    @Test
    void testWithUnbundled_Success() {
        // Given
        ProductFlag original = ProductFlag.defaultFlag();
        
        // When
        ProductFlag updated = original.withUnbundled(true);
        
        // Then
        assertNotSame(original, updated);
        assertFalse(original.getUnbundled());
        assertTrue(updated.getUnbundled());
    }
    
    @Test
    void testMerge_WithValidOther_Success() {
        // Given
        ProductFlag original = ProductFlag.builder()
            .purchaseDisabled(false)
            .stopSale(false)
            .specialPrice(false)
            .integral(false)
            .build();
            
        ProductFlag other = ProductFlag.builder()
            .purchaseDisabled(true)
            .stopSale(null) // null值应该保持原值
            .specialPrice(true)
            .integral(null) // null值应该保持原值
            .build();
        
        // When
        ProductFlag merged = original.merge(other);
        
        // Then
        assertTrue(merged.getPurchaseDisabled()); // 更新为other的值
        assertFalse(merged.getStopSale()); // 保持原值（other为null）
        assertTrue(merged.getSpecialPrice()); // 更新为other的值
        assertFalse(merged.getIntegral()); // 保持原值（other为null）
    }
    
    @Test
    void testMerge_WithNullOther_ReturnsSelf() {
        // Given
        ProductFlag original = ProductFlag.builder()
            .purchaseDisabled(true)
            .stopSale(true)
            .build();
        
        // When
        ProductFlag merged = original.merge(null);
        
        // Then
        assertSame(original, merged);
    }
    
    @Test
    void testEquals_SameValues_ReturnsTrue() {
        // Given
        ProductFlag flag1 = ProductFlag.builder()
            .purchaseDisabled(true)
            .stopSale(false)
            .specialPrice(true)
            .build();
            
        ProductFlag flag2 = ProductFlag.builder()
            .purchaseDisabled(true)
            .stopSale(false)
            .specialPrice(true)
            .build();
        
        // When & Then
        assertEquals(flag1, flag2);
        assertEquals(flag1.hashCode(), flag2.hashCode());
    }
    
    @Test
    void testEquals_DifferentValues_ReturnsFalse() {
        // Given
        ProductFlag flag1 = ProductFlag.builder()
            .purchaseDisabled(true)
            .stopSale(false)
            .build();
            
        ProductFlag flag2 = ProductFlag.builder()
            .purchaseDisabled(false)
            .stopSale(true)
            .build();
        
        // When & Then
        assertNotEquals(flag1, flag2);
    }
    
    @Test
    void testEquals_WithNull_ReturnsFalse() {
        // Given
        ProductFlag flag = ProductFlag.defaultFlag();
        
        // When & Then
        assertNotEquals(flag, null);
    }
    
    @Test
    void testEquals_WithDifferentClass_ReturnsFalse() {
        // Given
        ProductFlag flag = ProductFlag.defaultFlag();
        String other = "not a ProductFlag";
        
        // When & Then
        assertNotEquals(flag, other);
    }
}