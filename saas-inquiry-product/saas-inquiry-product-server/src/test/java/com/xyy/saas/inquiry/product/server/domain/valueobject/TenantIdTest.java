package com.xyy.saas.inquiry.product.server.domain.valueobject;

import com.xyy.saas.inquiry.product.server.test.BaseUnitTest;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 租户ID值对象单元测试
 * 
 * <AUTHOR> Assistant
 */
class TenantIdTest extends BaseUnitTest {
    
    @Test
    void testConstructor_WithValidValues_Success() {
        // Given
        Long id = 1L;
        String code = "TEST_TENANT";
        
        // When
        TenantId tenantId = new TenantId(id, code);
        
        // Then
        assertEquals(id, tenantId.getId());
        assertEquals(code, tenantId.getCode());
    }
    
    @Test
    void testConstructor_WithNullId_ThrowsException() {
        // Given
        String code = "TEST_TENANT";
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> new TenantId(null, code)
        );
        
        assertEquals("租户ID不能为空", exception.getMessage());
    }
    
    @Test
    void testConstructor_WithBlankCode_ThrowsException() {
        // Given
        Long id = 1L;
        String blankCode = "   ";
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> new TenantId(id, blankCode)
        );
        
        assertEquals("租户编码不能为空", exception.getMessage());
    }
    
    @Test
    void testConstructor_WithNullCode_ThrowsException() {
        // Given
        Long id = 1L;
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> new TenantId(id, null)
        );
        
        assertEquals("租户编码不能为空", exception.getMessage());
    }
    
    @Test
    void testConstructor_WithEmptyCode_ThrowsException() {
        // Given
        Long id = 1L;
        String emptyCode = "";
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> new TenantId(id, emptyCode)
        );
        
        assertEquals("租户编码不能为空", exception.getMessage());
    }
    
    @Test
    void testEquals_SameValues_ReturnsTrue() {
        // Given
        TenantId tenantId1 = new TenantId(1L, "TEST_TENANT");
        TenantId tenantId2 = new TenantId(1L, "TEST_TENANT");
        
        // When & Then
        assertEquals(tenantId1, tenantId2);
        assertEquals(tenantId1.hashCode(), tenantId2.hashCode());
    }
    
    @Test
    void testEquals_DifferentId_ReturnsFalse() {
        // Given
        TenantId tenantId1 = new TenantId(1L, "TEST_TENANT");
        TenantId tenantId2 = new TenantId(2L, "TEST_TENANT");
        
        // When & Then
        assertNotEquals(tenantId1, tenantId2);
    }
    
    @Test
    void testEquals_DifferentCode_ReturnsFalse() {
        // Given
        TenantId tenantId1 = new TenantId(1L, "TEST_TENANT_1");
        TenantId tenantId2 = new TenantId(1L, "TEST_TENANT_2");
        
        // When & Then
        assertNotEquals(tenantId1, tenantId2);
    }
    
    @Test
    void testEquals_WithNull_ReturnsFalse() {
        // Given
        TenantId tenantId = new TenantId(1L, "TEST_TENANT");
        
        // When & Then
        assertNotEquals(tenantId, null);
    }
    
    @Test
    void testEquals_WithDifferentClass_ReturnsFalse() {
        // Given
        TenantId tenantId = new TenantId(1L, "TEST_TENANT");
        String other = "not a TenantId";
        
        // When & Then
        assertNotEquals(tenantId, other);
    }
    
    @Test
    void testEquals_SameInstance_ReturnsTrue() {
        // Given
        TenantId tenantId = new TenantId(1L, "TEST_TENANT");
        
        // When & Then
        assertEquals(tenantId, tenantId);
    }
    
    @Test
    void testHashCode_SameValues_SameHashCode() {
        // Given
        TenantId tenantId1 = new TenantId(1L, "TEST_TENANT");
        TenantId tenantId2 = new TenantId(1L, "TEST_TENANT");
        
        // When & Then
        assertEquals(tenantId1.hashCode(), tenantId2.hashCode());
    }
    
    @Test
    void testHashCode_DifferentValues_DifferentHashCode() {
        // Given
        TenantId tenantId1 = new TenantId(1L, "TEST_TENANT_1");
        TenantId tenantId2 = new TenantId(2L, "TEST_TENANT_2");
        
        // When & Then
        assertNotEquals(tenantId1.hashCode(), tenantId2.hashCode());
    }
    
    @Test
    void testToString_Success() {
        // Given
        TenantId tenantId = new TenantId(1L, "TEST_TENANT");
        
        // When
        String result = tenantId.toString();
        
        // Then
        assertNotNull(result);
        assertTrue(result.contains("1"));
        assertTrue(result.contains("TEST_TENANT"));
    }
    
    @Test
    void testIsValid_ValidValues_ReturnsTrue() {
        // Given
        Long validId = 1L;
        String validCode = "VALID_TENANT";
        
        // When
        boolean result = TenantId.isValid(validId, validCode);
        
        // Then
        assertTrue(result);
    }
    
    @Test
    void testIsValid_NullId_ReturnsFalse() {
        // Given
        String validCode = "VALID_TENANT";
        
        // When
        boolean result = TenantId.isValid(null, validCode);
        
        // Then
        assertFalse(result);
    }
    
    @Test
    void testIsValid_BlankCode_ReturnsFalse() {
        // Given
        Long validId = 1L;
        
        // When & Then
        assertFalse(TenantId.isValid(validId, null));
        assertFalse(TenantId.isValid(validId, ""));
        assertFalse(TenantId.isValid(validId, "   "));
    }
    
    @Test
    void testOf_ValidValues_Success() {
        // Given
        Long id = 1L;
        String code = "TEST_TENANT";
        
        // When
        TenantId tenantId = TenantId.of(id, code);
        
        // Then
        assertNotNull(tenantId);
        assertEquals(id, tenantId.getId());
        assertEquals(code, tenantId.getCode());
    }
    
    @Test
    void testOf_InvalidValues_ThrowsException() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> TenantId.of(null, "TEST"));
        assertThrows(IllegalArgumentException.class, () -> TenantId.of(1L, null));
        assertThrows(IllegalArgumentException.class, () -> TenantId.of(1L, ""));
    }
}