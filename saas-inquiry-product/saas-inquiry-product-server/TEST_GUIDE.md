# 商品服务测试指南

## 测试架构概览

本项目采用了完整的测试体系，包括单元测试、集成测试、性能测试等多个层次，确保代码质量和系统稳定性。

### 测试分层架构

```
测试金字塔
    ┌─────────────────┐
    │   性能测试      │  ← 少量，验证系统性能指标
    ├─────────────────┤
    │   集成测试      │  ← 中等数量，验证组件协作
    ├─────────────────┤
    │   单元测试      │  ← 大量，验证单个组件逻辑
    └─────────────────┘
```

## 测试组织结构

### 测试基础架构
- `BaseUnitTest` - 单元测试基类，提供通用工具和Mock支持
- `BaseIntegrationTest` - 集成测试基类，提供数据库事务和Spring上下文
- `ProductTestDataBuilder` - 商品测试数据构建器
- `TenantTestDataBuilder` - 租户测试数据构建器
- `MockTenantContextUtil` - 租户上下文模拟工具
- `DatabaseTestUtil` - 数据库测试工具

### 测试工具类
- **数据构建器模式**: 使用Builder模式构建测试数据，提高可维护性
- **Mock工具**: 统一的Mock工具类，简化外部依赖模拟
- **数据库工具**: 提供数据库操作、断言和清理功能

## 单元测试

### 领域模型测试
- `ProductAggregateTest` - 商品聚合根测试
- `ProductFlagTest` - 商品标志值对象测试
- `TenantIdTest` - 租户ID值对象测试
- `ProductStatusChangedEventTest` - 领域事件测试

### 应用服务测试
- `ProductApplicationServiceTest` - 应用服务测试
- `ProductSaveOrchestratorTest` - 业务编排器测试
- `TenantInfoAssemblyStepTest` - 租户信息装配步骤测试
- `ProductValidationStepTest` - 商品验证步骤测试

### 策略模式测试
- `ProductBizTypeHandlerFactoryTest` - 处理器工厂测试
- `AddProductHandlerTest` - 新增商品处理器测试

## 集成测试

### 业务场景测试
- `ProductServiceIntegrationTest` - 端到端业务流程测试
  - 新增商品流程
  - 编辑商品流程
  - 首营商品流程
  - 中台标准库商品流程
  - 连锁店商品流程
  - 并发处理测试
  - 事务回滚测试
  - 多租户隔离测试

## 性能测试

### 性能指标测试
- `ProductServicePerformanceTest` - 性能和压力测试
  - 单个商品创建性能
  - 批量商品创建性能
  - 并发商品创建性能
  - 内存使用测试
  - 数据库连接池性能

### 性能测试启用
```bash
# 启用性能测试
mvn test -Dperformance.test.enabled=true -Dtest="**/*PerformanceTest"
```

## 运行测试

### 单元测试
```bash
# 运行所有单元测试
mvn test -Dtest="**/*Test"

# 运行特定测试类
mvn test -Dtest="ProductAggregateTest"

# 运行特定测试方法
mvn test -Dtest="ProductAggregateTest#testCreate_Success"
```

### 集成测试
```bash
# 运行所有集成测试
mvn test -Dtest="**/*IntegrationTest"

# 使用测试配置文件
mvn test -Dspring.profiles.active=test -Dtest="ProductServiceIntegrationTest"
```

### 性能测试
```bash
# 运行性能测试（需要显式启用）
mvn test -Dperformance.test.enabled=true -Dtest="**/*PerformanceTest"
```

### 全量测试
```bash
# 运行所有测试
mvn test

# 运行测试并生成覆盖率报告
mvn clean test jacoco:report
```

## 测试配置

### 测试环境配置
- `application-test.yml` - 测试环境专用配置
- `test-runner.properties` - 测试运行器配置
- 使用H2内存数据库进行单元测试和集成测试
- 支持MySQL数据库进行性能测试

### 测试数据管理
- 每个测试方法都在独立事务中运行
- 测试完成后自动回滚，确保数据隔离
- 提供测试数据构建器，标准化测试数据创建

## CI/CD集成

### GitHub Actions配置
- `test-ci.yml` - 完整的CI/CD测试流水线
- 支持单元测试、集成测试、性能测试
- 自动生成测试报告和覆盖率报告
- 支持多环境测试执行

### 测试分阶段执行
1. **单元测试阶段**: 快速验证单个组件
2. **集成测试阶段**: 验证组件协作
3. **性能测试阶段**: 验证系统性能（仅主分支）
4. **覆盖率统计**: 生成测试覆盖率报告

## 测试最佳实践

### 测试命名规范
- 单元测试: `testMethodName_Scenario_ExpectedResult`
- 集成测试: `testBusinessScenario_Success`
- 性能测试: `testPerformanceScenario_MeetsRequirement`

### 测试数据管理
- 使用Builder模式构建测试数据
- 预设常用测试数据场景
- 确保测试数据的隔离性和可重复性

### Mock使用原则
- 单元测试中Mock外部依赖
- 集成测试中使用真实依赖
- 性能测试中使用生产级配置

### 断言策略
- 优先使用业务语义清晰的断言
- 提供自定义断言方法简化测试代码
- 确保异常场景的完整覆盖

## 测试覆盖率目标

### 覆盖率要求
- 行覆盖率: ≥ 80%
- 分支覆盖率: ≥ 70%
- 方法覆盖率: ≥ 85%

### 覆盖率报告
```bash
# 生成覆盖率报告
mvn jacoco:report

# 查看报告
open target/site/jacoco/index.html
```

## 故障排查

### 常见问题
1. **测试数据冲突**: 检查测试数据隔离配置
2. **Mock不生效**: 确认Mock配置和依赖注入
3. **性能测试超时**: 调整超时配置和资源分配
4. **数据库连接问题**: 检查测试环境数据库配置

### 调试技巧
- 启用SQL日志: `logging.level.org.hibernate.SQL=DEBUG`
- 查看测试执行时间: `mvn test -Dorg.slf4j.simpleLogger.showDateTime=true`
- 单独运行失败测试: `mvn test -Dtest="TestClass#testMethod"`

## 持续改进

### 测试质量监控
- 定期审查测试覆盖率报告
- 分析测试执行时间和稳定性
- 持续优化测试数据和工具类

### 测试维护
- 及时更新测试用例以适应业务变化
- 重构测试代码提高可维护性
- 定期清理过时的测试代码和配置