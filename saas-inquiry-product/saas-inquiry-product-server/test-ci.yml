# CI/CD 测试集成配置
name: Product Service Tests

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'saas-inquiry-system/saas-inquiry-product/**'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'saas-inquiry-system/saas-inquiry-product/**'

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    name: Unit Tests
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up JDK 21
      uses: actions/setup-java@v3
      with:
        java-version: '21'
        distribution: 'temurin'
        
    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2
        
    - name: Run Unit Tests
      run: |
        cd saas-inquiry-system/saas-inquiry-product/saas-inquiry-product-server
        mvn clean test -Dtest="**/*Test" -DfailIfNoTests=false
        
    - name: Generate Test Report
      run: |
        cd saas-inquiry-system/saas-inquiry-product/saas-inquiry-product-server
        mvn surefire-report:report
        
    - name: Upload Test Results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: unit-test-results
        path: |
          saas-inquiry-system/saas-inquiry-product/saas-inquiry-product-server/target/surefire-reports/
          saas-inquiry-system/saas-inquiry-product/saas-inquiry-product-server/target/site/
        
  integration-tests:
    runs-on: ubuntu-latest
    name: Integration Tests
    needs: unit-tests
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: test_inquiry_product
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
        
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up JDK 21
      uses: actions/setup-java@v3
      with:
        java-version: '21'
        distribution: 'temurin'
        
    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2
        
    - name: Wait for MySQL
      run: |
        until mysql -h127.0.0.1 -uroot -proot -e "SELECT 1"; do
          echo "Waiting for MySQL..."
          sleep 1
        done
        
    - name: Run Integration Tests
      run: |
        cd saas-inquiry-system/saas-inquiry-product/saas-inquiry-product-server
        mvn clean test -Dtest="**/*IntegrationTest" -DfailIfNoTests=false \
          -Dspring.profiles.active=test \
          -Dspring.datasource.url=************************************************ \
          -Dspring.datasource.username=root \
          -Dspring.datasource.password=root
          
    - name: Upload Integration Test Results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: integration-test-results
        path: |
          saas-inquiry-system/saas-inquiry-product/saas-inquiry-product-server/target/surefire-reports/
          
  performance-tests:
    runs-on: ubuntu-latest
    name: Performance Tests
    needs: [unit-tests, integration-tests]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: test_inquiry_product
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3
          --memory=2g
          --cpus=2
          
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up JDK 21
      uses: actions/setup-java@v3
      with:
        java-version: '21'
        distribution: 'temurin'
        
    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2
        
    - name: Configure MySQL for Performance
      run: |
        mysql -h127.0.0.1 -uroot -proot -e "
          SET GLOBAL innodb_buffer_pool_size = 256M;
          SET GLOBAL innodb_log_file_size = 64M;
          SET GLOBAL max_connections = 200;
        "
        
    - name: Run Performance Tests
      run: |
        cd saas-inquiry-system/saas-inquiry-product/saas-inquiry-product-server
        mvn clean test -Dtest="**/*PerformanceTest" -DfailIfNoTests=false \
          -Dperformance.test.enabled=true \
          -Dspring.profiles.active=test \
          -Dspring.datasource.url=************************************************ \
          -Dspring.datasource.username=root \
          -Dspring.datasource.password=root \
          -Xmx2g -XX:+UseG1GC
          
    - name: Upload Performance Test Results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: performance-test-results
        path: |
          saas-inquiry-system/saas-inquiry-product/saas-inquiry-product-server/target/surefire-reports/
          
  code-coverage:
    runs-on: ubuntu-latest
    name: Code Coverage
    needs: [unit-tests, integration-tests]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up JDK 21
      uses: actions/setup-java@v3
      with:
        java-version: '21'
        distribution: 'temurin'
        
    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2
        
    - name: Run Tests with Coverage
      run: |
        cd saas-inquiry-system/saas-inquiry-product/saas-inquiry-product-server
        mvn clean test jacoco:report \
          -Dtest="**/*Test,**/*IntegrationTest" \
          -DfailIfNoTests=false
          
    - name: Upload Coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: saas-inquiry-system/saas-inquiry-product/saas-inquiry-product-server/target/site/jacoco/jacoco.xml
        flags: unittests
        name: codecov-umbrella
        
    - name: Upload Coverage Results
      uses: actions/upload-artifact@v3
      with:
        name: coverage-results
        path: saas-inquiry-system/saas-inquiry-product/saas-inquiry-product-server/target/site/jacoco/
        
  test-summary:
    runs-on: ubuntu-latest
    name: Test Summary
    needs: [unit-tests, integration-tests, performance-tests, code-coverage]
    if: always()
    
    steps:
    - name: Download Test Results
      uses: actions/download-artifact@v3
      with:
        path: test-results
        
    - name: Create Test Summary
      run: |
        echo "# 测试执行总结" > test-summary.md
        echo "" >> test-summary.md
        echo "## 测试结果" >> test-summary.md
        echo "- 单元测试: ${{ needs.unit-tests.result }}" >> test-summary.md
        echo "- 集成测试: ${{ needs.integration-tests.result }}" >> test-summary.md
        echo "- 性能测试: ${{ needs.performance-tests.result }}" >> test-summary.md
        echo "- 代码覆盖率: ${{ needs.code-coverage.result }}" >> test-summary.md
        echo "" >> test-summary.md
        echo "## 测试时间" >> test-summary.md
        echo "测试执行时间: $(date)" >> test-summary.md
        
    - name: Upload Test Summary
      uses: actions/upload-artifact@v3
      with:
        name: test-summary
        path: test-summary.md