package cn.iocoder.yudao.module.system.service.tenant;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomCommonStatus;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_PACKAGE_DISABLE;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_PACKAGE_NOT_EXISTS;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_PACKAGE_USED;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.BaseIntegrationTest;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.TenantPackagePageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.TenantPackageRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.TenantPackageSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageDO;
import cn.iocoder.yudao.module.system.dal.mysql.tenant.TenantPackageMapper;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.DateTermTypeEnum;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItem;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * {@link TenantPackageServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(TenantPackageServiceImpl.class)
public class TenantPackageServiceImplTest extends BaseIntegrationTest {

    private static final Logger log = LoggerFactory.getLogger(TenantPackageServiceImplTest.class);
    @Resource
    private TenantPackageServiceImpl tenantPackageService;

    @Resource
    private TenantPackageMapper tenantPackageMapper;

    @MockBean
    private TenantService tenantService;

    @BeforeEach
    public void before() {
        RequestAttributes requestAttributes = new ServletRequestAttributes(new MockHttpServletRequest());
        requestAttributes.setAttribute("login_user_id", 0L, RequestAttributes.SCOPE_REQUEST);
        RequestContextHolder.setRequestAttributes(requestAttributes);
//        TenantContextHolder.setTenantId();
    }

    /**
     * 创建门店套餐包
     */
    @Test
    public void testCreateTenantPackage_success() {
        // 创建问诊套餐包
        List<InquiryPackageItem> items = new ArrayList<>() {{
            add(new InquiryPackageItem().setCount(-1L).setUnlimited(true).setInquiryWayType(InquiryWayTypeEnum.TEXT.getCode()));
            add(new InquiryPackageItem().setCount(100L).setUnlimited(false).setInquiryWayType(InquiryWayTypeEnum.VIDEO.getCode()));
        }};
        Set<Long> menus = Stream.of(1, 1224, 1138, 1139, 1140, 1141, 1142, 1143, 1225, 1226, 1227, 1228, 1229, 100, 1001, 1002, 1003, 1004, 1005, 1006, 1007, 101, 1008, 1009, 1010, 1011, 1012, 1063, 1064, 1065, 102, 1013, 1014, 1015, 1016,
            103,
            1017, 1018, 1019, 1020, 104, 1021, 1022, 1023, 1024, 1025, 105, 1026, 1027, 1028, 1029, 1030, 2739, 1093, 1094, 1095, 1096, 1097, 1098, 1100, 1101, 1102, 1103, 1104, 1105, 1106, 1107, 1108, 1109, 2130, 2131, 2132, 2133, 2134,
            2135,
            2136, 2137, 2138, 2139, 2140, 2143, 2141, 2142, 2144, 2145, 2146, 2147, 2148, 2149, 2150, 2151, 2152, 107, 1036, 1037, 1038, 1039, 108, 500, 1040, 1042, 501, 1043, 1045, 1261, 1263, 1264, 1265, 1266, 1267, 109, 1046, 1048, 2447,
            2448, 2449, 2450, 2451, 2452, 2453, 2083).map(Long::valueOf).collect(Collectors.toSet());
        // mock 数据
        TenantPackageSaveReqVO saveReqVO = randomPojo(TenantPackageSaveReqVO.class, o -> { // 等会查询到
            o.setName("基础问诊套餐包" + o.getName());
            o.setBizType(BizTypeEnum.HYWZ.getCode());
            o.setTerm(2);
            o.setTermType(DateTermTypeEnum.YEAR.getCode());
            o.setStatus(CommonStatusEnum.ENABLE.getStatus());
            o.setSorted(1);
            o.setHospitalPrefs(Stream.of("1", "2").collect(Collectors.toList()));
            o.setMenuIds(menus);
            o.setPrice(BigDecimal.TEN);
            o.setRegionArr(Stream.of("420000", "310000").collect(Collectors.toList()));
            o.setInquiryWayTypes(Stream.of(1, 2).collect(Collectors.toList()));
            o.setInquiryPackageItems(items);
        });
        // 调用
        Long tenantPackageId = tenantPackageService.createTenantPackage(saveReqVO);
        // 断言
        assertNotNull(tenantPackageId);
        // 校验记录的属性是否正确
        TenantPackageDO tenantPackage = tenantPackageMapper.selectById(tenantPackageId);
        log.info("{}", JSON.toJSONString(tenantPackage));
    }

    /**
     * 更新门店套餐包
     */
    @Test
    public void testUpdateTenantPackage_success() {
        // mock 数据
        TenantPackageDO dbTenantPackage = randomPojo(TenantPackageDO.class,
            o -> o.setStatus(randomCommonStatus()));
        tenantPackageMapper.insert(dbTenantPackage);// @Sql: 先插入出一条存在的数据
        // 准备参数
        TenantPackageSaveReqVO reqVO = randomPojo(TenantPackageSaveReqVO.class, o -> {
            o.setId(dbTenantPackage.getId()); // 设置更新的 ID
            o.setStatus(randomCommonStatus());
        });
        // mock 方法
        Long tenantId01 = randomLongId();
        Long tenantId02 = randomLongId();
//        when(tenantService.getTenantListByPackageId(eq(reqVO.getId()))).thenReturn(
//                asList(randomPojo(TenantDO.class, o -> o.setId(tenantId01)),
//                        randomPojo(TenantDO.class, o -> o.setId(tenantId02))));

        // 调用
        tenantPackageService.updateTenantPackage(reqVO);
        // 校验是否更新正确
        TenantPackageDO tenantPackage = tenantPackageMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, tenantPackage);
        // 校验调用门店的菜单
        verify(tenantService).updateTenantRoleMenu(eq(tenantId01), eq(reqVO.getMenuIds()));
        verify(tenantService).updateTenantRoleMenu(eq(tenantId02), eq(reqVO.getMenuIds()));
    }

    @Test
    public void testUpdateTenantPackage_notExists() {
        // 准备参数
        TenantPackageSaveReqVO reqVO = randomPojo(TenantPackageSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> tenantPackageService.updateTenantPackage(reqVO), TENANT_PACKAGE_NOT_EXISTS);
    }

    @Test
    public void testDeleteTenantPackage_success() {
        // mock 数据
        TenantPackageDO dbTenantPackage = randomPojo(TenantPackageDO.class);
        tenantPackageMapper.insert(dbTenantPackage);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbTenantPackage.getId();
        // mock 门店未使用该套餐
//        when(tenantService.getTenantCountByPackageId(eq(id))).thenReturn(0L);

        // 调用
        tenantPackageService.deleteTenantPackage(id);
        // 校验数据不存在了
        assertNull(tenantPackageMapper.selectById(id));
    }

    @Test
    public void testDeleteTenantPackage_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> tenantPackageService.deleteTenantPackage(id), TENANT_PACKAGE_NOT_EXISTS);
    }

    @Test
    public void testDeleteTenantPackage_used() {
        // mock 数据
        TenantPackageDO dbTenantPackage = randomPojo(TenantPackageDO.class);
        tenantPackageMapper.insert(dbTenantPackage);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbTenantPackage.getId();
        // mock 门店在使用该套餐
//        when(tenantService.getTenantCountByPackageId(eq(id))).thenReturn(1L);

        // 调用, 并断言异常
        assertServiceException(() -> tenantPackageService.deleteTenantPackage(id), TENANT_PACKAGE_USED);
    }

    @Test
    public void testGetTenantPackagePage() {
        // mock 数据
        TenantPackageDO dbTenantPackage = randomPojo(TenantPackageDO.class, o -> { // 等会查询到
            o.setName("芋道源码");
            o.setStatus(CommonStatusEnum.ENABLE.getStatus());
            o.setRemark("源码解析");
            o.setCreateTime(buildTime(2022, 10, 10));
        });
        tenantPackageMapper.insert(dbTenantPackage);
        // 测试 name 不匹配
        tenantPackageMapper.insert(cloneIgnoreId(dbTenantPackage, o -> o.setName("源码")));
        // 测试 status 不匹配
        tenantPackageMapper.insert(cloneIgnoreId(dbTenantPackage, o -> o.setStatus(CommonStatusEnum.DISABLE.getStatus())));
        // 测试 remark 不匹配
        tenantPackageMapper.insert(cloneIgnoreId(dbTenantPackage, o -> o.setRemark("解析")));
        // 测试 createTime 不匹配
        tenantPackageMapper.insert(cloneIgnoreId(dbTenantPackage, o -> o.setCreateTime(buildTime(2022, 11, 11))));
        // 准备参数
        TenantPackagePageReqVO reqVO = new TenantPackagePageReqVO();
        reqVO.setName("芋道");
        reqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
//       reqVO.setRemark("源码");
        reqVO.setCreateTime(buildBetweenTime(2022, 10, 9, 2022, 10, 11));

        // 调用
        PageResult<TenantPackageRespVO> pageResult = tenantPackageService.getTenantPackagePage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbTenantPackage, pageResult.getList().getFirst());
    }

    @Test
    public void testValidTenantPackage_success() {
        // mock 数据
        TenantPackageDO dbTenantPackage = randomPojo(TenantPackageDO.class,
            o -> o.setStatus(CommonStatusEnum.ENABLE.getStatus()));
        tenantPackageMapper.insert(dbTenantPackage);// @Sql: 先插入出一条存在的数据

        // 调用
        TenantPackageDO result = tenantPackageService.validTenantPackage(dbTenantPackage.getId());
        // 断言
        assertPojoEquals(dbTenantPackage, result);
    }

    @Test
    public void testValidTenantPackage_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> tenantPackageService.validTenantPackage(id), TENANT_PACKAGE_NOT_EXISTS);
    }

    @Test
    public void testValidTenantPackage_disable() {
        // mock 数据
        TenantPackageDO dbTenantPackage = randomPojo(TenantPackageDO.class,
            o -> o.setStatus(CommonStatusEnum.DISABLE.getStatus()));
        tenantPackageMapper.insert(dbTenantPackage);// @Sql: 先插入出一条存在的数据

        // 调用, 并断言异常
        assertServiceException(() -> tenantPackageService.validTenantPackage(dbTenantPackage.getId()),
            TENANT_PACKAGE_DISABLE, dbTenantPackage.getName());
    }

    @Test
    public void testGetTenantPackage() {
        // mock 数据
        TenantPackageDO dbTenantPackage = randomPojo(TenantPackageDO.class);
        tenantPackageMapper.insert(dbTenantPackage);// @Sql: 先插入出一条存在的数据

        // 调用
        TenantPackageRespVO result = tenantPackageService.getTenantPackage(dbTenantPackage.getId());
        // 断言
        assertPojoEquals(result, dbTenantPackage);
    }

    @Test
    public void testGetTenantPackageListByStatus() {
        // mock 数据
        TenantPackageDO dbTenantPackage = randomPojo(TenantPackageDO.class,
            o -> o.setStatus(CommonStatusEnum.ENABLE.getStatus()));
        tenantPackageMapper.insert(dbTenantPackage);
        // 测试 status 不匹配
        tenantPackageMapper.insert(cloneIgnoreId(dbTenantPackage,
            o -> o.setStatus(CommonStatusEnum.DISABLE.getStatus())));

        // 调用
        List<TenantPackageDO> list = tenantPackageService.getTenantPackageListByStatus(
            CommonStatusEnum.ENABLE.getStatus());
        assertEquals(1, list.size());
        assertPojoEquals(dbTenantPackage, list.get(0));
    }

}
