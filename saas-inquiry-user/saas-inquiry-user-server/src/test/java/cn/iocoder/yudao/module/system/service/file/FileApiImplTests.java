package cn.iocoder.yudao.module.system.service.file;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.system.BaseIntegrationTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * 单元测试扫描当前包路径,相关api测试写到这个层级下或者建立相关包路径 com.xyy.saas.inquiry.drugstore.server
 */
class FileApiImplTests extends BaseIntegrationTest {

    @Autowired
    FileApi fileApi;

    @BeforeEach
    public void before() {
        RequestAttributes requestAttributes = new ServletRequestAttributes(new MockHttpServletRequest());
        requestAttributes.setAttribute("login_user_id", 0L, RequestAttributes.SCOPE_REQUEST);
        RequestContextHolder.setRequestAttributes(requestAttributes);
        TenantContextHolder.setTenantId(1831623538495963137L);
    }

    /**
     * 测试文件上传 上传私有桶路径 获取预览私有资源url
     */
    @Test
    public void testFileApi() {
//         final String url = fileApi.createFile("单测文件名", UUID.randomUUID().toString(true)+".jpg", ResourceUtil.readBytes("file/erweima.jpg"),false);
//         System.out.println(url);
// //        final String url = "http://files.test.ybm100.com/INVT/Sensitive/Lzinq/20240923/840a91dc87a94698beb29af811f9bbd0.jpg";
//         String filePresignedUrl = fileApi.getPrivateFilePreSignedUrl(url);
//         System.out.println(filePresignedUrl);

    }

}
