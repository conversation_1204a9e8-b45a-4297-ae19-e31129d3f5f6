package cn.iocoder.yudao.module.system.integration;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.xyy.saas.inquiry.user.server.InquiryUserServerApplication;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * 门店集成测试核心类
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/12 11:27
 */
@SpringBootTest(classes = InquiryUserServerApplication.class)
@ActiveProfiles("dev")
public class TenantIntegrationTest {

    @BeforeEach
    public void before() {
        RequestAttributes requestAttributes = new ServletRequestAttributes(new MockHttpServletRequest());
        requestAttributes.setAttribute("login_user_id", 1856634153722658818L, RequestAttributes.SCOPE_REQUEST);
        RequestContextHolder.setRequestAttributes(requestAttributes);
        TenantContextHolder.setTenantId(1856621116684963842L); // cxy
    }

    /**
     * 开通门店
     */

    /**
     * 用户登录
     */

    /**
     * 新增套餐
     */

    /**
     * 门店开通套餐
     */


}
