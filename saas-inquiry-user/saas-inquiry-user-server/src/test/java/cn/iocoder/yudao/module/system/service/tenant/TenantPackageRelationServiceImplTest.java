package cn.iocoder.yudao.module.system.service.tenant;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_PACKAGE_RELATION_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.BaseIntegrationTest;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation.TenantPackageRelationPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation.TenantPackageRelationRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation.TenantPackageRelationSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageRelationDO;
import cn.iocoder.yudao.module.system.dal.mysql.tenant.TenantPackageRelationMapper;
import com.alibaba.fastjson.JSON;
import jakarta.annotation.Resource;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;

/**
 * {@link TenantPackageRelationServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import({TenantPackageRelationServiceImpl.class, TenantPackageServiceImpl.class, TenantServiceImpl.class})
public class TenantPackageRelationServiceImplTest extends BaseIntegrationTest {

    private static final Logger log = LoggerFactory.getLogger(TenantPackageRelationServiceImplTest.class);


    @Resource
    private TenantPackageRelationServiceImpl tenantPackageRelationService;

    @Resource
    private TenantPackageRelationMapper tenantPackageRelationMapper;

    @Test
    public void testCreateTenantPackageRelation_success() {
        // 准备参数
        TenantPackageRelationSaveReqVO createReqVO = randomPojo(TenantPackageRelationSaveReqVO.class).setId(null);

        // 门店开通套餐
//        createReqVO.setPackageId(28780489036400L).setStartTime(LocalDateTime.now()).setTenantId(1831623538495963137L).setCollectAccount(1);
        createReqVO.setPayVoucherUrls(Stream.of("111", "222").collect(Collectors.toList())).setPaymentType(1);
        // 调用
        Long tenantPackageRelationId = tenantPackageRelationService.createTenantPackageRelation(createReqVO);
        // 断言
        assertNotNull(tenantPackageRelationId);
        // 校验记录的属性是否正确
        TenantPackageRelationDO tenantPackageRelation = tenantPackageRelationMapper.selectById(tenantPackageRelationId);
        log.info("{}", JSON.toJSONString(tenantPackageRelation));
    }

    @Test
    public void testUpdateTenantPackageRelation_success() {
        // mock 数据
        TenantPackageRelationDO dbTenantPackageRelation = randomPojo(TenantPackageRelationDO.class);
        tenantPackageRelationMapper.insert(dbTenantPackageRelation);// @Sql: 先插入出一条存在的数据
        // 准备参数
        TenantPackageRelationSaveReqVO updateReqVO = randomPojo(TenantPackageRelationSaveReqVO.class, o -> {
            o.setId(dbTenantPackageRelation.getId()); // 设置更新的 ID
        });

        // 调用
        tenantPackageRelationService.updateTenantPackageRelation(updateReqVO);
        // 校验是否更新正确
        TenantPackageRelationDO tenantPackageRelation = tenantPackageRelationMapper.selectById(updateReqVO.getId()); // 获取最新的
        log.info("{},{}", updateReqVO, tenantPackageRelation);
    }

    @Test
    public void testUpdateTenantPackageRelation_notExists() {
        // 准备参数
        TenantPackageRelationSaveReqVO updateReqVO = randomPojo(TenantPackageRelationSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> tenantPackageRelationService.updateTenantPackageRelation(updateReqVO), TENANT_PACKAGE_RELATION_NOT_EXISTS);
    }

    @Test
    public void testDeleteTenantPackageRelation_success() {
        // mock 数据
        TenantPackageRelationDO dbTenantPackageRelation = randomPojo(TenantPackageRelationDO.class);
        tenantPackageRelationMapper.insert(dbTenantPackageRelation);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbTenantPackageRelation.getId();

        // 调用
        tenantPackageRelationService.deleteTenantPackageRelation(id);
        // 校验数据不存在了
        assertNull(tenantPackageRelationMapper.selectById(id));
    }

    @Test
    public void testDeleteTenantPackageRelation_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> tenantPackageRelationService.deleteTenantPackageRelation(id), TENANT_PACKAGE_RELATION_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetTenantPackageRelationPage() {
        // mock 数据
        TenantPackageRelationDO dbTenantPackageRelation = randomPojo(TenantPackageRelationDO.class, o -> { // 等会查询到
            o.setPackageId(null);
            o.setPackageName(null);
            o.setStatus(null);
            o.setStartTime(null);
            o.setEndTime(null);
            o.setPaymentType(null);
            o.setSignTime(null);
            o.setSignUser(null);
            o.setProxyUser(null);
            o.setSignChannel(null);
            o.setActualAmount(null);
            o.setCollectAccount(null);
            o.setPayNo(null);
            o.setRemark(null);
            o.setCreateTime(null);
        });
        tenantPackageRelationMapper.insert(dbTenantPackageRelation);
        // 测试 packageId 不匹配
        tenantPackageRelationMapper.insert(cloneIgnoreId(dbTenantPackageRelation, o -> o.setPackageId(null)));
        // 测试 packageName 不匹配
        tenantPackageRelationMapper.insert(cloneIgnoreId(dbTenantPackageRelation, o -> o.setPackageName(null)));
        // 测试 status 不匹配
        tenantPackageRelationMapper.insert(cloneIgnoreId(dbTenantPackageRelation, o -> o.setStatus(null)));
        // 测试 serverStart 不匹配
        tenantPackageRelationMapper.insert(cloneIgnoreId(dbTenantPackageRelation, o -> o.setStartTime(null)));
        // 测试 serverEnd 不匹配
        tenantPackageRelationMapper.insert(cloneIgnoreId(dbTenantPackageRelation, o -> o.setEndTime(null)));
        // 测试 paymentType 不匹配
        tenantPackageRelationMapper.insert(cloneIgnoreId(dbTenantPackageRelation, o -> o.setPaymentType(null)));
        // 测试 signTime 不匹配
        tenantPackageRelationMapper.insert(cloneIgnoreId(dbTenantPackageRelation, o -> o.setSignTime(null)));
        // 测试 signUser 不匹配
        tenantPackageRelationMapper.insert(cloneIgnoreId(dbTenantPackageRelation, o -> o.setSignUser(null)));
        // 测试 proxyUser 不匹配
        tenantPackageRelationMapper.insert(cloneIgnoreId(dbTenantPackageRelation, o -> o.setProxyUser(null)));
        // 测试 signChannel 不匹配
        tenantPackageRelationMapper.insert(cloneIgnoreId(dbTenantPackageRelation, o -> o.setSignChannel(null)));
        // 测试 actualAmount 不匹配
        tenantPackageRelationMapper.insert(cloneIgnoreId(dbTenantPackageRelation, o -> o.setActualAmount(null)));
        // 测试 collectAccount 不匹配
        tenantPackageRelationMapper.insert(cloneIgnoreId(dbTenantPackageRelation, o -> o.setCollectAccount(null)));
        // 测试 payNo 不匹配
        tenantPackageRelationMapper.insert(cloneIgnoreId(dbTenantPackageRelation, o -> o.setPayNo(null)));
        // 测试 remark 不匹配
        tenantPackageRelationMapper.insert(cloneIgnoreId(dbTenantPackageRelation, o -> o.setRemark(null)));
        // 测试 createTime 不匹配
        tenantPackageRelationMapper.insert(cloneIgnoreId(dbTenantPackageRelation, o -> o.setCreateTime(null)));
        // 准备参数
        TenantPackageRelationPageReqVO reqVO = new TenantPackageRelationPageReqVO();
        reqVO.setPackageId(null);
        reqVO.setPackageName(null);
        reqVO.setStatus(null);
        reqVO.setStartTime(null);
        reqVO.setEndTime(null);
        reqVO.setPaymentType(null);
        reqVO.setSignTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
        reqVO.setSignUser(null);
        reqVO.setProxyUser(null);
        reqVO.setSignChannel(null);
        reqVO.setActualAmount(null);
        reqVO.setCollectAccount(null);
        reqVO.setPayNo(null);
        reqVO.setRemark(null);
        reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

        // 调用
        PageResult<TenantPackageRelationRespVO> pageResult = tenantPackageRelationService.getTenantPackageRelationPage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbTenantPackageRelation, pageResult.getList().getFirst());
    }

}