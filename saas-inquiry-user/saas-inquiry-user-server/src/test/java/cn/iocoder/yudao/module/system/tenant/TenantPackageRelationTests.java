package cn.iocoder.yudao.module.system.tenant;

import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.system.BaseIntegrationTest;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation.TenantPackageRelationSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageRelationDO;
import cn.iocoder.yudao.module.system.dal.mysql.tenant.TenantPackageRelationMapper;
import cn.iocoder.yudao.module.system.service.tenant.TenantPackageRelationServiceImpl;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * 单元测试扫描当前包路径,相关api测试写到这个层级下或者建立相关包路径 com.xyy.saas.inquiry.drugstore.server
 */
@Import(TenantPackageRelationServiceImpl.class)
class TenantPackageRelationTests extends BaseIntegrationTest {

    @Resource
    private TenantPackageRelationServiceImpl tenantPackageRelationService;

    @Resource
    private TenantPackageRelationMapper tenantPackageRelationMapper;

    @BeforeEach
    public void before() {
        RequestAttributes requestAttributes = new ServletRequestAttributes(new MockHttpServletRequest());
        requestAttributes.setAttribute("login_user_id", 0L, RequestAttributes.SCOPE_REQUEST);
        RequestContextHolder.setRequestAttributes(requestAttributes);
        TenantContextHolder.setTenantId(1831623538495963137L);
    }

    /**
     * 初始化门店套餐
     */
    @Test
    public void testCreateTenantPackageRelation_success() {
        // 准备参数
        TenantPackageRelationSaveReqVO createReqVO = randomPojo(TenantPackageRelationSaveReqVO.class).setId(null);

        // 门店开通套餐
//        createReqVO.setPackageId(28780489036400L).setStartTime(LocalDateTime.now()).setTenantId(1831623538495963137L).setCollectAccount(1);
        createReqVO.setPayVoucherUrls(Stream.of("111", "222").collect(Collectors.toList()))
            .setPaymentType(1).setPackageNature(1).setPackageId(28780489036400L).setTenantId(1840295887797739521L).setActualAmount(BigDecimal.ONE);
        // 调用
//        Long tenantPackageRelationId = tenantPackageRelationService.createTenantPackageRelation(createReqVO);
//        // 断言
//        assertNotNull(tenantPackageRelationId);
        // 校验记录的属性是否正确
        TenantContextHolder.setIgnore(true);
        TenantPackageRelationDO tenantPackageRelation = tenantPackageRelationMapper.selectById(1840323984425168898L);
        System.out.println();
    }

}
