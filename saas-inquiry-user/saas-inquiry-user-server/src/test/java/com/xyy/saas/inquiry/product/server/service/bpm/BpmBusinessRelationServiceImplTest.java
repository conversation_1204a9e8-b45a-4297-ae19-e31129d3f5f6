package com.xyy.saas.inquiry.product.server.service.bpm;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static org.junit.jupiter.api.Assertions.assertEquals;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmBusinessRelationPageReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.bpm.BpmBusinessRelationDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.bpm.BpmBusinessRelationMapper;
import com.xyy.saas.inquiry.product.server.service.BaseIntegrationTest;
import com.xyy.saas.inquiry.product.server.service.bpm.BpmBusinessRelationServiceImpl;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

/**
 * {@link BpmBusinessRelationServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(BpmBusinessRelationServiceImpl.class)
public class BpmBusinessRelationServiceImplTest extends BaseIntegrationTest {

    @Resource
    private BpmBusinessRelationServiceImpl bpmBusinessRelationService;

    @Resource
    private BpmBusinessRelationMapper bpmBusinessRelationMapper;



    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetBpmBusinessRelationPage() {
       // mock 数据
       BpmBusinessRelationDO dbBpmBusinessRelation = randomPojo(BpmBusinessRelationDO.class, o -> { // 等会查询到
           o.setHeadTenantId(null);
           o.setBusinessType(null);
           o.setBusinessPref(null);
           o.setProcessInstanceId(null);
           o.setStartTime(null);
           o.setEndTime(null);
           o.setApplicant(null);
           o.setApprovalStatus(null);
           o.setRemark(null);
           o.setCreateTime(null);
       });
       bpmBusinessRelationMapper.insert(dbBpmBusinessRelation);
       // 测试 headTenantId 不匹配
       bpmBusinessRelationMapper.insert(cloneIgnoreId(dbBpmBusinessRelation, o -> o.setHeadTenantId(null)));
       // 测试 businessType 不匹配
       bpmBusinessRelationMapper.insert(cloneIgnoreId(dbBpmBusinessRelation, o -> o.setBusinessType(null)));
       // 测试 businessPref 不匹配
       bpmBusinessRelationMapper.insert(cloneIgnoreId(dbBpmBusinessRelation, o -> o.setBusinessPref(null)));
       // 测试 processInstanceId 不匹配
       bpmBusinessRelationMapper.insert(cloneIgnoreId(dbBpmBusinessRelation, o -> o.setProcessInstanceId(null)));
       // 测试 startTime 不匹配
       bpmBusinessRelationMapper.insert(cloneIgnoreId(dbBpmBusinessRelation, o -> o.setStartTime(null)));
       // 测试 endTime 不匹配
       bpmBusinessRelationMapper.insert(cloneIgnoreId(dbBpmBusinessRelation, o -> o.setEndTime(null)));
       // 测试 applicant 不匹配
       bpmBusinessRelationMapper.insert(cloneIgnoreId(dbBpmBusinessRelation, o -> o.setApplicant(null)));
       // 测试 approvalStatus 不匹配
       bpmBusinessRelationMapper.insert(cloneIgnoreId(dbBpmBusinessRelation, o -> o.setApprovalStatus(null)));
       // 测试 remark 不匹配
       bpmBusinessRelationMapper.insert(cloneIgnoreId(dbBpmBusinessRelation, o -> o.setRemark(null)));
       // 测试 createTime 不匹配
       bpmBusinessRelationMapper.insert(cloneIgnoreId(dbBpmBusinessRelation, o -> o.setCreateTime(null)));
       // 准备参数
       BpmBusinessRelationPageReqVO reqVO = new BpmBusinessRelationPageReqVO();
       reqVO.setHeadTenantId(null);
       reqVO.setBusinessType(null);
       reqVO.setBusinessPref(null);
       reqVO.setProcessInstanceId(null);
       reqVO.setStartTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setEndTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setApplicant(null);
       reqVO.setApprovalStatus(null);
       reqVO.setRemark(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<BpmBusinessRelationDO> pageResult = bpmBusinessRelationService.getBpmBusinessRelationPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbBpmBusinessRelation, pageResult.getList().get(0));
    }

}