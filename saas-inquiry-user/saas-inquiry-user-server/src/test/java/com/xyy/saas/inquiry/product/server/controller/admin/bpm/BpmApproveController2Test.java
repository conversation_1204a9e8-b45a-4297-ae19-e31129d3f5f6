package com.xyy.saas.inquiry.product.server.controller.admin.bpm;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceRespVO;
import cn.iocoder.yudao.module.bpm.convert.task.BpmProcessInstanceConvert;
import cn.iocoder.yudao.module.bpm.service.definition.BpmCategoryService;
import cn.iocoder.yudao.module.bpm.service.definition.BpmProcessDefinitionService;
import cn.iocoder.yudao.module.bpm.service.task.BpmTaskService;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import com.xyy.saas.inquiry.product.enums.BpmBusinessTypeEnum;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmApprovePageReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.bpm.BpmBusinessRelationDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductInfoDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductInfoMapper;
import com.xyy.saas.inquiry.product.server.service.bpm.BpmBusinessRelationService;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.flowable.engine.HistoryService;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class BpmApproveController2Test {

    @InjectMocks
    private BpmApproveController controller;

    @Mock
    private BpmBusinessRelationService bpmBusinessRelationService;

    @Mock
    private ProductInfoMapper productInfoMapper;

    @Mock
    private HistoryService historyService;

    @Mock
    private BpmTaskService taskService;

    @Mock
    private BpmProcessDefinitionService processDefinitionService;

    @Mock
    private BpmCategoryService categoryService;

    @Mock
    private AdminUserApi adminUserApi;

    @Mock
    private DeptApi deptApi;

    @Mock
    private BpmProcessInstanceConvert bpmProcessInstanceConvert;

    @BeforeEach
    public void setUp() {
        // 设置静态方法的mock
        mockStatic(BpmProcessInstanceConvert.class);
    }

    @Test
    public void testPage_Empty() {
        // 准备测试数据
        BpmApprovePageReqVO reqVO = new BpmApprovePageReqVO();

        // Mock 服务调用
        when(bpmBusinessRelationService.getBpmBusinessRelationPage(any())).thenReturn(PageResult.empty());

        // 调用接口
        controller.page(reqVO);

        // 验证业务类型已设置
        assertEquals(BpmBusinessTypeEnum.PRODUCT_FIRST_APPROVE.code, reqVO.getBusinessType());
    }

    @Test
    public void testPage_WithData() {
        // 准备测试数据
        BpmApprovePageReqVO reqVO = new BpmApprovePageReqVO();

        // Mock 业务关联数据
        List<BpmBusinessRelationDO> relationList = new ArrayList<>();
        BpmBusinessRelationDO relation = new BpmBusinessRelationDO();
        relation.setProcessInstanceId("instance1");
        relation.setBusinessPref("product1");
        relationList.add(relation);
        PageResult<BpmBusinessRelationDO> pageResult = new PageResult<>(relationList, 1L);

        // Mock 商品数据
        List<ProductInfoDO> productList = new ArrayList<>();
        ProductInfoDO product = new ProductInfoDO();
        product.setPref("product1");
        product.setCommonName("测试商品");
        productList.add(product);

        // Mock 流程实例查询
        HistoricProcessInstanceQuery query = mock(HistoricProcessInstanceQuery.class);
        when(historyService.createHistoricProcessInstanceQuery()).thenReturn(query);
        when(query.processInstanceIds(any())).thenReturn(query);
        when(query.includeProcessVariables()).thenReturn(query);
        when(query.list()).thenReturn(Collections.emptyList());

        // Mock 服务调用
        when(bpmBusinessRelationService.getBpmBusinessRelationPage(any())).thenReturn(pageResult);
        when(productInfoMapper.listByPref(any())).thenReturn(productList);
        when(taskService.getTaskMapByProcessInstanceIds(any())).thenReturn(Collections.emptyMap());
        when(processDefinitionService.getProcessDefinitionMap(any())).thenReturn(Collections.emptyMap());
        when(categoryService.getCategoryMap(any())).thenReturn(Collections.emptyMap());
        when(adminUserApi.getUserMap(any())).thenReturn(Collections.emptyMap());
        when(deptApi.getDeptMap(any())).thenReturn(Collections.emptyMap());

        PageResult<BpmProcessInstanceRespVO> bpmPageResult = new PageResult<>(Collections.emptyList(), 1L);
        when(BpmProcessInstanceConvert.INSTANCE.buildProcessInstancePage(any(), any(), any(), any(), any(), any()))
            .thenReturn(bpmPageResult);

        // 调用接口
        controller.page(reqVO);

        // 验证业务类型已设置
        assertEquals(BpmBusinessTypeEnum.PRODUCT_FIRST_APPROVE.code, reqVO.getBusinessType());
    }

    private void mockStatic(Class<?> clazz) {
        // 这里是一个简化的mock静态方法的实现
        // 实际项目中可能需要使用MockedStatic或PowerMock等工具
    }

    private void assertEquals(Object expected, Object actual) {
        if (!expected.equals(actual)) {
            throw new AssertionError("Expected: " + expected + ", but was: " + actual);
        }
    }
}