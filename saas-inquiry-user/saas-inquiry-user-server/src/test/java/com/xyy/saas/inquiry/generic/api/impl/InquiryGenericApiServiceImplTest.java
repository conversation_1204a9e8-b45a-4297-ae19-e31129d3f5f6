package com.xyy.saas.inquiry.generic.api.impl;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.generic.api.InquiryGenericApiService;
import com.xyy.saas.inquiry.generic.api.dto.diagnosis.InquiryDiagnosisDto;
import com.xyy.saas.inquiry.generic.api.dto.hospital.InquiryHospitalReqDto;
import com.xyy.saas.inquiry.generic.api.dto.hospital.InquiryHospitalRespDto;
import com.xyy.saas.inquiry.generic.model.GenericInvokeResponse;
import com.xyy.saas.inquiry.product.server.WebMvcIntegrationTest;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * InquiryGenericApiServiceImpl 的集成测试。
 * <p>
 * 注意：这是一个集成测试，它会真实地通过 Dubbo 调用远程服务。
 * 请确保 Dubbo provider 服务正在运行，并且测试环境可以访问注册中心。
 */
class InquiryGenericApiServiceImplTest extends WebMvcIntegrationTest {

    @Resource
    private InquiryGenericApiService inquiryGenericApiService;

    @Test
    void getInquiryHospitalsBaseInfoMap() {
        // Arrange
        InquiryHospitalReqDto reqDto = new InquiryHospitalReqDto();
        reqDto.setInquiryHospitalIds(List.of(1909144351472562178L, 1906982989937913857L)); // 示例ID

        // Act
        Map<String, InquiryHospitalRespDto> result = inquiryGenericApiService.getInquiryHospitalsBaseInfoMap(reqDto);

        // Assert
        assertNotNull(result, "返回的Map不应为null");
        // 可以在这里添加更多针对返回结果的断言
    }

    @Test
    void queryDiagnosisByCondition() {
        // Arrange
        List<String> dictNames = Collections.singletonList("感冒"); // 示例诊断名称

        // Act
        List<InquiryDiagnosisDto> result = inquiryGenericApiService.queryDiagnosisByCondition(dictNames);

        // Assert
        assertNotNull(result, "返回的诊断列表不应为null");
    }

    @Test
    void getDiagnosisPage() {
        // Arrange
        InquiryDiagnosisDto pageReqDto = new InquiryDiagnosisDto();
        pageReqDto.setPageNo(1);
        pageReqDto.setPageSize(10);

        // Act
        PageResult<InquiryDiagnosisDto> result = inquiryGenericApiService.getDiagnosisPage(pageReqDto);

        // Assert
        assertNotNull(result, "返回的分页结果不应为null");
        assertNotNull(result.getList(), "分页结果中的列表不应为null");
    }

    @Test
    void getDiagnosisByName() {
        // Arrange
        String diagnosisName = "高血压"; // 示例诊断名称

        // Act
        List<InquiryDiagnosisDto> result = inquiryGenericApiService.getDiagnosisByName(diagnosisName);

        // Assert
        assertNotNull(result, "返回的诊断列表不应为null");
    }

    @Test
    void getDiagnosisByIds() {
        // Arrange
        List<Long> ids = List.of(1945687397065527297L, 1945687397052944386L); // 示例ID

        // Act
        List<InquiryDiagnosisDto> result = inquiryGenericApiService.getDiagnosisByIds(ids);

        // Assert
        assertNotNull(result, "返回的诊断列表不应为null");
    }

    @Test
    void jobHandPharmacistOffline() {
        // Act & Assert
        assertDoesNotThrow(() -> inquiryGenericApiService.jobHandPharmacistOffline(), "调用药师离线任务不应抛出异常");
    }

    @Test
    void jobHandSaasMigration() {
        // Act & Assert
        assertDoesNotThrow(() -> inquiryGenericApiService.jobHandSaasMigration(), "调用SaaS迁移任务不应抛出异常");
    }

    @Test
    void jobHandAutoInquiryDoctor() {
        // Act & Assert
        assertDoesNotThrow(() -> inquiryGenericApiService.jobHandAutoInquiryDoctor(), "调用医生出停诊任务不应抛出异常");
    }

    @Test
    void invokeGeneric() {
        // Arrange: 调用一个已知存在的、简单的泛化接口作为测试
        String interfaceName = "com.xyy.saas.inquiry.hospital.api.hospital.InquiryHospitalApi";
        String methodName = "getInquiryHospitalsBaseInfoMap";
        String[] parameterTypes = {"com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalReqDto"};
        Object[] args = {new InquiryHospitalReqDto()};

        // Act
        GenericInvokeResponse response = inquiryGenericApiService.invokeGeneric(interfaceName, methodName, parameterTypes, args);

        // Assert
        assertNotNull(response, "泛化调用响应不应为null");
        assertTrue(response.isSuccess(), "泛化调用应成功: " + response.getErrorMessage());
        assertNotNull(response.getResult(), "泛化调用的结果不应为null");
    }
}
