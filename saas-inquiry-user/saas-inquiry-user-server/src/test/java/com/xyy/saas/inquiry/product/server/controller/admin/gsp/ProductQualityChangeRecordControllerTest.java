// package com.xyy.saas.inquiry.product.server.controller.admin.gsp;
//
// import cn.iocoder.yudao.framework.common.pojo.CommonResult;
// import cn.iocoder.yudao.framework.common.pojo.PageParam;
// import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
// import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.*;
// import org.junit.jupiter.api.Test;
// import org.springframework.http.MediaType;
// import org.springframework.security.test.context.support.WithMockUser;
//
// import javax.annotation.Resource;
// import java.util.ArrayList;
// import java.util.List;
//
// import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
// import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
// import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
//
// @WithMockUser(username = "admin", authorities = {"saas:product:quality-change:create","saas:product:quality-change:query"})
// public class ProductQualityChangeRecordControllerTest extends BaseDbUnitTest {
//
//     @Resource
//     private ProductQualityChangeRecordController controller;
//
//     @Test
//     public void testCreateQualityChange() throws Exception {
//         // 准备参数
//         ProductQualityChangeRecordSaveReqVO reqVO = new ProductQualityChangeRecordSaveReqVO();
//         reqVO.setRemark("测试质量变更");
//
//         // 准备明细
//         List<ProductQualityChangeDetailSaveReqVO> details = new ArrayList<>();
//         ProductQualityChangeDetailSaveReqVO detail = new ProductQualityChangeDetailSaveReqVO();
//         detail.setItemId(1L);
//         detail.setContent("{\"field\":\"value\"}");
//         details.add(detail);
//         reqVO.setDetails(details);
//
//         // 调用接口
//         mockMvc.perform(post("/product/quality-change/save")
//                 .contentType(MediaType.APPLICATION_JSON)
//                 .content(objectMapper.writeValueAsString(reqVO)))
//                 .andExpect(status().isOk())
//                 .andExpect(jsonPath("$.code").value(0));
//     }
//
//     @Test
//     public void testGetQualityChangePage() throws Exception {
//         // 准备参数
//         ProductQualityChangeRecordPageReqVO pageReqVO = new ProductQualityChangeRecordPageReqVO();
//         pageReqVO.setPageNo(1);
//         pageReqVO.setPageSize(10);
//
//         // 调用接口
//         mockMvc.perform(get("/product/quality-change/page")
//                 .contentType(MediaType.APPLICATION_JSON)
//                 .content(objectMapper.writeValueAsString(pageReqVO)))
//                 .andExpect(status().isOk())
//                 .andExpect(jsonPath("$.code").value(0));
//     }
//
//     @Test
//     public void testGetQualityChangeDetail() throws Exception {
//         // 先创建质量变更申请
//         Long recordId = createQualityChange();
//
//         // 调用接口
//         mockMvc.perform(get("/product/quality-change/get")
//                 .param("id", String.valueOf(recordId)))
//                 .andExpect(status().isOk())
//                 .andExpect(jsonPath("$.code").value(0));
//     }
//
//     @Test
//     public void testGetQualityChangeDetailPage() throws Exception {
//         // 先创建质量变更申请
//         Long recordId = createQualityChange();
//
//         // 准备参数
//         PageParam pageParam = new PageParam();
//         pageParam.setPageNo(1);
//         pageParam.setPageSize(10);
//
//         // 调用接口
//         mockMvc.perform(get("/product/quality-change/product-quality-change-detail/page")
//                 .param("recordId", String.valueOf(recordId))
//                 .contentType(MediaType.APPLICATION_JSON)
//                 .content(objectMapper.writeValueAsString(pageParam)))
//                 .andExpect(status().isOk())
//                 .andExpect(jsonPath("$.code").value(0));
//     }
//
//     private Long createQualityChange() {
//         ProductQualityChangeRecordSaveReqVO reqVO = new ProductQualityChangeRecordSaveReqVO();
//         // 设置必要参数
//         reqVO.setRemark("测试质量变更");
//         List<ProductQualityChangeDetailSaveReqVO> details = new ArrayList<>();
//         // ... 设置明细
//         reqVO.setDetails(details);
//         CommonResult<Long> result = controller.submitQualityChange(reqVO);
//         return result.getData();
//     }
// }