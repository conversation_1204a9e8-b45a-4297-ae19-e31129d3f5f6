package com.xyy.saas.inquiry.product.server.service.catalog;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.CATALOG_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogSaveReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.CatalogDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.catalog.CatalogMapper;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

/**
 * {@link CatalogServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(CatalogServiceImpl.class)
public class CatalogServiceImplTest extends BaseDbUnitTest {

    @Resource
    private CatalogServiceImpl catalogService;

    @Resource
    private CatalogMapper catalogMapper;

    @Test
    public void testCreateCatalog_success() {
        // 准备参数
        CatalogSaveReqVO createReqVO = randomPojo(CatalogSaveReqVO.class).setId(null);

        // 调用
        CatalogRespVO catalogRespVO = catalogService.createCatalog(createReqVO);
        // 断言
        assertNotNull(catalogRespVO.getId());
        // 校验记录的属性是否正确
        CatalogDO catalog = catalogMapper.selectById(catalogRespVO.getId());
        assertPojoEquals(createReqVO, catalog, "id");
    }

    @Test
    public void testUpdateCatalog_success() {
        // mock 数据
        CatalogDO dbCatalog = randomPojo(CatalogDO.class);
        catalogMapper.insert(dbCatalog);// @Sql: 先插入出一条存在的数据
        // 准备参数
        CatalogSaveReqVO updateReqVO = randomPojo(CatalogSaveReqVO.class, o -> {
            o.setId(dbCatalog.getId()); // 设置更新的 ID
        });

        // 调用
        catalogService.updateCatalog(updateReqVO);
        // 校验是否更新正确
        CatalogDO catalog = catalogMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, catalog);
    }

    @Test
    public void testUpdateCatalog_notExists() {
        // 准备参数
        CatalogSaveReqVO updateReqVO = randomPojo(CatalogSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> catalogService.updateCatalog(updateReqVO), CATALOG_NOT_EXISTS);
    }

    @Test
    public void testDeleteCatalog_success() {
        // mock 数据
        CatalogDO dbCatalog = randomPojo(CatalogDO.class);
        catalogMapper.insert(dbCatalog);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbCatalog.getId();

        // 调用
        catalogService.deleteCatalog(id);
       // 校验数据不存在了
       assertNull(catalogMapper.selectById(id));
    }

    @Test
    public void testDeleteCatalog_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> catalogService.deleteCatalog(id), CATALOG_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetCatalogPage() {
       // mock 数据
       CatalogDO dbCatalog = randomPojo(CatalogDO.class, o -> { // 等会查询到
           o.setPref(null);
           o.setName(null);
           o.setType(null);
           o.setProjectCodeType(null);
           o.setVersion(null);
           o.setVersionCode(null);
           o.setUploadUrl(null);
           o.setNeedDownload(null);
           o.setDownloadUrl(null);
           o.setTotalCount(null);
           o.setMatchedCount(null);
           o.setUnmatchedCount(null);
           o.setDisable(null);
           o.setEnv(null);
           o.setRemark(null);
           o.setCreateTime(null);
       });
       catalogMapper.insert(dbCatalog);
       // 测试 pref 不匹配
       catalogMapper.insert(cloneIgnoreId(dbCatalog, o -> o.setPref(null)));
       // 测试 name 不匹配
       catalogMapper.insert(cloneIgnoreId(dbCatalog, o -> o.setName(null)));
       // 测试 type 不匹配
       catalogMapper.insert(cloneIgnoreId(dbCatalog, o -> o.setType(null)));
       // 测试 projectCodeType 不匹配
       catalogMapper.insert(cloneIgnoreId(dbCatalog, o -> o.setProjectCodeType(null)));
       // 测试 version 不匹配
       catalogMapper.insert(cloneIgnoreId(dbCatalog, o -> o.setVersion(null)));
       // 测试 versionCode 不匹配
       catalogMapper.insert(cloneIgnoreId(dbCatalog, o -> o.setVersionCode(null)));
       // 测试 uploadUrl 不匹配
       catalogMapper.insert(cloneIgnoreId(dbCatalog, o -> o.setUploadUrl(null)));
       // 测试 needDownload 不匹配
       catalogMapper.insert(cloneIgnoreId(dbCatalog, o -> o.setNeedDownload(null)));
       // 测试 downloadUrl 不匹配
       catalogMapper.insert(cloneIgnoreId(dbCatalog, o -> o.setDownloadUrl(null)));
       // 测试 totalCount 不匹配
       catalogMapper.insert(cloneIgnoreId(dbCatalog, o -> o.setTotalCount(null)));
       // 测试 matchedCount 不匹配
       catalogMapper.insert(cloneIgnoreId(dbCatalog, o -> o.setMatchedCount(null)));
       // 测试 unmatchedCount 不匹配
       catalogMapper.insert(cloneIgnoreId(dbCatalog, o -> o.setUnmatchedCount(null)));
       // 测试 disable 不匹配
       catalogMapper.insert(cloneIgnoreId(dbCatalog, o -> o.setDisable(null)));
       // 测试 env 不匹配
       catalogMapper.insert(cloneIgnoreId(dbCatalog, o -> o.setEnv(null)));
       // 测试 remark 不匹配
       catalogMapper.insert(cloneIgnoreId(dbCatalog, o -> o.setRemark(null)));
       // 测试 createTime 不匹配
       catalogMapper.insert(cloneIgnoreId(dbCatalog, o -> o.setCreateTime(null)));
       // 准备参数
       CatalogPageReqVO reqVO = new CatalogPageReqVO();
       reqVO.setPref(null);
       reqVO.setName(null);
       reqVO.setType(null);
       reqVO.setProjectCodeType(null);
       reqVO.setVersion(null);
       reqVO.setVersionCode(null);
       reqVO.setUploadUrl(null);
       reqVO.setNeedDownload(null);
       reqVO.setDownloadUrl(null);
       reqVO.setTotalCount(null);
       reqVO.setMatchedCount(null);
       reqVO.setUnmatchedCount(null);
       reqVO.setDisable(null);
       reqVO.setEnv(null);
       reqVO.setRemark(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
        PageResult<CatalogRespVO> pageResult = catalogService.getCatalogPage(reqVO);
        // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbCatalog, pageResult.getList().get(0));
    }

}