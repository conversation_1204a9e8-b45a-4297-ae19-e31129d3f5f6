package com.xyy.saas.inquiry.product.server.service.product;

import cn.iocoder.yudao.framework.redis.core.TimeoutRedisCacheManager;
import cn.iocoder.yudao.framework.tenant.core.redis.TenantRedisCacheManager;
import cn.iocoder.yudao.framework.websocket.config.YudaoWebSocketAutoConfiguration;
import cn.iocoder.yudao.module.system.BaseIntegrationTest;
import cn.iocoder.yudao.module.system.api.dict.DictDataApiImpl;
import cn.iocoder.yudao.module.system.api.tenant.TenantApiImpl;
import cn.iocoder.yudao.module.system.api.user.AdminUserApiImpl;
import cn.iocoder.yudao.module.system.service.biz.BizService;
import cn.iocoder.yudao.module.system.service.biz.BizServiceImpl;
import cn.iocoder.yudao.module.system.service.dict.DictDataServiceImpl;
import cn.iocoder.yudao.module.system.service.permission.MenuService;
import cn.iocoder.yudao.module.system.service.permission.MenuServiceImpl;
import cn.iocoder.yudao.module.system.service.permission.PermissionService;
import cn.iocoder.yudao.module.system.service.permission.PermissionServiceImpl;
import cn.iocoder.yudao.module.system.service.permission.RoleService;
import cn.iocoder.yudao.module.system.service.permission.RoleServiceImpl;
import cn.iocoder.yudao.module.system.service.tenant.TenantBizRelationService;
import cn.iocoder.yudao.module.system.service.tenant.TenantBizRelationServiceImpl;
import cn.iocoder.yudao.module.system.service.tenant.TenantCertificateService;
import cn.iocoder.yudao.module.system.service.tenant.TenantCertificateServiceImpl;
import cn.iocoder.yudao.module.system.service.tenant.TenantHeadServiceImpl;
import cn.iocoder.yudao.module.system.service.tenant.TenantPackageRelationService;
import cn.iocoder.yudao.module.system.service.tenant.TenantPackageRelationServiceImpl;
import cn.iocoder.yudao.module.system.service.tenant.TenantPackageService;
import cn.iocoder.yudao.module.system.service.tenant.TenantPackageServiceImpl;
import cn.iocoder.yudao.module.system.service.tenant.TenantServiceImpl;
import cn.iocoder.yudao.module.system.service.tenant.TenantUserRelationService;
import cn.iocoder.yudao.module.system.service.tenant.TenantUserRelationServiceImpl;
import com.xyy.saas.inquiry.product.server.config.ProductCategoryCacheConfig;
import com.xyy.saas.inquiry.product.server.config.forward.ForwardWebClientConfig;
import com.xyy.saas.inquiry.product.server.config.forward.InquiryDubboForwardClient;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductPriceUpdateReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductInfoDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductUseInfoDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductInfoMapper;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductUseInfoMapper;
import com.xyy.saas.inquiry.product.server.service.bpm.BpmBusinessInitService;
import com.xyy.saas.inquiry.product.server.service.bpm.BpmBusinessRelationServiceImpl;
import com.xyy.saas.inquiry.product.server.service.gsp.ProductPriceAdjustmentRecordService;
import com.xyy.saas.inquiry.product.server.service.gsp.ProductPriceAdjustmentRecordServiceImpl;
import com.xyy.saas.inquiry.product.server.service.gsp.ProductQualityChangeRecordServiceImpl;
import com.xyy.saas.inquiry.product.server.service.product.mid.ProductMidStdlibServiceImpl;
import com.xyy.saas.inquiry.product.server.service.productcategory.ProductCategoryServiceImpl;
import com.xyy.saas.inquiry.product.server.service.transfer.ProductTransferRecordServiceImpl;
import com.xyy.saas.inquiry.user.server.InquiryUserServerApplication;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

import java.math.BigDecimal;
import java.util.List;

import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_INFO_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * {@link ProductInfoServiceImpl} 商品价格修改功能的单元测试类
 *
 * <AUTHOR>
 */
// @Import({ProductInfoServiceImpl.class,
//     ProductStdlibServiceImpl.class,
//     ProductMidStdlibServiceImpl.class,
//     ForwardWebClientConfig.class,
//     ForwardWebClientConfig.class,
//     DictDataApiImpl.class,
//     DictDataServiceImpl.class,
//     ProductTransferRecordServiceImpl.class,
//     ProductCategoryServiceImpl.class,
//     ProductQualityChangeRecordServiceImpl.class,
//     ProductPriceAdjustmentRecordServiceImpl.class,
//     BpmBusinessRelationServiceImpl.class,
//     ProductCategoryCacheConfig.class,
//     CaffeineCacheManager.class,
//     TenantApiImpl.class,
//     TenantServiceImpl.class, TenantPackageServiceImpl.class,
//     RoleServiceImpl.class,
//     MenuServiceImpl.class,
//     PermissionServiceImpl.class,
//     TenantUserRelationServiceImpl.class,
//     TenantPackageRelationServiceImpl.class,
//     TenantCertificateServiceImpl.class,
//     BizServiceImpl.class,
//     TenantBizRelationServiceImpl.class,
//     TenantHeadServiceImpl.class,
//     TenantBizRelationServiceImpl.class,
//     AdminUserApiImpl.class,
//     LocalValidatorFactoryBean.class})
@ComponentScan(value = {
    "com.xyy.saas.inquiry.product.server",
    // "cn.iocoder.yudao.module.system"
}, lazyInit = true)

@SpringBootTest(classes = BaseIntegrationTest.Application.class, webEnvironment = SpringBootTest.WebEnvironment.NONE, properties = {
    "spring.config.location=classpath:/application-test.yml,classpath:/application.yml" // 同时加载两个配置文件
})
public class ProductInfoServiceImplPriceTest extends BaseIntegrationTest {

    @Resource
    private ProductInfoServiceImpl productInfoService;

    @Resource
    private ProductInfoMapper productInfoMapper;

    @Resource
    private ProductUseInfoMapper productUseInfoMapper;

    @MockBean
    private ProductPriceAdjustmentRecordService priceAdjustmentRecordService;

    @Test
    public void testUpdateProductPrice_success() {
        // 准备数据：商品基本信息
        ProductInfoDO productInfo = randomPojo(ProductInfoDO.class, o -> {
            o.setId(1L);
            o.setPref("PROD001");
            o.setTenantId(100L);
        });
        productInfoMapper.insert(productInfo);

        // 准备数据：商品使用信息
        ProductUseInfoDO useInfo = randomPojo(ProductUseInfoDO.class, o -> {
            o.setProductPref("PROD001");
            o.setTenantId(100L);
            o.setHeadTenantId(100L);
            o.setRetailPrice(new BigDecimal("100.00"));
            o.setMemberPrice(new BigDecimal("90.00"));
        });
        productUseInfoMapper.insert(useInfo);

        // 准备参数
        ProductPriceUpdateReqVO reqVO = new ProductPriceUpdateReqVO();
        reqVO.setId(1L);
        reqVO.setRetailPrice(new BigDecimal("120.00"));
        reqVO.setMemberPrice(new BigDecimal("110.00"));
        reqVO.setTargetTenantIdList(List.of(100L));

        // 调用
        productInfoService.updateProductPrice(reqVO);

        // 验证：商品使用信息已更新
        ProductUseInfoDO updatedUseInfo = productUseInfoMapper.selectList(
            List.of(100L), List.of("PROD001")).get(0);
        assertEquals(new BigDecimal("120.00"), updatedUseInfo.getRetailPrice());
        assertEquals(new BigDecimal("110.00"), updatedUseInfo.getMemberPrice());

        // 验证：调用了售价调整单服务
        verify(priceAdjustmentRecordService, times(1))
            .savePriceAdjustmentRecord(any(), any(), any());
    }

    @Test
    public void testUpdateProductPrice_createDefaultUseInfo() {
        // 准备数据：只有商品基本信息，没有使用信息
        ProductInfoDO productInfo = randomPojo(ProductInfoDO.class, o -> {
            o.setId(2L);
            o.setPref("PROD002");
            o.setTenantId(200L);
        });
        productInfoMapper.insert(productInfo);

        // 准备参数
        ProductPriceUpdateReqVO reqVO = new ProductPriceUpdateReqVO();
        reqVO.setId(2L);
        reqVO.setRetailPrice(new BigDecimal("150.00"));
        reqVO.setMemberPrice(new BigDecimal("140.00"));
        reqVO.setTargetTenantIdList(List.of(200L));

        // 调用
        productInfoService.updateProductPrice(reqVO);

        // 验证：创建了默认使用信息并更新了价格
        List<ProductUseInfoDO> useInfoList = productUseInfoMapper.selectList(
            List.of(200L), List.of("PROD002"));
        assertEquals(1, useInfoList.size());
        
        ProductUseInfoDO createdUseInfo = useInfoList.get(0);
        assertEquals(new BigDecimal("150.00"), createdUseInfo.getRetailPrice());
        assertEquals(new BigDecimal("140.00"), createdUseInfo.getMemberPrice());
        assertEquals("PROD002", createdUseInfo.getProductPref());
        assertEquals(200L, createdUseInfo.getTenantId());
    }

    @Test
    public void testUpdateProductPrice_multiTenant() {
        // 准备数据：商品基本信息
        ProductInfoDO productInfo = randomPojo(ProductInfoDO.class, o -> {
            o.setId(3L);
            o.setPref("PROD003");
            o.setTenantId(300L);
        });
        productInfoMapper.insert(productInfo);

        // 准备数据：多个租户的使用信息
        ProductUseInfoDO useInfo1 = randomPojo(ProductUseInfoDO.class, o -> {
            o.setProductPref("PROD003");
            o.setTenantId(300L);
            o.setHeadTenantId(300L);
            o.setRetailPrice(new BigDecimal("100.00"));
            o.setMemberPrice(new BigDecimal("90.00"));
        });
        ProductUseInfoDO useInfo2 = randomPojo(ProductUseInfoDO.class, o -> {
            o.setProductPref("PROD003");
            o.setTenantId(301L);
            o.setHeadTenantId(300L);
            o.setRetailPrice(new BigDecimal("105.00"));
            o.setMemberPrice(new BigDecimal("95.00"));
        });
        productUseInfoMapper.insert(useInfo1);
        productUseInfoMapper.insert(useInfo2);

        // 准备参数：修改多个租户的价格
        ProductPriceUpdateReqVO reqVO = new ProductPriceUpdateReqVO();
        reqVO.setId(3L);
        reqVO.setRetailPrice(new BigDecimal("130.00"));
        reqVO.setMemberPrice(new BigDecimal("120.00"));
        reqVO.setTargetTenantIdList(List.of(300L, 301L));

        // 调用
        productInfoService.updateProductPrice(reqVO);

        // 验证：两个租户的价格都已更新
        List<ProductUseInfoDO> updatedUseInfoList = productUseInfoMapper.selectList(
            List.of(300L, 301L), List.of("PROD003"));
        assertEquals(2, updatedUseInfoList.size());

        for (ProductUseInfoDO updatedUseInfo : updatedUseInfoList) {
            assertEquals(new BigDecimal("130.00"), updatedUseInfo.getRetailPrice());
            assertEquals(new BigDecimal("120.00"), updatedUseInfo.getMemberPrice());
        }

        // 验证：为每个租户都调用了售价调整单服务
        verify(priceAdjustmentRecordService, times(2))
            .savePriceAdjustmentRecord(any(), any(), any());
    }

    @Test
    public void testUpdateProductPrice_partialUpdate() {
        // 准备数据
        ProductInfoDO productInfo = randomPojo(ProductInfoDO.class, o -> {
            o.setId(4L);
            o.setPref("PROD004");
            o.setTenantId(400L);
        });
        productInfoMapper.insert(productInfo);

        ProductUseInfoDO useInfo = randomPojo(ProductUseInfoDO.class, o -> {
            o.setProductPref("PROD004");
            o.setTenantId(400L);
            o.setHeadTenantId(400L);
            o.setRetailPrice(new BigDecimal("200.00"));
            o.setMemberPrice(new BigDecimal("180.00"));
        });
        productUseInfoMapper.insert(useInfo);

        // 准备参数：只修改零售价
        ProductPriceUpdateReqVO reqVO = new ProductPriceUpdateReqVO();
        reqVO.setId(4L);
        reqVO.setRetailPrice(new BigDecimal("220.00"));
        // 不设置memberPrice
        reqVO.setTargetTenantIdList(List.of(400L));

        // 调用
        productInfoService.updateProductPrice(reqVO);

        // 验证：只有零售价被更新，会员价保持不变
        ProductUseInfoDO updatedUseInfo = productUseInfoMapper.selectList(
            List.of(400L), List.of("PROD004")).get(0);
        assertEquals(new BigDecimal("220.00"), updatedUseInfo.getRetailPrice());
        assertEquals(new BigDecimal("180.00"), updatedUseInfo.getMemberPrice()); // 保持原值
    }

    @Test
    public void testUpdateProductPrice_productNotExists() {
        // 准备参数：使用不存在的商品ID
        ProductPriceUpdateReqVO reqVO = new ProductPriceUpdateReqVO();
        reqVO.setId(999L);
        reqVO.setRetailPrice(new BigDecimal("100.00"));

        // 调用并断言异常
        assertServiceException(() -> productInfoService.updateProductPrice(reqVO), 
            PRODUCT_INFO_NOT_EXISTS);

        // 验证：没有调用售价调整单服务
        verify(priceAdjustmentRecordService, never())
            .savePriceAdjustmentRecord(any(), any(), any());
    }

    @Test
    public void testUpdateProductPrice_emptyTargetTenantList() {
        // 准备数据
        ProductInfoDO productInfo = randomPojo(ProductInfoDO.class, o -> {
            o.setId(5L);
            o.setPref("PROD005");
            o.setTenantId(500L);
        });
        productInfoMapper.insert(productInfo);

        // 准备参数：不指定目标租户列表
        ProductPriceUpdateReqVO reqVO = new ProductPriceUpdateReqVO();
        reqVO.setId(5L);
        reqVO.setRetailPrice(new BigDecimal("100.00"));
        // targetTenantIdList为空

        // 调用
        productInfoService.updateProductPrice(reqVO);

        // 验证：使用了默认的租户列表（总部租户）
        verify(priceAdjustmentRecordService, times(1))
            .savePriceAdjustmentRecord(any(), any(), any());
    }
}
