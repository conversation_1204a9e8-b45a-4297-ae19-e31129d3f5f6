package com.xyy.saas.inquiry.product.server.controller.admin.bpm;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xyy.saas.inquiry.product.server.WebMvcIntegrationTest;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmBusinessRelationPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmTaskApprovalValidateReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.bpm.BpmBusinessRelationDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.bpm.BpmBusinessRelationMapper;
import com.xyy.saas.inquiry.product.server.service.BaseIntegrationTest;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;

/**
 * 审批流关联业务 Controller 集成测试
 */
@Slf4j
public class BpmBusinessRelationControllerTest extends WebMvcIntegrationTest {

    @Resource
    private BpmBusinessRelationMapper bpmBusinessRelationMapper;

    /**
     * GET /bpm/business-relation/get
     */
    @Test
    public void testGetBpmBusinessRelation() throws Exception {
        // 准备数据
        BpmBusinessRelationDO relation = BpmBusinessRelationDO.builder()
            .businessType(1)
            .businessPref("TEST-PREF")
            .applicant("tester")
            .startTime(LocalDateTime.now())
            .build();
        bpmBusinessRelationMapper.insert(relation);

        mockMvc.perform(get("/admin-api/bpm/business-relation/get")
                .param("id", String.valueOf(relation.getId())))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(0))
            .andExpect(jsonPath("$.data.id").value(relation.getId()));
    }

    /**
     * GET /bpm/business-relation/page
     */
    @Test
    public void testGetBpmBusinessRelationPage() throws Exception {
        // 准备一条数据
        BpmBusinessRelationDO relation = BpmBusinessRelationDO.builder()
            .businessType(1)
            .businessPref("PAGE-PREF")
            .applicant("tester")
            .startTime(LocalDateTime.now())
            .build();
        bpmBusinessRelationMapper.insert(relation);

        BpmBusinessRelationPageReqVO pageReqVO = new BpmBusinessRelationPageReqVO();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(10);

        mockMvc.perform(get("/admin-api/bpm/business-relation/page")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(pageReqVO)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(0))
            .andExpect(jsonPath("$.data.list[0].id").value(relation.getId()));
    }

    /**
     * GET /bpm/business-relation/export-excel
     */
    @Test
    public void testExportExcel() throws Exception {
        mockMvc.perform(get("/admin-api/bpm/business-relation/export-excel"))
            .andExpect(status().isOk());
    }

    /**
     * POST /bpm/business-relation/validate —— 无对应验证器时应返回 false
     */
    @Test
    public void testValidateReturnFalse() throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("dummy", "value");

        BpmTaskApprovalValidateReqVO reqVO = new BpmTaskApprovalValidateReqVO();
        reqVO.setTaskId("123");
        reqVO.setValidatorType("not_exist");
        reqVO.setBusinessType(999);
        reqVO.setParams(params);

        String resp = mockMvc.perform(post("/admin-api/bpm/business-relation/validate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(reqVO)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(0))
            .andReturn()
            .getResponse()
            .getContentAsString();

        CommonResult<Boolean> result =
            objectMapper.readValue(resp, new TypeReference<CommonResult<Boolean>>() {});

        assert Boolean.FALSE.equals(result.getCheckedData());
    }
}