package com.xyy.saas.inquiry.product.server.service.productcategory;

import cn.iocoder.yudao.module.system.BaseIntegrationTest;
import com.github.benmanes.caffeine.cache.Cache;
import com.xyy.saas.inquiry.product.server.controller.admin.productcategory.vo.ProductCategoryRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.productcategory.vo.ProductCategorySaveReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.productcategory.ProductCategoryDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.productcategory.ProductCategoryMapper;
import com.xyy.saas.inquiry.product.server.dal.redis.ProductCategoryCacheConstants;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Import;

import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * {@link ProductCategoryServiceImpl} 分级缓存功能的单元测试类
 *
 * <AUTHOR>
 */
@Import(ProductCategoryServiceImpl.class)
public class ProductCategoryServiceImplCacheTest extends BaseIntegrationTest {

    @Resource
    private ProductCategoryServiceImpl productCategoryService;

    @Resource
    private ProductCategoryMapper productCategoryMapper;

    @MockBean
    private CacheManager cacheManager;

    @MockBean
    private Cache<String, List<ProductCategoryRespVO>> localCache;

    @Test
    public void testGetRootCategories_success() {
        // 准备数据：根分类
        ProductCategoryDO rootCategory1 = randomPojo(ProductCategoryDO.class, o -> {
            o.setId(1L);
            o.setName("医疗器械");
            o.setDictId(1001L);
            o.setParentDictId(ProductCategoryDO.PARENT_DICT_ID_ROOT);
            o.setSortOrder(1);
            o.setUpdateTime(LocalDateTime.now().minusDays(1));
        });
        ProductCategoryDO rootCategory2 = randomPojo(ProductCategoryDO.class, o -> {
            o.setId(2L);
            o.setName("药品");
            o.setDictId(1002L);
            o.setParentDictId(ProductCategoryDO.PARENT_DICT_ID_ROOT);
            o.setSortOrder(2);
            o.setUpdateTime(LocalDateTime.now());
        });
        productCategoryMapper.insert(rootCategory1);
        productCategoryMapper.insert(rootCategory2);

        // Mock本地缓存为空
        when(localCache.getIfPresent(ProductCategoryCacheConstants.LOCAL_CACHE_KEY_ROOT_CATEGORIES))
            .thenReturn(null);

        // 调用
        List<ProductCategoryRespVO> result = productCategoryService.getRootCategories();

        // 验证
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // 验证排序（按更新时间倒序）
        assertEquals("药品", result.get(0).getName());
        assertEquals("医疗器械", result.get(1).getName());

        // 验证本地缓存被调用
        verify(localCache).getIfPresent(ProductCategoryCacheConstants.LOCAL_CACHE_KEY_ROOT_CATEGORIES);
        verify(localCache).put(eq(ProductCategoryCacheConstants.LOCAL_CACHE_KEY_ROOT_CATEGORIES), any());
    }

    @Test
    public void testGetRootCategories_fromLocalCache() {
        // 准备本地缓存数据
        ProductCategoryRespVO cachedCategory = new ProductCategoryRespVO();
        cachedCategory.setId(1L);
        cachedCategory.setName("缓存分类");
        cachedCategory.setDictId(1001L);
        List<ProductCategoryRespVO> cachedResult = List.of(cachedCategory);

        // Mock本地缓存返回数据
        when(localCache.getIfPresent(ProductCategoryCacheConstants.LOCAL_CACHE_KEY_ROOT_CATEGORIES))
            .thenReturn(cachedResult);

        // 调用
        List<ProductCategoryRespVO> result = productCategoryService.getRootCategories();

        // 验证：返回缓存数据
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("缓存分类", result.get(0).getName());

        // 验证：没有查询数据库
        verify(localCache).getIfPresent(ProductCategoryCacheConstants.LOCAL_CACHE_KEY_ROOT_CATEGORIES);
        verify(localCache, never()).put(anyString(), any());
    }

    @Test
    public void testGetChildCategories_success() {
        // 准备数据：父分类和子分类
        Long parentDictId = 1001L;
        ProductCategoryDO childCategory1 = randomPojo(ProductCategoryDO.class, o -> {
            o.setId(3L);
            o.setName("一次性医疗器械");
            o.setDictId(2001L);
            o.setParentDictId(parentDictId);
            o.setUpdateTime(LocalDateTime.now().minusHours(1));
        });
        ProductCategoryDO childCategory2 = randomPojo(ProductCategoryDO.class, o -> {
            o.setId(4L);
            o.setName("植入性医疗器械");
            o.setDictId(2002L);
            o.setParentDictId(parentDictId);
            o.setUpdateTime(LocalDateTime.now());
        });
        productCategoryMapper.insert(childCategory1);
        productCategoryMapper.insert(childCategory2);

        // Mock本地缓存为空
        String localCacheKey = ProductCategoryCacheConstants.LOCAL_CACHE_KEY_CHILDREN_PREFIX + parentDictId;
        when(localCache.getIfPresent(localCacheKey)).thenReturn(null);

        // 调用
        List<ProductCategoryRespVO> result = productCategoryService.getChildCategories(parentDictId);

        // 验证
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // 验证排序（按更新时间倒序）
        assertEquals("植入性医疗器械", result.get(0).getName());
        assertEquals("一次性医疗器械", result.get(1).getName());

        // 验证本地缓存被调用（子分类数量<=50，会被缓存）
        verify(localCache).getIfPresent(localCacheKey);
        verify(localCache).put(eq(localCacheKey), any());
    }

    @Test
    public void testGetChildCategories_parentNull() {
        // Mock getRootCategories方法
        ProductCategoryServiceImpl spyService = spy(productCategoryService);
        List<ProductCategoryRespVO> rootCategories = List.of(new ProductCategoryRespVO());
        doReturn(rootCategories).when(spyService).getRootCategories();

        // 调用：父ID为null
        List<ProductCategoryRespVO> result = spyService.getChildCategories(null);

        // 验证：调用了getRootCategories
        assertEquals(rootCategories, result);
        verify(spyService).getRootCategories();
    }

    @Test
    public void testGetCategoryDetail_success() {
        // 准备数据
        Long dictId = 1001L;
        ProductCategoryDO category = randomPojo(ProductCategoryDO.class, o -> {
            o.setId(1L);
            o.setName("医疗器械");
            o.setDictId(dictId);
            o.setParentDictId(ProductCategoryDO.PARENT_DICT_ID_ROOT);
        });
        productCategoryMapper.insert(category);

        // 调用
        ProductCategoryRespVO result = productCategoryService.getCategoryDetail(dictId);

        // 验证
        assertNotNull(result);
        assertEquals("医疗器械", result.getName());
        assertEquals(dictId, result.getDictId());
    }

    @Test
    public void testGetProductCategoryTreeWithChildren_success() {
        // 准备数据：多层级分类
        ProductCategoryDO rootCategory = randomPojo(ProductCategoryDO.class, o -> {
            o.setId(1L);
            o.setName("医疗器械");
            o.setDictId(1001L);
            o.setParentDictId(ProductCategoryDO.PARENT_DICT_ID_ROOT);
        });
        ProductCategoryDO childCategory = randomPojo(ProductCategoryDO.class, o -> {
            o.setId(2L);
            o.setName("一次性医疗器械");
            o.setDictId(2001L);
            o.setParentDictId(1001L);
        });
        ProductCategoryDO grandChildCategory = randomPojo(ProductCategoryDO.class, o -> {
            o.setId(3L);
            o.setName("注射器");
            o.setDictId(3001L);
            o.setParentDictId(2001L);
        });
        productCategoryMapper.insert(rootCategory);
        productCategoryMapper.insert(childCategory);
        productCategoryMapper.insert(grandChildCategory);

        // Mock本地缓存为空
        when(localCache.getIfPresent(anyString())).thenReturn(null);

        // 调用：获取3层深度的分类树
        List<ProductCategoryRespVO> result = productCategoryService
            .getProductCategoryTreeWithChildren(ProductCategoryDO.PARENT_DICT_ID_ROOT, 3);

        // 验证：构建了完整的树结构
        assertNotNull(result);
        assertEquals(1, result.size());
        
        ProductCategoryRespVO root = result.get(0);
        assertEquals("医疗器械", root.getName());
        assertNotNull(root.getChildren());
        assertEquals(1, root.getChildren().size());
        
        ProductCategoryRespVO child = root.getChildren().get(0);
        assertEquals("一次性医疗器械", child.getName());
        assertNotNull(child.getChildren());
        assertEquals(1, child.getChildren().size());
        
        ProductCategoryRespVO grandChild = child.getChildren().get(0);
        assertEquals("注射器", grandChild.getName());
    }

    @Test
    public void testGetProductCategoryTreeWithChildren_maxDepthZero() {
        // 调用：深度为0
        List<ProductCategoryRespVO> result = productCategoryService
            .getProductCategoryTreeWithChildren(1001L, 0);

        // 验证：返回空列表
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testCreateCategory_cacheEvict() {
        // 准备参数
        ProductCategorySaveReqVO reqVO = new ProductCategorySaveReqVO();
        reqVO.setName("新分类");
        reqVO.setParentDictId(ProductCategoryDO.PARENT_DICT_ID_ROOT);
        reqVO.setDictId(9001L);
        reqVO.setSortOrder(1);

        // Mock缓存管理器
        org.springframework.cache.Cache mockCache = mock(org.springframework.cache.Cache.class);
        when(cacheManager.getCache(anyString())).thenReturn(mockCache);

        // 调用
        Long categoryId = productCategoryService.createProductCategory(reqVO);

        // 验证：分类创建成功
        assertNotNull(categoryId);
        ProductCategoryDO created = productCategoryMapper.selectById(categoryId);
        assertNotNull(created);
        assertEquals("新分类", created.getName());

        // 验证：缓存被清理
        verify(localCache).invalidate(ProductCategoryCacheConstants.LOCAL_CACHE_KEY_ROOT_CATEGORIES);
        verify(localCache).invalidate(ProductCategoryCacheConstants.LOCAL_CACHE_KEY_CHILDREN_PREFIX + 
            ProductCategoryDO.PARENT_DICT_ID_ROOT);
    }

    @Test
    public void testClearAllCategoryCache() {
        // Mock缓存管理器
        org.springframework.cache.Cache mockRedisCache = mock(org.springframework.cache.Cache.class);
        when(cacheManager.getCache(anyString())).thenReturn(mockRedisCache);

        // 调用
        productCategoryService.clearProductCategoryTreeCache();

        // 验证：本地缓存被清理
        verify(localCache).invalidateAll();
    }
}
