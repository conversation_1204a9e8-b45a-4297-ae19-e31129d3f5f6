package com.xyy.saas.inquiry.product.server.controller.admin.bpm;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xyy.saas.inquiry.product.enums.BpmBusinessTypeEnum;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmApprovePageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmApproveRespVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.bpm.BpmBusinessRelationDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.bpm.BpmBusinessRelationMapper;
import com.xyy.saas.inquiry.product.server.service.BaseIntegrationTest;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;

/**
 * 首营商品审批 Controller 集成测试
 */
@Slf4j
public class BpmApproveControllerTest extends BaseIntegrationTest {

    @Resource
    private BpmBusinessRelationMapper bpmBusinessRelationMapper;

    /**
     * GET /bpm/product-first-approve/page
     */
    @Test
    public void testProductFirstApprovePage() throws Exception {
        // 准备审批流关联业务数据（业务类型：首营商品审批）
        BpmBusinessRelationDO relation = BpmBusinessRelationDO.builder()
            .businessType(BpmBusinessTypeEnum.PRODUCT_FIRST_APPROVE.code)
            .businessPref("FIRST-TEST")
            .applicant("tester")
            .startTime(LocalDateTime.now())
            .build();
        bpmBusinessRelationMapper.insert(relation);

        BpmApprovePageReqVO pageReqVO = new BpmApprovePageReqVO();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(10);

        String resp = mockMvc.perform(get("/bpm/product-first-approve/page")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(pageReqVO)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(0))
            .andReturn()
            .getResponse()
            .getContentAsString();

        CommonResult<PageResult<BpmApproveRespVO>> result =
            objectMapper.readValue(resp,
                new TypeReference<CommonResult<PageResult<BpmApproveRespVO>>>() {});

        assert result.getCheckedData() != null;
    }
}