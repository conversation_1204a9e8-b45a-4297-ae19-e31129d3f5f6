package com.xyy.saas.inquiry.product.server.controller.admin.product;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xyy.saas.inquiry.product.api.product.dto.ProductFlag;
import com.xyy.saas.inquiry.product.api.product.dto.ProductUseInfoDto;
import com.xyy.saas.inquiry.product.api.product.dto.qualification.ProductQualificationExtInfo;
import com.xyy.saas.inquiry.product.api.product.dto.qualification.ProductQualificationInfoDto;
import com.xyy.saas.inquiry.product.server.WebMvcIntegrationTest;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductInfoPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductInfoRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductInfoSaveReqVO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import com.xyy.saas.inquiry.user.server.InquiryUserServerApplication;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.transaction.annotation.Transactional;

@ActiveProfiles("test")
@SpringBootTest(classes = InquiryUserServerApplication.class)
//@MapperScan(basePackages = {"cn.iocoder.yudao.module", "com.xyy.saas.inquiry"})
@Transactional(rollbackFor = Throwable.class)
// @AutoConfigureMockMvc
public class ProductInfoControllerTest {
    protected static final Logger log = LoggerFactory.getLogger(WebMvcIntegrationTest.class);

    @Resource
    protected MockMvc mockMvc;

    @Resource
    protected ObjectMapper objectMapper;

    @Test
    public void testAbc() {
        log.info("testAbc");
    }

    @Test
    public void testProductInfo() throws Exception {
        // 创建商品
        Long productId = createProductInfo().getCheckedData();
        assert productId != null;

        // 分页查询
        PageResult<ProductInfoRespVO> pageResult = getProductInfoPage().getCheckedData();
        assert pageResult != null;
        assert pageResult.getList() != null;
        assert !pageResult.getList().isEmpty();

        // 单个查询
        ProductInfoRespVO productInfoRespVO = getProductInfo(productId).getCheckedData();
        assert productInfoRespVO != null;

        // 更新商品
        Long productId2 = updateProductInfo(productId).getCheckedData();
        assert productId.equals(productId2);

        // 单个查询
        ProductInfoRespVO productInfoRespVO2 = getProductInfo(productId).getCheckedData();
        assert productInfoRespVO2 != null;
    }


    public CommonResult<Long> createProductInfo() throws Exception {
        // 准备参数
        ProductInfoSaveReqVO reqVO = new ProductInfoSaveReqVO()
            .setCommonName("医用防护口罩")
            .setBrandName("怡恩康")
            .setSpec("无菌折叠耳挂式（20只装）")
            .setManufacturer("河南省佳康医疗器械有限责任公司")
            .setApprovalNumber("批准文号")
            .setOrigin("河南新乡")
            .setBarcode("6974085219616")
            .setUnit("盒")
            .setDosageForm("口罩")
            .setBusinessScope("消毒器械")
            .setPresCategory("甲类OTC")
            .setInputTaxRate(new BigDecimal("13"))
            .setOutputTaxRate(new BigDecimal("13"));

        // 设置资质信息
        ProductQualificationInfoDto qualificationInfo = new ProductQualificationInfoDto()
            .setQualityStandard("XHH11223")
            .setQualityStandardStart(LocalDate.now())
            .setQualityStandardExt(new ProductQualificationExtInfo()
                .setUrls(List.of("https://example.com/qualification.pdf")));
        reqVO.setQualificationInfo(qualificationInfo);

        // 设置价格信息
        ProductUseInfoDto useInfo = new ProductUseInfoDto();
        useInfo.setRetailPrice(new BigDecimal("100"));
        useInfo.setMemberPrice(new BigDecimal("90"));
        reqVO.setUseInfo(useInfo);

        // 设置商品标志
        ProductFlag productFlag = new ProductFlag()
            .setImported(true);
        reqVO.setProductFlag(productFlag);


        // 调用接口, 获取返回result
        String resp = mockMvc.perform(post("/admin-api/product/info/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(reqVO)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(0))
            .andReturn().getResponse().getContentAsString();

        return objectMapper.readValue(resp, new TypeReference<CommonResult<Long>>() {});
    }

    public CommonResult<Long> updateProductInfo(Long productId) throws Exception {
        // 准备参数
        ProductInfoSaveReqVO reqVO = new ProductInfoSaveReqVO()
            .setId(productId)
            .setCommonName("测试商品1")
            .setBrandName("测试品牌2")
            .setSpec("规格型号")
            .setManufacturer("生产厂家")
            .setApprovalNumber("批准文号")
            .setUnit("袋")
            .setDosageForm("胶囊")
            .setBusinessScope("医疗器械")
            .setPresCategory("乙类OTC")
            .setInputTaxRate(new BigDecimal("11"))
            .setOutputTaxRate(new BigDecimal("9"));

        // 设置资质信息
        ProductQualificationInfoDto qualificationInfo = new ProductQualificationInfoDto()
            .setQualityStandard("XHH11223")
            .setQualityStandardStart(LocalDate.now())
            .setQualityStandardExt(new ProductQualificationExtInfo()
                .setUrls(List.of("https://example.com/qualification.pdf")));
        reqVO.setQualificationInfo(qualificationInfo);

        // 设置价格信息
        ProductUseInfoDto useInfo = new ProductUseInfoDto()
            .setRetailPrice(new BigDecimal("10"))
            .setMemberPrice(new BigDecimal("9"));
        reqVO.setUseInfo(useInfo);

        // 设置商品标志
        ProductFlag productFlag = new ProductFlag()
            .setImported(false);
        reqVO.setProductFlag(productFlag);


        // 调用接口, 获取返回result
        String resp = mockMvc.perform(post("/admin-api/product/info/update")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(reqVO)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(0))
            .andDo(MockMvcResultHandlers.print())
            .andReturn().getResponse().getContentAsString();

        return objectMapper.readValue(resp, new TypeReference<CommonResult<Long>>() {});
    }

    public CommonResult<PageResult<ProductInfoRespVO>> getProductInfoPage() throws Exception {
        // 准备参数
        ProductInfoPageReqVO pageReqVO = new ProductInfoPageReqVO();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(10);

        // 调用接口, 获取返回result
        String resp = mockMvc.perform(get("/admin-api/product/info/page")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(pageReqVO)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(0))
            .andReturn().getResponse().getContentAsString();

        return objectMapper.readValue(resp, new TypeReference<CommonResult<PageResult<ProductInfoRespVO>>>() {});
    }

    public CommonResult<ProductInfoRespVO> getProductInfo(Long productId) throws Exception {
        // 调用接口, 获取返回result
        String resp = mockMvc.perform(get("/admin-api/product/info/get")
                .param("id", String.valueOf(productId)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(0))
            .andExpect(jsonPath("$.data.commonName").value("测试商品"))
            .andReturn().getResponse().getContentAsString();

        return objectMapper.readValue(resp, new TypeReference<CommonResult<ProductInfoRespVO>>() {});
    }

} 