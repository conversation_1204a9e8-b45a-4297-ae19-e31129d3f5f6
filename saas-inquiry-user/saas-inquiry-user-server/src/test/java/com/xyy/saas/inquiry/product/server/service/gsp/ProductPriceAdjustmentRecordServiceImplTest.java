package com.xyy.saas.inquiry.product.server.service.gsp;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_PRICE_ADJUSTMENT_RECORD_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductPriceAdjustmentRecordPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductPriceAdjustmentRecordSaveReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductPriceAdjustmentRecordDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.gsp.ProductPriceAdjustmentRecordMapper;
import com.xyy.saas.inquiry.product.server.service.BaseIntegrationTest;
import com.xyy.saas.inquiry.product.server.service.gsp.ProductPriceAdjustmentRecordServiceImpl;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

/**
 * {@link ProductPriceAdjustmentRecordServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(ProductPriceAdjustmentRecordServiceImpl.class)
public class ProductPriceAdjustmentRecordServiceImplTest extends BaseIntegrationTest {

    @Resource
    private ProductPriceAdjustmentRecordServiceImpl productPriceAdjustmentRecordService;

    @Resource
    private ProductPriceAdjustmentRecordMapper productPriceAdjustmentRecordMapper;

    @Test
    public void testCreateProductPriceAdjustmentRecord_success() {
        // 准备参数
        ProductPriceAdjustmentRecordSaveReqVO createReqVO = randomPojo(ProductPriceAdjustmentRecordSaveReqVO.class).setId(null);

        // 调用
        Long productPriceAdjustmentRecordId = productPriceAdjustmentRecordService.saveOrUpdatePriceAdjustment(createReqVO);
        // 断言
        assertNotNull(productPriceAdjustmentRecordId);
        // 校验记录的属性是否正确
        ProductPriceAdjustmentRecordDO productPriceAdjustmentRecord = productPriceAdjustmentRecordMapper.selectById(productPriceAdjustmentRecordId);
        assertPojoEquals(createReqVO, productPriceAdjustmentRecord, "id");
    }

    @Test
    public void testUpdateProductPriceAdjustmentRecord_success() {
        // mock 数据
        ProductPriceAdjustmentRecordDO dbProductPriceAdjustmentRecord = randomPojo(ProductPriceAdjustmentRecordDO.class);
        productPriceAdjustmentRecordMapper.insert(dbProductPriceAdjustmentRecord);// @Sql: 先插入出一条存在的数据
        // 准备参数
        ProductPriceAdjustmentRecordSaveReqVO updateReqVO = randomPojo(ProductPriceAdjustmentRecordSaveReqVO.class, o -> {
            o.setId(dbProductPriceAdjustmentRecord.getId()); // 设置更新的 ID
        });

        // 调用
        productPriceAdjustmentRecordService.saveOrUpdatePriceAdjustment(updateReqVO);
        // 校验是否更新正确
        ProductPriceAdjustmentRecordDO productPriceAdjustmentRecord = productPriceAdjustmentRecordMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, productPriceAdjustmentRecord);
    }

    @Test
    public void testUpdateProductPriceAdjustmentRecord_notExists() {
        // 准备参数
        ProductPriceAdjustmentRecordSaveReqVO updateReqVO = randomPojo(ProductPriceAdjustmentRecordSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> productPriceAdjustmentRecordService.saveOrUpdatePriceAdjustment(updateReqVO), PRODUCT_PRICE_ADJUSTMENT_RECORD_NOT_EXISTS);
    }

    @Test
    public void testDeleteProductPriceAdjustmentRecord_success() {
        // mock 数据
        ProductPriceAdjustmentRecordDO dbProductPriceAdjustmentRecord = randomPojo(ProductPriceAdjustmentRecordDO.class);
        productPriceAdjustmentRecordMapper.insert(dbProductPriceAdjustmentRecord);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbProductPriceAdjustmentRecord.getId();

        // 调用
        productPriceAdjustmentRecordService.deleteProductPriceAdjustmentRecord(id);
       // 校验数据不存在了
       assertNull(productPriceAdjustmentRecordMapper.selectById(id));
    }

    @Test
    public void testDeleteProductPriceAdjustmentRecord_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> productPriceAdjustmentRecordService.deleteProductPriceAdjustmentRecord(id), PRODUCT_PRICE_ADJUSTMENT_RECORD_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetProductPriceAdjustmentRecordPage() {
       // mock 数据
       ProductPriceAdjustmentRecordDO dbProductPriceAdjustmentRecord = randomPojo(ProductPriceAdjustmentRecordDO.class, o -> { // 等会查询到
           o.setPref(null);
           o.setApplicableTenantIds(null);
           o.setAdjustmentReason(null);
           o.setApprovalStatus(null);
           o.setRemark(null);
           o.setCreateTime(null);
       });
       productPriceAdjustmentRecordMapper.insert(dbProductPriceAdjustmentRecord);
       // 测试 billNo 不匹配
       productPriceAdjustmentRecordMapper.insert(cloneIgnoreId(dbProductPriceAdjustmentRecord, o -> o.setPref(null)));
       // 测试 applicableTenantIds 不匹配
       productPriceAdjustmentRecordMapper.insert(cloneIgnoreId(dbProductPriceAdjustmentRecord, o -> o.setApplicableTenantIds(null)));
       // 测试 adjustmentReason 不匹配
       productPriceAdjustmentRecordMapper.insert(cloneIgnoreId(dbProductPriceAdjustmentRecord, o -> o.setAdjustmentReason(null)));
       // 测试 approvalStatus 不匹配
       productPriceAdjustmentRecordMapper.insert(cloneIgnoreId(dbProductPriceAdjustmentRecord, o -> o.setApprovalStatus(null)));
       // 测试 remark 不匹配
       productPriceAdjustmentRecordMapper.insert(cloneIgnoreId(dbProductPriceAdjustmentRecord, o -> o.setRemark(null)));
       // 测试 createTime 不匹配
       productPriceAdjustmentRecordMapper.insert(cloneIgnoreId(dbProductPriceAdjustmentRecord, o -> o.setCreateTime(null)));
       // 准备参数
       ProductPriceAdjustmentRecordPageReqVO reqVO = new ProductPriceAdjustmentRecordPageReqVO();
       reqVO.setPref(null);
       reqVO.setApplicableTenantIdList(null);
       reqVO.setApprovalStatus(null);
       reqVO.setRemark(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<ProductPriceAdjustmentRecordDO> pageResult = productPriceAdjustmentRecordService.getProductPriceAdjustmentRecordPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbProductPriceAdjustmentRecord, pageResult.getList().get(0));
    }

}