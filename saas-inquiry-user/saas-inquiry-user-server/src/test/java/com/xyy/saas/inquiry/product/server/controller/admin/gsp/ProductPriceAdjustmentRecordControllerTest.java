// package com.xyy.saas.inquiry.product.server.controller.admin.gsp;
//
// import cn.iocoder.yudao.framework.common.pojo.CommonResult;
// import cn.iocoder.yudao.framework.common.pojo.PageParam;
// import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
// import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.*;
// import org.junit.jupiter.api.Test;
// import org.springframework.http.MediaType;
// import org.springframework.security.test.context.support.WithMockUser;
//
// import javax.annotation.Resource;
// import java.math.BigDecimal;
// import java.util.ArrayList;
// import java.util.Arrays;
// import java.util.List;
//
// import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
// import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
// import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
//
// @WithMockUser(username = "admin", authorities = {"saas:product:price-adjustment:create","saas:product:price-adjustment:query"})
// public class ProductPriceAdjustmentRecordControllerTest extends BaseDbUnitTest {
//
//     @Resource
//     private ProductPriceAdjustmentRecordController controller;
//
//     @Test
//     public void testCreatePriceAdjustment() throws Exception {
//         // 准备参数
//         ProductPriceAdjustmentRecordSaveReqVO reqVO = new ProductPriceAdjustmentRecordSaveReqVO();
//         reqVO.setAdjustmentReason("测试调价");
//         reqVO.setApplicableTenantIdList(Arrays.asList(1L, 2L));
//
//         // 准备明细
//         List<ProductPriceAdjustmentDetailSaveReqVO> details = new ArrayList<>();
//         ProductPriceAdjustmentDetailSaveReqVO detail = new ProductPriceAdjustmentDetailSaveReqVO();
//         detail.setProductId(1L);
//         detail.setOldRetailPrice(new BigDecimal("100"));
//         detail.setOldMemberPrice(new BigDecimal("90"));
//         detail.setNewRetailPrice(new BigDecimal("110"));
//         detail.setNewMemberPrice(new BigDecimal("100"));
//         details.add(detail);
//         reqVO.setDetails(details);
//
//         // 调用接口
//         mockMvc.perform(post("/product/price-adjustment/save")
//                 .contentType(MediaType.APPLICATION_JSON)
//                 .content(objectMapper.writeValueAsString(reqVO)))
//                 .andExpect(status().isOk())
//                 .andExpect(jsonPath("$.code").value(0));
//     }
//
//     @Test
//     public void testGetPriceAdjustmentPage() throws Exception {
//         // 准备参数
//         ProductPriceAdjustmentRecordPageReqVO pageReqVO = new ProductPriceAdjustmentRecordPageReqVO();
//         pageReqVO.setPageNo(1);
//         pageReqVO.setPageSize(10);
//
//         // 调用接口
//         mockMvc.perform(get("/product/price-adjustment/page")
//                 .contentType(MediaType.APPLICATION_JSON)
//                 .content(objectMapper.writeValueAsString(pageReqVO)))
//                 .andExpect(status().isOk())
//                 .andExpect(jsonPath("$.code").value(0));
//     }
//
//     @Test
//     public void testGetPriceAdjustment() throws Exception {
//         // 先创建售价调整单
//         Long recordId = createPriceAdjustment();
//
//         // 调用接口
//         mockMvc.perform(get("/product/price-adjustment/get")
//                 .param("id", String.valueOf(recordId)))
//                 .andExpect(status().isOk())
//                 .andExpect(jsonPath("$.code").value(0));
//     }
//
//     @Test
//     public void testGetPriceAdjustmentDetailPage() throws Exception {
//         // 先创建售价调整单
//         Long recordId = createPriceAdjustment();
//
//         // 准备参数
//         PageParam pageParam = new PageParam();
//         pageParam.setPageNo(1);
//         pageParam.setPageSize(10);
//
//         // 调用接口
//         mockMvc.perform(get("/product/price-adjustment/product-price-adjustment-detail/page")
//                 .param("recordId", String.valueOf(recordId))
//                 .contentType(MediaType.APPLICATION_JSON)
//                 .content(objectMapper.writeValueAsString(pageParam)))
//                 .andExpect(status().isOk())
//                 .andExpect(jsonPath("$.code").value(0));
//     }
//
//     private Long createPriceAdjustment() {
//         ProductPriceAdjustmentRecordSaveReqVO reqVO = new ProductPriceAdjustmentRecordSaveReqVO();
//         // 设置必要参数
//         reqVO.setAdjustmentReason("测试调价");
//         reqVO.setApplicableTenantIdList(Arrays.asList(1L));
//         List<ProductPriceAdjustmentDetailSaveReqVO> details = new ArrayList<>();
//         // ... 设置明细
//         reqVO.setDetails(details);
//         CommonResult<Long> result = controller.submitPriceAdjustment(reqVO);
//         return result.getData();
//     }
// }