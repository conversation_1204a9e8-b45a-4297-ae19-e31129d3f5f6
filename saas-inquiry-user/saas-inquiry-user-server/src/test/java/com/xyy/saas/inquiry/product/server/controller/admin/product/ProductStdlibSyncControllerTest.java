package com.xyy.saas.inquiry.product.server.controller.admin.product;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibSyncDto;
import com.xyy.saas.inquiry.product.server.service.BaseIntegrationTest;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;

@Slf4j
public class ProductStdlibSyncControllerTest extends BaseIntegrationTest {

    // private static WebTestClient webClient;
    // private static MockMvc mockMvc;
    // private static String token;
    //
    // @BeforeAll
    // public static void setUp() {
    //     log.info("setUp");
    //     mockMvc = MockMvcBuilders.standaloneSetup(new ProductStdlibSyncController()).build();
    //     webClient = WebTestClient.bindToServer().baseUrl("http://localhost:48080/admin-api").build();
    //
    //
    // }

    @Test
    public void testFullSyncFromMid() throws Exception {
        startFullSync();
    }

    private ProductStdlibSyncDto startFullSync() throws Exception {
        // 调用接口, 获取返回result
        CommonResult<?> resp = webClient.get().uri("/product/stdlib/sync/full")
            // .header("Authorization", )
            .header("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .exchange()
            .returnResult(CommonResult.class).getResponseBody().blockFirst();
        assertNotNull(resp);
        log.info("resp:{}", JsonUtils.toJsonPrettyString(resp));
        assertEquals(0, resp.getCode());
        assertNotNull(resp.getData());

        return (ProductStdlibSyncDto) resp.getData();
    }

    @Test
    public void testCancelSync() throws Exception {
        // 先创建一个同步任务
        ProductStdlibSyncDto progress = startFullSync();
        assertNotNull(progress);

        // 调用接口, 获取返回result
        String resp = mockMvc.perform(post("/product/stdlib/sync/cancel")
                .param("guid", progress.getGuid())
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(0))
            .andDo(MockMvcResultHandlers.print())
            .andReturn().getResponse().getContentAsString();

        CommonResult<Boolean> result = JsonUtils.parseObject(resp, new TypeReference<>() {});
        assertNotNull(result.getData());
    }

    @Test
    public void testGetSyncProgress() throws Exception {
        // 先创建一个同步任务
        ProductStdlibSyncDto progress = startFullSync();
        assertNotNull(progress);

        // 调用接口, 获取返回result
        String resp = mockMvc.perform(get("/product/stdlib/sync/progress")
                .param("guid", progress.getGuid())
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(0))
            .andDo(MockMvcResultHandlers.print())
            .andReturn().getResponse().getContentAsString();

        CommonResult<ProductStdlibSyncDto> result = JsonUtils.parseObject(resp, new TypeReference<>() {});
        assertNotNull(result.getData());
    }

    @Test
    public void testGetSyncProgressList() throws Exception {
        // 调用接口, 获取返回result
        String resp = mockMvc.perform(get("/product/stdlib/sync/progress/list")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(0))
            .andDo(MockMvcResultHandlers.print())
            .andReturn().getResponse().getContentAsString();

        CommonResult<List<ProductStdlibSyncDto>> result = JsonUtils.parseObject(resp, new TypeReference<>() {});
        assertNotNull(result.getData());
    }
} 