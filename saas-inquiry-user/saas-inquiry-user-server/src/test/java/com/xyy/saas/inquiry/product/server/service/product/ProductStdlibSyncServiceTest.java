package com.xyy.saas.inquiry.product.server.service.product;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibSyncDto;
import com.xyy.saas.inquiry.product.server.config.properties.ProductStdlibSyncProperties;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibSyncDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductStdlibSyncMapper;
import com.xyy.saas.inquiry.product.server.enums.StdlibSyncStatusEnum;
import com.xyy.saas.inquiry.product.server.enums.StdlibSyncTypeEnum;
import com.xyy.saas.inquiry.product.server.service.BaseIntegrationTest;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.Rollback;

@Import({ProductStdlibSyncServiceImpl.class})
class ProductStdlibSyncServiceTest extends BaseIntegrationTest {

    @Resource
    private ProductStdlibSyncService stdlibSyncService;

    @Resource
    private ProductStdlibSyncMapper stdlibSyncMapper;

    @MockBean
    private ProductStdlibService stdlibService;

    @MockBean
    private ProductStdlibSyncProperties stdlibSyncProperties;

    @Test
    @Rollback
    void testStartFullSync_whenDisabled() {
        // 1. 配置Mock - 禁用同步
        when(stdlibSyncProperties.isEnableFullSync()).thenReturn(false);

        // 2. 调用开始同步
        ProductStdlibSyncDto progress = stdlibSyncService.startFullSync();
        
        // 3. 验证结果
        assertNull(progress);
    }

    @Test
    @Rollback
    void testStartFullSync_whenRunningTaskExists() {
        // 1. 配置Mock
        when(stdlibSyncProperties.isEnableFullSync()).thenReturn(true);
        
        // 2. 插入一个运行中的任务
        ProductStdlibSyncDO runningTask = new ProductStdlibSyncDO()
            .setGuid(UUID.randomUUID().toString())
            .setType(StdlibSyncTypeEnum.FULL.getCode())
            .setStatus(StdlibSyncStatusEnum.RUNNING.getCode())
            .setStartId(1L)
            .setEndId(2000L)
            .setCurrent(500L)
            .setStartTime(LocalDateTime.now());
        stdlibSyncMapper.insert(runningTask);

        // 3. 调用开始同步
        ProductStdlibSyncDto progress = stdlibSyncService.startFullSync();
        
        // 4. 验证结果
        assertNotNull(progress);
        assertEquals(runningTask.getGuid(), progress.getGuid());
        assertEquals(StdlibSyncStatusEnum.RUNNING.getCode(), progress.getStatus());
    }

    @Test
    @Rollback
    void testStartFullSync_success() {
        // 1. 配置Mock
        when(stdlibSyncProperties.isEnableFullSync()).thenReturn(true);
        when(stdlibSyncProperties.getFullSyncStartId()).thenReturn(1L);
        when(stdlibSyncProperties.getFullSyncEndId()).thenReturn(1000L);

        // 2. 调用开始同步
        ProductStdlibSyncDto progress = stdlibSyncService.startFullSync();
        
        // 3. 验证结果
        assertNotNull(progress);
        assertEquals(StdlibSyncTypeEnum.FULL.getCode(), progress.getType());
        assertEquals(StdlibSyncStatusEnum.NOT_START.getCode(), progress.getStatus());
        assertEquals(1000L, progress.getEndId());
        assertEquals(0L, progress.getCurrent());
        assertNotNull(progress.getStartTime());
        assertNotNull(progress.getCreateTime());
    }

    @Test
    @Rollback
    void testSyncWithCompleteProgress() {
        // 1. 配置Mock
        when(stdlibSyncProperties.getFullSyncStartId()).thenReturn(1L);
        when(stdlibSyncProperties.getFullSyncEndId()).thenReturn(100L);
        when(stdlibSyncProperties.getFullSyncIdBatchSize()).thenReturn(10);
        when(stdlibSyncProperties.getFullSyncInterval()).thenReturn(0L);
        when(stdlibService.saveOrUpdateStdlibFromMid(anyList(), eq(true)))
            .thenReturn(List.of(new ProductStdlibDO()));

        // 2. 创建同步任务
        String guid = UUID.randomUUID().toString();
        ProductStdlibSyncDO task = new ProductStdlibSyncDO()
            .setGuid(guid)
            .setType(StdlibSyncTypeEnum.FULL.getCode())
            .setStatus(StdlibSyncStatusEnum.NOT_START.getCode())
            .setStartId(1L)
            .setEndId(100L)
            .setCurrent(0L)
            .setStartTime(LocalDateTime.now());
        stdlibSyncMapper.insert(task);

        // 3. 执行同步
        boolean success = stdlibSyncService.syncWithCompleteProgress(guid);
        
        // 4. 验证结果
        assertTrue(success);
        ProductStdlibSyncDO updatedTask = stdlibSyncMapper.selectByGuid(guid);
        assertEquals(StdlibSyncStatusEnum.COMPLETED.getCode(), updatedTask.getStatus());
        assertEquals(100L, updatedTask.getCurrent());
        assertNotNull(updatedTask.getEndTime());
    }

    @Test
    @Rollback
    void testCancelSync() {
        // 1. 创建运行中的任务
        String guid = UUID.randomUUID().toString();
        ProductStdlibSyncDO task = new ProductStdlibSyncDO()
            .setGuid(guid)
            .setType(StdlibSyncTypeEnum.FULL.getCode())
            .setStatus(StdlibSyncStatusEnum.RUNNING.getCode())
            .setStartId(1L)
            .setEndId(1500L)
            .setCurrent(50L)
            .setStartTime(LocalDateTime.now());
        stdlibSyncMapper.insert(task);

        // 2. 取消任务
        boolean cancelled = stdlibSyncService.cancelSync(guid);
        
        // 3. 验证结果
        assertTrue(cancelled);
        ProductStdlibSyncDO updatedTask = stdlibSyncMapper.selectByGuid(guid);
        assertEquals(StdlibSyncStatusEnum.CANCELLED.getCode(), updatedTask.getStatus());
    }

    @Test
    @Rollback
    void testRunNotStartFullSyncTasks() {
        // 1. 配置Mock
        when(stdlibSyncProperties.getFullSyncStartId()).thenReturn(1L);
        when(stdlibSyncProperties.getFullSyncEndId()).thenReturn(100L);
        when(stdlibSyncProperties.getFullSyncIdBatchSize()).thenReturn(10);
        when(stdlibService.saveOrUpdateStdlibFromMid(anyList(), eq(true)))
            .thenReturn(List.of(new ProductStdlibDO()));

        // 2. 创建未开始的任务
        String guid = UUID.randomUUID().toString();
        ProductStdlibSyncDO task = new ProductStdlibSyncDO()
            .setGuid(guid)
            .setType(StdlibSyncTypeEnum.FULL.getCode())
            .setStatus(StdlibSyncStatusEnum.NOT_START.getCode())
            .setStartId(1L)
            .setEndId(100L)
            .setCurrent(0L)
            .setStartTime(LocalDateTime.now());
        stdlibSyncMapper.insert(task);

        // 3. 执行未开始的任务
        List<ProductStdlibSyncDO> results = stdlibSyncService.runNotStartFullSyncTasks();
        
        // 4. 验证结果
        assertFalse(results.isEmpty());
        assertEquals(guid, results.getFirst().getGuid());
        ProductStdlibSyncDO updatedTask = stdlibSyncMapper.selectByGuid(guid);
        assertEquals(StdlibSyncStatusEnum.COMPLETED.getCode(), updatedTask.getStatus());
    }
} 