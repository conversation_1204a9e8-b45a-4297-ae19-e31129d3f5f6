package com.xyy.saas.inquiry.product.server.integration;

import cn.iocoder.yudao.module.system.BaseIntegrationTest;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductPriceUpdateReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.productcategory.vo.ProductCategoryRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.productcategory.vo.ProductCategorySaveReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductInfoDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductUseInfoDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.productcategory.ProductCategoryDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductInfoMapper;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductUseInfoMapper;
import com.xyy.saas.inquiry.product.server.dal.mysql.productcategory.ProductCategoryMapper;
import com.xyy.saas.inquiry.product.server.service.product.ProductInfoService;
import com.xyy.saas.inquiry.product.server.service.productcategory.ProductCategoryService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 商品价格修改和分类缓存功能的集成测试
 * 
 * <AUTHOR>
 */
@Import({
    com.xyy.saas.inquiry.product.server.service.product.ProductInfoServiceImpl.class,
    com.xyy.saas.inquiry.product.server.service.productcategory.ProductCategoryServiceImpl.class,
    com.xyy.saas.inquiry.product.server.config.ProductCategoryCacheConfig.class
})
public class ProductPriceCacheIntegrationTest extends BaseIntegrationTest {

    @Resource
    private ProductInfoService productInfoService;

    @Resource
    private ProductCategoryService productCategoryService;

    @Resource
    private ProductInfoMapper productInfoMapper;

    @Resource
    private ProductUseInfoMapper productUseInfoMapper;

    @Resource
    private ProductCategoryMapper productCategoryMapper;

    @Test
    public void testProductPriceUpdate_fullWorkflow() {
        // 1. 准备商品数据
        ProductInfoDO productInfo = randomPojo(ProductInfoDO.class, o -> {
            o.setId(1L);
            o.setPref("INTEGRATION_PROD_001");
            o.setTenantId(1000L);
            o.setCommonName("集成测试商品");
        });
        productInfoMapper.insert(productInfo);

        // 2. 准备多租户使用信息
        ProductUseInfoDO useInfo1 = randomPojo(ProductUseInfoDO.class, o -> {
            o.setProductPref("INTEGRATION_PROD_001");
            o.setTenantId(1000L); // 总部
            o.setHeadTenantId(1000L);
            o.setRetailPrice(new BigDecimal("100.00"));
            o.setMemberPrice(new BigDecimal("90.00"));
        });
        ProductUseInfoDO useInfo2 = randomPojo(ProductUseInfoDO.class, o -> {
            o.setProductPref("INTEGRATION_PROD_001");
            o.setTenantId(1001L); // 门店1
            o.setHeadTenantId(1000L);
            o.setRetailPrice(new BigDecimal("105.00"));
            o.setMemberPrice(new BigDecimal("95.00"));
        });
        productUseInfoMapper.insert(useInfo1);
        productUseInfoMapper.insert(useInfo2);

        // 3. 执行价格修改
        ProductPriceUpdateReqVO priceUpdateReq = new ProductPriceUpdateReqVO();
        priceUpdateReq.setId(1L);
        priceUpdateReq.setRetailPrice(new BigDecimal("150.00"));
        priceUpdateReq.setMemberPrice(new BigDecimal("140.00"));
        priceUpdateReq.setTargetTenantIdList(List.of(1000L, 1001L));

        // 调用价格修改服务
        assertDoesNotThrow(() -> productInfoService.updateProductPrice(priceUpdateReq));

        // 4. 验证价格修改结果
        List<ProductUseInfoDO> updatedUseInfoList = productUseInfoMapper.selectList(
            List.of(1000L, 1001L), List.of("INTEGRATION_PROD_001"));
        
        assertEquals(2, updatedUseInfoList.size());
        for (ProductUseInfoDO updatedUseInfo : updatedUseInfoList) {
            assertEquals(new BigDecimal("150.00"), updatedUseInfo.getRetailPrice());
            assertEquals(new BigDecimal("140.00"), updatedUseInfo.getMemberPrice());
        }
    }

    @Test
    public void testProductCategoryCache_fullWorkflow() {
        // 1. 创建分类层级结构
        ProductCategoryDO rootCategory = randomPojo(ProductCategoryDO.class, o -> {
            o.setId(null);
            o.setName("集成测试根分类");
            o.setDictId(9001L);
            o.setParentDictId(ProductCategoryDO.PARENT_DICT_ID_ROOT);
            o.setSortOrder(1);
            o.setUpdateTime(LocalDateTime.now());
        });
        productCategoryMapper.insert(rootCategory);

        ProductCategoryDO childCategory = randomPojo(ProductCategoryDO.class, o -> {
            o.setId(null);
            o.setName("集成测试子分类");
            o.setDictId(9002L);
            o.setParentDictId(9001L);
            o.setSortOrder(1);
            o.setUpdateTime(LocalDateTime.now());
        });
        productCategoryMapper.insert(childCategory);

        // 2. 测试获取根分类
        List<ProductCategoryRespVO> rootCategories = productCategoryService.getRootCategories();
        assertNotNull(rootCategories);
        assertTrue(rootCategories.stream().anyMatch(c -> "集成测试根分类".equals(c.getName())));

        // 3. 测试获取子分类
        List<ProductCategoryRespVO> childCategories = productCategoryService.getChildCategories(9001L);
        assertNotNull(childCategories);
        assertEquals(1, childCategories.size());
        assertEquals("集成测试子分类", childCategories.get(0).getName());

        // 4. 测试获取分类详情
        ProductCategoryRespVO categoryDetail = productCategoryService.getCategoryDetail(9001L);
        assertNotNull(categoryDetail);
        assertEquals("集成测试根分类", categoryDetail.getName());

        // 5. 测试构建分类树
        List<ProductCategoryRespVO> categoryTree = productCategoryService
            .getProductCategoryTreeWithChildren(ProductCategoryDO.PARENT_DICT_ID_ROOT, 2);
        assertNotNull(categoryTree);
        
        // 查找我们创建的根分类
        ProductCategoryRespVO testRootCategory = categoryTree.stream()
            .filter(c -> "集成测试根分类".equals(c.getName()))
            .findFirst()
            .orElse(null);
        assertNotNull(testRootCategory);
        assertNotNull(testRootCategory.getChildren());
        assertEquals(1, testRootCategory.getChildren().size());
        assertEquals("集成测试子分类", testRootCategory.getChildren().get(0).getName());
    }

    @Test
    public void testCategoryCacheEviction_workflow() {
        // 1. 创建初始分类
        ProductCategorySaveReqVO createReq = new ProductCategorySaveReqVO();
        createReq.setName("缓存测试分类");
        createReq.setParentDictId(ProductCategoryDO.PARENT_DICT_ID_ROOT);
        createReq.setDictId(9003L);
        createReq.setSortOrder(1);

        Long categoryId = productCategoryService.createProductCategory(createReq);
        assertNotNull(categoryId);

        // 2. 第一次获取根分类（会缓存）
        List<ProductCategoryRespVO> rootCategories1 = productCategoryService.getRootCategories();
        assertTrue(rootCategories1.stream().anyMatch(c -> "缓存测试分类".equals(c.getName())));

        // 3. 修改分类（会清除缓存）
        ProductCategorySaveReqVO updateReq = new ProductCategorySaveReqVO();
        updateReq.setId(categoryId);
        updateReq.setName("缓存测试分类_修改");
        updateReq.setParentDictId(ProductCategoryDO.PARENT_DICT_ID_ROOT);
        updateReq.setDictId(9003L);
        updateReq.setSortOrder(1);

        int updateResult = productCategoryService.updateProductCategory(updateReq);
        assertEquals(1, updateResult);

        // 4. 再次获取根分类（应该从数据库重新加载）
        List<ProductCategoryRespVO> rootCategories2 = productCategoryService.getRootCategories();
        assertTrue(rootCategories2.stream().anyMatch(c -> "缓存测试分类_修改".equals(c.getName())));
        assertFalse(rootCategories2.stream().anyMatch(c -> "缓存测试分类".equals(c.getName())));

        // 5. 删除分类（会清除缓存）
        productCategoryService.deleteProductCategory(categoryId);

        // 6. 最后获取根分类（应该不包含已删除的分类）
        List<ProductCategoryRespVO> rootCategories3 = productCategoryService.getRootCategories();
        assertFalse(rootCategories3.stream().anyMatch(c -> "缓存测试分类_修改".equals(c.getName())));
    }

    @Test
    public void testProductPriceUpdate_withNonExistentUseInfo() {
        // 1. 准备商品数据（没有使用信息）
        ProductInfoDO productInfo = randomPojo(ProductInfoDO.class, o -> {
            o.setId(2L);
            o.setPref("INTEGRATION_PROD_002");
            o.setTenantId(2000L);
            o.setCommonName("无使用信息商品");
        });
        productInfoMapper.insert(productInfo);

        // 2. 执行价格修改（应该创建默认使用信息）
        ProductPriceUpdateReqVO priceUpdateReq = new ProductPriceUpdateReqVO();
        priceUpdateReq.setId(2L);
        priceUpdateReq.setRetailPrice(new BigDecimal("200.00"));
        priceUpdateReq.setMemberPrice(new BigDecimal("180.00"));
        priceUpdateReq.setTargetTenantIdList(List.of(2000L));

        // 调用价格修改服务
        assertDoesNotThrow(() -> productInfoService.updateProductPrice(priceUpdateReq));

        // 3. 验证创建了默认使用信息
        List<ProductUseInfoDO> useInfoList = productUseInfoMapper.selectList(
            List.of(2000L), List.of("INTEGRATION_PROD_002"));
        
        assertEquals(1, useInfoList.size());
        ProductUseInfoDO createdUseInfo = useInfoList.get(0);
        assertEquals(new BigDecimal("200.00"), createdUseInfo.getRetailPrice());
        assertEquals(new BigDecimal("180.00"), createdUseInfo.getMemberPrice());
        assertEquals("INTEGRATION_PROD_002", createdUseInfo.getProductPref());
        assertEquals(2000L, createdUseInfo.getTenantId());
        assertEquals(2000L, createdUseInfo.getHeadTenantId());
    }

    @Test
    public void testCategoryHierarchy_deepLevel() {
        // 1. 创建深层级分类结构（4级）
        ProductCategoryDO level1 = createAndInsertCategory("一级分类", 8001L, ProductCategoryDO.PARENT_DICT_ID_ROOT);
        ProductCategoryDO level2 = createAndInsertCategory("二级分类", 8002L, 8001L);
        ProductCategoryDO level3 = createAndInsertCategory("三级分类", 8003L, 8002L);
        ProductCategoryDO level4 = createAndInsertCategory("四级分类", 8004L, 8003L);

        // 2. 测试不同深度的分类树构建
        
        // 深度1：只有一级分类
        List<ProductCategoryRespVO> tree1 = productCategoryService
            .getProductCategoryTreeWithChildren(ProductCategoryDO.PARENT_DICT_ID_ROOT, 1);
        ProductCategoryRespVO level1Result = findCategoryByName(tree1, "一级分类");
        assertNotNull(level1Result);
        assertNull(level1Result.getChildren()); // 深度1，没有子分类

        // 深度2：包含二级分类
        List<ProductCategoryRespVO> tree2 = productCategoryService
            .getProductCategoryTreeWithChildren(ProductCategoryDO.PARENT_DICT_ID_ROOT, 2);
        ProductCategoryRespVO level1Result2 = findCategoryByName(tree2, "一级分类");
        assertNotNull(level1Result2);
        assertNotNull(level1Result2.getChildren());
        assertEquals(1, level1Result2.getChildren().size());
        assertEquals("二级分类", level1Result2.getChildren().get(0).getName());

        // 深度4：包含所有层级
        List<ProductCategoryRespVO> tree4 = productCategoryService
            .getProductCategoryTreeWithChildren(ProductCategoryDO.PARENT_DICT_ID_ROOT, 4);
        ProductCategoryRespVO level1Result4 = findCategoryByName(tree4, "一级分类");
        assertNotNull(level1Result4);
        
        // 验证完整的4级结构
        ProductCategoryRespVO level2Result = level1Result4.getChildren().get(0);
        assertEquals("二级分类", level2Result.getName());
        
        ProductCategoryRespVO level3Result = level2Result.getChildren().get(0);
        assertEquals("三级分类", level3Result.getName());
        
        ProductCategoryRespVO level4Result = level3Result.getChildren().get(0);
        assertEquals("四级分类", level4Result.getName());
    }

    private ProductCategoryDO createAndInsertCategory(String name, Long dictId, Long parentDictId) {
        ProductCategoryDO category = randomPojo(ProductCategoryDO.class, o -> {
            o.setId(null);
            o.setName(name);
            o.setDictId(dictId);
            o.setParentDictId(parentDictId);
            o.setSortOrder(1);
            o.setUpdateTime(LocalDateTime.now());
        });
        productCategoryMapper.insert(category);
        return category;
    }

    private ProductCategoryRespVO findCategoryByName(List<ProductCategoryRespVO> categories, String name) {
        return categories.stream()
            .filter(c -> name.equals(c.getName()))
            .findFirst()
            .orElse(null);
    }
}
