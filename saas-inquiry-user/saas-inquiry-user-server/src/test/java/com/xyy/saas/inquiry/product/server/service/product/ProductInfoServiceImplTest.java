package com.xyy.saas.inquiry.product.server.service.product;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_INFO_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductInfoPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductInfoSaveReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductInfoDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductInfoMapper;
import com.xyy.saas.inquiry.product.server.service.BaseIntegrationTest;
import com.xyy.saas.inquiry.product.server.service.product.ProductInfoServiceImpl;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

/**
 * {@link ProductInfoServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(ProductInfoServiceImpl.class)
public class ProductInfoServiceImplTest extends BaseIntegrationTest {

    @Resource
    private ProductInfoServiceImpl productInfoService;

    @Resource
    private ProductInfoMapper productInfoMapper;

    @Test
    public void testSaveOrUpdateProduct_success() {
        // 准备参数
        ProductInfoSaveReqVO createReqVO = randomPojo(ProductInfoSaveReqVO.class).setId(null);
        ProductInfoDto dto = BeanUtil.toBean(createReqVO, ProductInfoDto.class);

        // 调用
        Long productInfoId = productInfoService.saveOrUpdateProduct(dto, null);
        // 断言
        assertNotNull(productInfoId);
        // 校验记录的属性是否正确
        ProductInfoDO productInfo = productInfoMapper.selectById(productInfoId);
        assertPojoEquals(createReqVO, productInfo, "id");
    }

    @Test
    public void testUpdateProductInfo_success() {
        // mock 数据
        ProductInfoDO dbProductInfo = randomPojo(ProductInfoDO.class);
        productInfoMapper.insert(dbProductInfo);// @Sql: 先插入出一条存在的数据
        // 准备参数
        ProductInfoDto updateReqVO = randomPojo(ProductInfoDto.class, o -> {
            o.setId(dbProductInfo.getId()); // 设置更新的 ID
        });

        // 调用
        productInfoService.saveOrUpdateProduct(updateReqVO, null);
        // 校验是否更新正确
        ProductInfoDO productInfo = productInfoMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, productInfo);
    }

    @Test
    public void testUpdateProductInfo_notExists() {
        // 准备参数
        ProductInfoDto updateReqVO = randomPojo(ProductInfoDto.class);

        // 调用, 并断言异常
        assertServiceException(() -> productInfoService.saveOrUpdateProduct(updateReqVO, null), PRODUCT_INFO_NOT_EXISTS);
    }

    @Test
    public void testDeleteProductInfo_success() {
        // mock 数据
        ProductInfoDO dbProductInfo = randomPojo(ProductInfoDO.class);
        productInfoMapper.insert(dbProductInfo);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbProductInfo.getId();

        // 调用
        productInfoService.deleteProductInfo(id);
        // 校验数据不存在了
        assertNull(productInfoMapper.selectById(id));
    }

    @Test
    public void testDeleteProductInfo_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> productInfoService.deleteProductInfo(id), PRODUCT_INFO_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetProductInfoByIdPage() {
        // mock 数据
        ProductInfoDO dbProductInfo = randomPojo(ProductInfoDO.class, o -> { // 等会查询到
            o.setShowPref(null);
            o.setSourceProductPref(null);
            o.setStdlibId(null);
            o.setMnemonicCode(null);
            o.setCommonName(null);
            o.setBrandName(null);
            o.setSpec(null);
            o.setBarcode(null);
            o.setManufacturer(null);
            o.setApprovalNumber(null);
            o.setInputTaxRate(null);
            o.setOutputTaxRate(null);
            o.setUnbundledQuantity(null);
            o.setStatus(null);
            o.setMultiFlag(null);
            o.setDeletedAt(null);
            o.setDeleteType(null);
            o.setRemark(null);
            o.setCreateTime(null);
        });
        productInfoMapper.insert(dbProductInfo);
        // 测试 showPref 不匹配
        productInfoMapper.insert(cloneIgnoreId(dbProductInfo, o -> o.setShowPref(null)));
        // 测试 sourceProductId 不匹配
        productInfoMapper.insert(cloneIgnoreId(dbProductInfo, o -> o.setSourceProductPref(null)));
        // 测试 standardLibId 不匹配
        productInfoMapper.insert(cloneIgnoreId(dbProductInfo, o -> o.setStdlibId(null)));
        // 测试 mnemonicCode 不匹配
        productInfoMapper.insert(cloneIgnoreId(dbProductInfo, o -> o.setMnemonicCode(null)));
        // 测试 commonName 不匹配
        productInfoMapper.insert(cloneIgnoreId(dbProductInfo, o -> o.setCommonName(null)));
        // 测试 brandName 不匹配
        productInfoMapper.insert(cloneIgnoreId(dbProductInfo, o -> o.setBrandName(null)));
        // 测试 spec 不匹配
        productInfoMapper.insert(cloneIgnoreId(dbProductInfo, o -> o.setSpec(null)));
        // 测试 barcode 不匹配
        productInfoMapper.insert(cloneIgnoreId(dbProductInfo, o -> o.setBarcode(null)));
        // 测试 manufacturer 不匹配
        productInfoMapper.insert(cloneIgnoreId(dbProductInfo, o -> o.setManufacturer(null)));
        // 测试 approvalNumber 不匹配
        productInfoMapper.insert(cloneIgnoreId(dbProductInfo, o -> o.setApprovalNumber(null)));
        // 测试 inputTaxRate 不匹配
        productInfoMapper.insert(cloneIgnoreId(dbProductInfo, o -> o.setInputTaxRate(null)));
        // 测试 outputTaxRate 不匹配
        productInfoMapper.insert(cloneIgnoreId(dbProductInfo, o -> o.setOutputTaxRate(null)));
        // 测试 unbundledQuantity 不匹配
        productInfoMapper.insert(cloneIgnoreId(dbProductInfo, o -> o.setUnbundledQuantity(null)));
        // 测试 status 不匹配
        productInfoMapper.insert(cloneIgnoreId(dbProductInfo, o -> o.setStatus(null)));
        // 测试 multiFlag 不匹配
        productInfoMapper.insert(cloneIgnoreId(dbProductInfo, o -> o.setMultiFlag(null)));
        // 测试 deletedAt 不匹配
        productInfoMapper.insert(cloneIgnoreId(dbProductInfo, o -> o.setDeletedAt(null)));
        // 测试 deleteType 不匹配
        productInfoMapper.insert(cloneIgnoreId(dbProductInfo, o -> o.setDeleteType(null)));
        // 测试 remark 不匹配
        productInfoMapper.insert(cloneIgnoreId(dbProductInfo, o -> o.setRemark(null)));
        // 测试 createTime 不匹配
        productInfoMapper.insert(cloneIgnoreId(dbProductInfo, o -> o.setCreateTime(null)));
        // 准备参数
        ProductInfoPageReqVO reqVO = new ProductInfoPageReqVO();
        reqVO.setMixedQuery(null);
        reqVO.setStdlibIdList(null);
        reqVO.setUnit(null);
        reqVO.setDosageForm(null);
        reqVO.setBusinessScope(null);
        reqVO.setPresCategory(null);
        reqVO.setStorageWay(null);
        reqVO.setManufacturer(null);
        reqVO.setStatus(null);
        // reqVO.setMultiFlag(null);
        reqVO.setDeletedAt(null);
        reqVO.setDeleteType(null);
        reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

        // 调用
        PageResult<ProductInfoDto> pageResult = productInfoService.getProductInfoPage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbProductInfo, pageResult.getList().get(0));
    }

}