<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.transmitter.server.dal.mysql.config.TransmissionConfigItemMapper">


  <select id="selectItemCountByPackId" resultType="com.xyy.saas.inquiry.pojo.CommonGroupStatisticsDto">

    SELECT
    config_package_id as intKey,
    COUNT(distinct node_type) as countValue
    FROM saas_transmission_config_item
    WHERE
    config_package_id IN
    <foreach collection="configPackIds" item="cid" open="(" separator="," close=")">
      #{cid}
    </foreach>
    <if test="disable != null">
      and disable = #{disable}
    </if>
    AND deleted = false
    GROUP BY config_package_id

  </select>

</mapper> 