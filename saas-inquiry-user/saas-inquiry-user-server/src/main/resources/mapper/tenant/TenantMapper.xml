<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.system.dal.mysql.tenant.TenantMapper">

  <resultMap id="BaseResultMap" type="cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO">
    <id property="id" column="id"/>
    <result property="pref" column="pref"/>
    <result property="name" column="name"/>
    <result property="type" column="type"/>
    <result property="headTenantId" column="head_tenant_id"/>
    <result property="contactUserId" column="contact_user_id"/>
    <result property="contactName" column="contact_name"/>
    <result property="contactMobile" column="contact_mobile"/>
    <result property="businessLicenseName" column="business_license_name"/>
    <result property="businessLicenseNumber" column="business_license_number"/>
    <result property="wzBizTypeStatus" column="wz_biz_type_status"/>
    <result property="wzAccountCount" column="wz_account_count"/>
    <result property="zhlBizTypeStatus" column="zhl_biz_type_status"/>
    <result property="zhlAccountCount" column="zhl_account_count"/>
    <result property="status" column="status"/>
    <result property="province" column="province"/>
    <result property="provinceCode" column="province_code"/>
    <result property="city" column="city"/>
    <result property="cityCode" column="city_code"/>
    <result property="area" column="area"/>
    <result property="areaCode" column="area_code"/>
    <result property="address" column="address"/>
    <result property="envTag" column="env_tag"/>
    <result property="bizType" column="biz_type"/>
    <result property="bizTypes" column="biz_types"/>
    <result property="bizTenantType" column="biz_tenant_type"/>
    <result property="bizHeadTenantId" column="biz_head_tenant_id"/>
    <result property="createTime" column="create_time"/>
    <result property="updateTime" column="update_time"/>
    <result property="deleted" column="deleted"/>
    <result property="creator" column="creator"/>
    <result property="updater" column="updater"/>
  </resultMap>

  <select id="getTenantPage" resultMap="BaseResultMap">
    select st.*
    <if test="pageReqVO.bizType != null">
      , stbr.biz_type, stbr.tenant_type as biz_tenant_type, stbr.head_tenant_id as biz_head_tenant_id
    </if>
    from system_tenant st
    <if test="pageReqVO.transmissionOrganId != null">
      left join saas_tenant_third_app stta on st.id = stta.tenant_id
    </if>
    <if test="pageReqVO.bizType != null">
      left join system_tenant_biz_relation stbr on st.id = stbr.tenant_id
    </if>
    <where>
      st.deleted = false
      <if test="pageReqVO.transmissionOrganId != null">
        and stta.transmission_organ_id = #{pageReqVO.transmissionOrganId}
        and stta.deleted = false
      </if>
      <if test="pageReqVO.name != null and pageReqVO.name != ''">
        and st.name like concat('%',#{pageReqVO.name},'%')
      </if>
      <if test="pageReqVO.pref != null and pageReqVO.pref != ''">
        and st.pref = #{pageReqVO.pref}
      </if>
      <if test="pageReqVO.headTenantId != null">
        and st.head_tenant_id = #{pageReqVO.headTenantId}
      </if>
      <if test="pageReqVO.type != null">
        and st.type = #{pageReqVO.type}
      </if>
      <if test="pageReqVO.contactName != null and pageReqVO.contactName != ''">
        and st.contact_name like concat('%',#{pageReqVO.contactName},'%')
      </if>
      <if test="pageReqVO.contactMobile != null and pageReqVO.contactMobile != ''">
        and st.contact_mobile = #{pageReqVO.contactMobile}
      </if>
      <if test="pageReqVO.businessLicenseName != null and pageReqVO.businessLicenseName != ''">
        and st.business_license_name like concat('%',#{pageReqVO.businessLicenseName},'%')
      </if>
      <if test="pageReqVO.businessLicenseNumber != null and pageReqVO.businessLicenseNumber != ''">
        and st.business_license_number = #{pageReqVO.businessLicenseNumber}
      </if>
      <if test="pageReqVO.status != null">
        and st.status = #{pageReqVO.status}
      </if>
      <if test="pageReqVO.province != null and pageReqVO.province != ''">
        and st.province = #{pageReqVO.province}
      </if>
      <if test="pageReqVO.provinceCode != null and pageReqVO.provinceCode != ''">
        and st.province_code = #{pageReqVO.provinceCode}
      </if>
      <if test="pageReqVO.city != null and pageReqVO.city != ''">
        and st.city = #{pageReqVO.city}
      </if>
      <if test="pageReqVO.cityCode != null and pageReqVO.cityCode != ''">
        and st.city_code = #{pageReqVO.cityCode}
      </if>
      <if test="pageReqVO.area != null and pageReqVO.area != ''">
        and st.area = #{pageReqVO.area}
      </if>
      <if test="pageReqVO.areaCode != null and pageReqVO.areaCode != ''">
        and st.area_code = #{pageReqVO.areaCode}
      </if>
      <if test="pageReqVO.address != null and pageReqVO.address != ''">
        and st.address = #{pageReqVO.address}
      </if>
      <if test="pageReqVO.envTag != null and pageReqVO.envTag != ''">
        and st.env_tag = #{pageReqVO.envTag}
      </if>
      <if test="pageReqVO.createTime != null and pageReqVO.createTime.length > 0">
        and st.create_time BETWEEN #{pageReqVO.createTime[0],javaType=java.time.LocalDateTime} AND
        #{pageReqVO.createTime[1],javaType=java.time.LocalDateTime}
      </if>
      <if test="pageReqVO.nameOrPref != null and  pageReqVO.nameOrPref != ''">
        and (st.name like concat('%',#{pageReqVO.nameOrPref},'%') or st.pref = #{pageReqVO.nameOrPref})
      </if>
      <if test="pageReqVO.nid != null">
        and st.id != #{pageReqVO.nid}
      </if>
      <if test="pageReqVO.bizType != null">
        and stbr.biz_type = #{pageReqVO.bizType}
      </if>
      <if test="pageReqVO.bizTenantType != null">
        and stbr.tenant_type = #{pageReqVO.bizTenantType}
      </if>
      <if test="pageReqVO.bizHeadTenantId != null">
        and stbr.head_tenant_id = #{pageReqVO.bizHeadTenantId}
      </if>
    </where>
    <if test="pageReqVO.bizType != null">
        group by st.id
    </if>
    order by st.id desc
  </select>

  <select id="getTenantBizRelations" resultMap="BaseResultMap">
    select st.id, GROUP_CONCAT(stbr.biz_type ORDER BY stbr.biz_type SEPARATOR ',') AS biz_types
    from system_tenant st
    left join system_tenant_biz_relation stbr on st.id = stbr.tenant_id
    <where>
      st.deleted = false
      <if test="tenantIds != null and tenantIds.size() > 0 ">
        and st.id in
        <foreach collection="tenantIds" open="(" item="id" separator="," close=")">
          #{id}
        </foreach>
      </if>
    </where>
    group by st.id
  </select>

</mapper>