<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.system.dal.mysql.tenant.TenantTransmissionServicePackRelationMapper">

  <!--
      一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
      无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
      代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
      文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
   -->


  <select id="selectCountByOrgans" resultType="com.xyy.saas.inquiry.pojo.CommonGroupStatisticsDto">
    SELECT
    organ_id as intKey,
    COUNT(DISTINCT tenant_id) as countValue
    FROM saas_tenant_transmission_service_pack_relation
    WHERE
    organ_id IN
    <foreach collection="organIds" item="organId" open="(" separator="," close=")">
      #{organId}
    </foreach>
    <if test="status != null">
      and status = #{status}
    </if>
    AND deleted = false
    GROUP BY organ_id
  </select>

  <select id="selectCountByServicePacks" resultType="com.xyy.saas.inquiry.pojo.CommonGroupStatisticsDto">
    SELECT
    service_pack_id as intKey,
    COUNT(DISTINCT tenant_id) as countValue
    FROM saas_tenant_transmission_service_pack_relation
    WHERE
    service_pack_id IN
    <foreach collection="servicePackIds" item="servicePackId" open="(" separator="," close=")">
      #{servicePackId}
    </foreach>
    <if test="status != null">
      and status = #{status}
    </if>
    AND deleted = false
    GROUP BY service_pack_id
  </select>

  <!-- result类型 -->
  <resultMap id="selectCountByCatalogIdsResult" type="com.xyy.saas.inquiry.pojo.CommonGroupStatisticsDto">
    <result property="longKey" column="catalog_id"/>
    <result property="countValue" column="count"/>
  </resultMap>

  <select id="selectCountByCatalogIds" resultMap="selectCountByCatalogIdsResult">
    SELECT
    catalog_id,
    COUNT(DISTINCT tenant_id) as count
    FROM saas_tenant_transmission_service_pack_relation
    WHERE
    catalog_id IN
    <foreach collection="catalogIds" item="catalogId" open="(" separator="," close=")">
      #{catalogId}
    </foreach>
    <if test="status != null">
      and status = #{status}
    </if>
    AND deleted = false
    GROUP BY catalog_id
  </select>

  <delete id="deleteByTenantOrganType">
    delete from saas_tenant_transmission_service_pack_relation
    where tenant_id in
    <foreach collection="tenantIds" open="(" item="id" separator="," close=")">
      #{id}
    </foreach>
    <if test="organType != null">
      and organ_type = #{organType}
    </if>
  </delete>
  <delete id="deleteIds">
    delete from saas_tenant_transmission_service_pack_relation
    where id in
    <foreach collection="ids" open="(" item="id" separator="," close=")">
      #{id}
    </foreach>
  </delete>

  <select id="getTenantServicePackRelationPage"
    resultType="cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantRespVO">
    select
    a.*
    from system_tenant a where a.deleted = false and a.id != ${@com.xyy.saas.inquiry.constant.TenantConstant@DEFAULT_TENANT_ID}
    <if test="reqVO.nameOrPref != null and reqVO.nameOrPref != ''">
      and ( a.pref = #{reqVO.nameOrPref} or a.name like concat('%',#{reqVO.nameOrPref},'%') )
    </if>
    <if test="reqVO.contactMobile != null and reqVO.contactMobile != ''">
      and a.contact_mobile like concat('%',#{reqVO.contactMobile},'%')
    </if>
    <if test="reqVO.provinceCode != null and reqVO.provinceCode != ''">
      and a.province_code = #{reqVO.provinceCode}
    </if>
    <if test="reqVO.cityCode != null and reqVO.cityCode != ''">
      and a.city_code = #{reqVO.cityCode}
    </if>
    <if test="reqVO.areaCode != null and reqVO.areaCode != ''">
      and a.area_code = #{reqVO.areaCode}
    </if>
    <if test="reqVO.servicePackIds != null and reqVO.servicePackIds.size() > 0">
      and exists(select 1 from saas_tenant_transmission_service_pack_relation where tenant_id = a.id and service_pack_id in
      <foreach collection="reqVO.servicePackIds" open="(" item="id" separator="," close=")">
        #{id}
      </foreach>
      and status = ${@<EMAIL>()}
      )
    </if>
    <if test="reqVO.servicePackId != null ">
      and exists(select 1 from saas_tenant_transmission_service_pack_relation where tenant_id = a.id and service_pack_id = #{reqVO.servicePackId}
      and status = ${@<EMAIL>()}
      )
    </if>
    <if test="reqVO.catalogId != null">
      and exists(select 1 from saas_tenant_transmission_service_pack_relation where tenant_id = a.id and catalog_id = #{reqVO.catalogId}
      and status = ${@<EMAIL>()} )
    </if>
    <if test="reqVO.organId != null">
      and exists(select 1 from saas_tenant_transmission_service_pack_relation where tenant_id = a.id and organ_id = #{reqVO.organId}
      and status = ${@<EMAIL>()} )
    </if>
    <if test="reqVO.organType != null and reqVO.status != null">
      and
      <if test=" reqVO.status == 0">
        not exists(select 1 from saas_tenant_transmission_service_pack_relation where tenant_id = a.id and organ_type = #{reqVO.organType} and status = ${@<EMAIL>()}
        )
      </if>
      <if test=" reqVO.status == 1 ">
        exists(select 1 from saas_tenant_transmission_service_pack_relation where tenant_id = a.id and organ_type = #{reqVO.organType} and status = #{reqVO.status})
      </if>
    </if>
    order by a.id desc
  </select>

  <resultMap id="CatalogRelationTenantDtoResultMap" type="com.xyy.saas.inquiry.pojo.catalog.CatalogRelationTenantDto">
    <id column="id" property="id"/>
    <result column="pref" property="pref"/>
    <result column="name" property="name"/>
    <result column="head_tenant_id" property="headTenantId"/>
    <result column="contact_user_id" property="contactUserId"/>
    <result column="contact_name" property="contactName"/>
    <result column="contact_mobile" property="contactMobile"/>
    <result column="business_license_name" property="businessLicenseName"/>
    <result column="business_license_number" property="businessLicenseNumber"/>
    <result column="wz_biz_type_status" property="wzBizTypeStatus"/>
    <result column="wz_account_count" jdbcType="INTEGER" property="wzAccountCount"/>
    <result column="zhl_biz_type_status" property="zhlBizTypeStatus"/>
    <result column="zhl_account_count" jdbcType="INTEGER" property="zhlAccountCount"/>
    <result column="province" property="province"/>
    <result column="province_code" property="provinceCode"/>
    <result column="city" property="city"/>
    <result column="city_code" property="cityCode"/>
    <result column="area" property="area"/>
    <result column="area_code" property="areaCode"/>
    <result column="address" property="address"/>
    <result column="creator" property="creator"/>
    <result column="updater" property="updater"/>
    <result column="create_time" property="createTime"/>
    <result column="update_time" property="updateTime"/>
    <result column="catalog_id" property="catalogId"/>
    <result column="tenantTransmissionServicePackRelationExt" property="tenantTransmissionServicePackRelationExt" typeHandler="com.xyy.saas.inquiry.annotation.JsonTypeHandler"/>
  </resultMap>

  <select id="getCatalogRelationTenantPage" resultMap="CatalogRelationTenantDtoResultMap">
    select
    st.id,
    st.pref,
    st.name,
    st.head_tenant_id,
    st.contact_user_id,
    st.contact_name,
    st.contact_mobile,
    st.business_license_name,
    st.business_license_number,
    st.wz_biz_type_status,
    st.wz_account_count,
    st.zhl_biz_type_status,
    st.zhl_account_count,
    st.province,
    st.province_code,
    st.city,
    st.city_code,
    st.area,
    st.area_code,
    st.address,
    st.creator,
    st.updater,
    st.create_time,
    st.update_time,
    stt.catalog_id,
    stt.ext tenantTransmissionServicePackRelationExt
    from
    saas_tenant_transmission_service_pack_relation stt
    left join system_tenant st on stt.tenant_id = st.id
    where
    stt.deleted = false
    and st.deleted = false
    <if test="reqDto.catalogId != null">
      and stt.catalog_id = #{reqDto.catalogId}
    </if>
    <if test="reqDto.catalogIdList != null and reqDto.catalogIdList.size() > 0">
      <foreach collection="reqDto.catalogIdList" index="index" item="item" open="and stt.catalog_id in (" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="reqDto.tenantNameOrPref != null and reqDto.tenantNameOrPref != ''">
      and ( st.pref = #{reqDto.tenantNameOrPref} or st.name like concat('%',#{reqDto.tenantNameOrPref},'%') )
    </if>
    <if test="reqDto.tenantContactMobile != null and reqDto.tenantContactMobile != ''">
      and st.contact_mobile like concat('%',#{reqDto.tenantContactMobile},'%')
    </if>
    <if test="reqDto.provinceCode != null and reqDto.provinceCode != ''">
      and st.province_code = #{reqDto.provinceCode}
    </if>
    <if test="reqDto.cityCode != null and reqDto.cityCode != ''">
      and st.city_code = #{reqDto.cityCode}
    </if>
    <if test="reqDto.areaCode != null and reqDto.areaCode != ''">
      and st.area_code = #{reqDto.areaCode}
    </if>
    order by st.id
  </select>


</mapper>