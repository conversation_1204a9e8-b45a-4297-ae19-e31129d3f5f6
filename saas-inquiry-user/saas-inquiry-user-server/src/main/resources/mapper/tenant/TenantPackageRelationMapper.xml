<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.system.dal.mysql.tenant.TenantPackageRelationMapper">

  <resultMap id="BaseResultMap" type="cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageRelationDO">
    <id property="id" column="id"/>
    <result property="tenantId" column="tenant_id"/>
    <result property="bizType" column="biz_type"/>
    <result property="packageId" column="package_id"/>
    <result property="packageType" column="package_type"/>
    <result property="packageName" column="package_name"/>
    <result property="inquiryBizType" column="inquiry_biz_type"/>
    <result property="inquiryAuditType" column="inquiry_audit_type"/>
    <result property="hospitalPrefs" column="hospital_prefs" typeHandler="cn.iocoder.yudao.framework.mybatis.core.type.StringListTypeHandler"/>
    <result property="price" column="price"/>
    <result property="inquiryWayTypes" column="inquiry_way_types" typeHandler="cn.iocoder.yudao.framework.mybatis.core.type.IntegerListTypeHandler"/>
    <result property="prescriptionTypes" column="prescription_types" typeHandler="cn.iocoder.yudao.framework.mybatis.core.type.IntegerListTypeHandler"/>
    <result property="inquiryPackageItems" column="inquiry_package_items" typeHandler="com.xyy.saas.inquiry.annotation.JsonTypeHandler"/>
    <result property="status" column="status"/>
    <result property="packageNature" column="package_nature"/>
    <result property="startTime" column="start_time"/>
    <result property="endTime" column="end_time"/>
    <result property="paymentType" column="payment_type"/>
    <result property="signTime" column="sign_time"/>
    <result property="signUser" column="sign_user"/>
    <result property="proxyUser" column="proxy_user"/>
    <result property="signChannel" column="sign_channel"/>
    <result property="actualAmount" column="actual_amount"/>
    <result property="collectAccount" column="collect_account"/>
    <result property="payNo" column="pay_no"/>
    <result property="payVoucherUrls" column="pay_voucher_urls" typeHandler="cn.iocoder.yudao.framework.mybatis.core.type.StringListTypeHandler"/>
    <result property="remark" column="remark"/>
    <result property="statusChangeInfo" column="status_change_info" typeHandler="com.xyy.saas.inquiry.annotation.JsonTypeHandler"/>
    <result property="ext" column="ext" typeHandler="com.xyy.saas.inquiry.annotation.JsonTypeHandler"/>
  </resultMap>
  <!--
      一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
      无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
      代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
      文档可见：https://www.iocoder.cn/MyBatis/x-plugins/

      <if test="pageReqVO.effective != null and pageReqVO.effective == 0">
      AND a.start_time <![CDATA[<=]]>  now() AND a.end_time <![CDATA[>=]]> now() and a.status = 0
  </if>
  <if test="pageReqVO.effective != null and pageReqVO.effective == 1">
      AND ( ( a.start_time <![CDATA[>=]]> now() and  a.status = 0 )  or
            (a.start_time <![CDATA[<=]]>  now() AND a.end_time <![CDATA[>=]]> now() and a.status = 1 ) )
  </if>
  <if test="pageReqVO.effective != null and pageReqVO.effective == 2">
      AND  (a.status in (2,3) or  a.end_time <![CDATA[<]]> now())
  </if>
   -->
  <select id="getTenantPackageRelationPage" resultMap="BaseResultMap">
    select a.*
    from saas_tenant_package_relation a left join system_tenant_package b on a.package_id = b.id
    left join system_tenant c on a.tenant_id = c.id
    <where>
      a.deleted = false
      <if test="pageReqVO.pref != null and pageReqVO.pref != ''">
        and a.pref = #{pageReqVO.pref}
      </if>
      <if test="pageReqVO.tenantId != null">
        and a.tenant_id = #{pageReqVO.tenantId}
      </if>
      <if test="pageReqVO.status != null ">
        AND a.status = #{pageReqVO.status}
      </if>
      <if test="pageReqVO.bizType != null ">
        AND a.biz_type = #{pageReqVO.bizType}
      </if>
      <if test="pageReqVO.inquiryBizType != null ">
        AND a.inquiry_biz_type = #{pageReqVO.inquiryBizType}
      </if>
      <if test="pageReqVO.packageId != null ">
        AND a.package_id = #{pageReqVO.packageId}
      </if>
      <if test="pageReqVO.recharge != null and pageReqVO.recharge == 0">
        AND a.package_id = 0
      </if>
      <if test="pageReqVO.recharge != null and pageReqVO.recharge == 1">
        AND a.package_id != 0
      </if>
      <if test="pageReqVO.packageIds != null and pageReqVO.packageIds.size() > 0 ">
        and a.package_id in
        <foreach collection="pageReqVO.packageIds" open="(" item="id" separator="," close=")">
          #{id}
        </foreach>
      </if>
      <if test="pageReqVO.packageName != null and pageReqVO.packageName != '' ">
        AND ( a.package_name like concat('%',#{pageReqVO.packageName},'%') or b.pref = #{pageReqVO.packageName} )
      </if>
      <if test="pageReqVO.signChannel != null ">
        AND a.sign_channel = #{pageReqVO.signChannel}
      </if>
      <if test="pageReqVO.paymentType != null">
        AND a.payment_type = #{pageReqVO.paymentType}
      </if>
      <if test="pageReqVO.packageNature != null">
        AND a.package_nature = #{pageReqVO.packageNature}
      </if>
      <if test="pageReqVO.signUser != null and pageReqVO.signUser != ''">
        AND a.sign_user = #{pageReqVO.signUser}
      </if>
      <if test="pageReqVO.proxyUser != null and pageReqVO.proxyUser != ''">
        AND a.proxy_user = #{pageReqVO.proxyUser}
      </if>
      <if test="pageReqVO.collectAccount != null ">
        AND a.collect_account = #{pageReqVO.collectAccount}
      </if>
      <if test="pageReqVO.payNo != null and pageReqVO.payNo != '' ">
        AND a.pay_no like concat('%',#{pageReqVO.payNo},'%')
      </if>
      <if test="pageReqVO.createTime != null and pageReqVO.createTime.length > 0">
        AND a.create_time BETWEEN #{pageReqVO.createTime[0],javaType=java.time.LocalDateTime} AND
        #{pageReqVO.createTime[1],javaType=java.time.LocalDateTime}
      </if>
      <if test="pageReqVO.signTime != null and pageReqVO.signTime.length > 0">
        AND a.sign_time BETWEEN #{pageReqVO.signTime[0],javaType=java.time.LocalDateTime} AND
        #{pageReqVO.signTime[1],javaType=java.time.LocalDateTime}
      </if>

      <if test="pageReqVO.inquiryWayTypes != null and pageReqVO.inquiryWayTypes.size() > 0">
        and
        <foreach collection="pageReqVO.inquiryWayTypes" item="iwt" open="(" close=")" separator=" and ">
          FIND_IN_SET(#{iwt},a.inquiry_way_types)
        </foreach>
      </if>

      <if test="pageReqVO.prescriptionTypes != null and pageReqVO.prescriptionTypes.size() > 0">
        and
        <foreach collection="pageReqVO.prescriptionTypes" item="iwt" open="(" close=")" separator=" and ">
          FIND_IN_SET(#{iwt},a.prescription_types)
        </foreach>
      </if>

      <if test="pageReqVO.hospitalPref != null">
        and FIND_IN_SET(#{pageReqVO.hospitalPref},a.hospital_prefs)
      </if>
      <if test="pageReqVO.tenantName != null and pageReqVO.tenantName != '' ">
        AND ( c.name like concat('%',#{pageReqVO.tenantName},'%') or c.pref = #{pageReqVO.tenantName}
        or c.business_license_name like concat('%',#{pageReqVO.tenantName},'%') or c.business_license_number like concat('%',#{pageReqVO.tenantName},'%') )
      </if>
      <if test="pageReqVO.provinceCode != null and pageReqVO.provinceCode != '' ">
        AND c.province_code = #{pageReqVO.provinceCode}
      </if>
      <if test="pageReqVO.cityCode != null and pageReqVO.cityCode != '' ">
        AND c.city_code = #{pageReqVO.cityCode}
      </if>
      <if test="pageReqVO.areaCode != null and pageReqVO.areaCode != '' ">
        AND c.area_code = #{pageReqVO.areaCode}
      </if>
    </where>
    order by id desc
  </select>
</mapper>