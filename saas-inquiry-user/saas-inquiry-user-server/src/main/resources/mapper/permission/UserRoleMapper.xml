<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.system.dal.mysql.permission.UserRoleMapper">

  <!--
      一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
      无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
      代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
      文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
   -->


  <select id="selectUserRoleByUserIdRoleCodes"
    resultType="cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleDO">
    select a.* from system_role a left join system_user_role b on a.id = b.role_id
    <where>
      b.user_id = #{userId}
      <if test="roleCodes != null and roleCodes.size() > 0 ">
        and a.code in
        <foreach collection="roleCodes" open="(" item="id" separator="," close=")">
          #{id}
        </foreach>
      </if>
    </where>
  </select>
  <select id="getUserRoleByUserIds"
    resultType="cn.iocoder.yudao.module.system.controller.admin.permission.vo.permission.UserRoleVO">
    select a.*,b.name as roleName,b.code as roleCode from system_user_role a left join system_role b on a.role_id = b.id
    <where>
      a.tenant_id = #{tenantId} and a.deleted = false and b.deleted = false
      <if test="userIds != null and userIds.size() > 0 ">
        and a.user_id in
        <foreach collection="userIds" open="(" item="id" separator="," close=")">
          #{id}
        </foreach>
      </if>
    </where>

  </select>


  <delete id="deleteListByUserIdAndRoleIdTenantIds">
    delete from system_user_role
    <where>
      user_id = #{userId}
      <if test="roleIds != null and roleIds.size() > 0 ">
        and role_id in
        <foreach collection="roleIds" open="(" item="id" separator="," close=")">
          #{id}
        </foreach>
      </if>
      <if test="tenantIds != null and tenantIds.size() > 0 ">
        and tenant_id in
        <foreach collection="tenantIds" open="(" item="id" separator="," close=")">
          #{id}
        </foreach>
      </if>
    </where>
  </delete>
</mapper>