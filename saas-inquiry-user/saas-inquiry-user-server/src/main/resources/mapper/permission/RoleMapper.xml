<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.system.dal.mysql.permission.RoleMapper">

  <!--
      一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
      无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
      代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
      文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
   -->


  <select id="selectPageStore" resultType="cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleDO">
    <if test="reqVO.roleIds != null and reqVO.roleIds.size() > 0 ">
      select * from system_role where id in
      <foreach collection="reqVO.roleIds" open="(" item="roleId" separator="," close=")">
        #{roleId}
      </foreach>
      <if test="reqVO.status != null ">
        AND status = #{reqVO.status}
      </if>
      <if test="reqVO.name != null and reqVO.name != ''">
        AND name like concat('%',#{reqVO.name},'%')
      </if>
      union all
    </if>
    select * from system_role
    <where>
      tenant_id = #{reqVO.tenantId}
      <if test="reqVO.name != null and reqVO.name != ''">
        AND name like concat('%',#{reqVO.name},'%')
      </if>
      <if test="reqVO.code != null and reqVO.code != ''">
        AND code like concat('%',#{reqVO.code},'%')
      </if>
      <if test="reqVO.status != null ">
        AND status = #{reqVO.status}
      </if>
      <if test="reqVO.createTime != null and reqVO.createTime.length > 0">
        AND create_time BETWEEN #{reqVO.createTime[0],javaType=java.time.LocalDateTime} AND
        #{reqVO.createTime[1],javaType=java.time.LocalDateTime}
      </if>

    </where>

  </select>
</mapper>