-- 社交客户端应用配置表
DROP TABLE IF EXISTS `system_social_client_app`;
CREATE TABLE `system_social_client_app` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用名',
  `social_type` tinyint NOT NULL COMMENT '社交平台的类型',
  `app_type` tinyint NOT NULL COMMENT '应用类型',
  `user_type` tinyint NOT NULL COMMENT '用户类型',
  `client_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端编号',
  `client_secret` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端密钥',
  `agent_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '代理编号',
  `status` tinyint NOT NULL COMMENT '状态',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_social_app_user_type` (`social_type`, `app_type`, `user_type`, `tenant_id`) COMMENT '社交平台、应用类型、用户类型唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='社交客户端应用配置表';

-- 插入示例数据
INSERT INTO `system_social_client_app` (`name`, `social_type`, `app_type`, `user_type`, `client_id`, `client_secret`, `status`, `creator`, `tenant_id`) VALUES
('荷叶问诊微信小程序', 34, 1, 2, 'your_hywz_app_id', 'your_hywz_app_secret', 0, 'system', 0),
('智慧脸微信小程序', 34, 2, 2, 'your_zhl_app_id', 'your_zhl_app_secret', 0, 'system', 0);