package com.xyy.saas.inquiry.transmitter.server.dal.mysql.dict;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import java.util.List;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictPageReqVO;
import com.xyy.saas.inquiry.transmitter.server.dal.dataobject.dict.TransmissionOrganDictDO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;

/**
 * 服务商字典 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TransmissionOrganDictMapper extends BaseMapperX<TransmissionOrganDictDO> {

    default PageResult<TransmissionOrganDictDO> selectPage(TransmissionOrganDictPageReqVO reqVO) {
        LambdaQueryWrapperX<TransmissionOrganDictDO> queryWrapper = new LambdaQueryWrapperX<TransmissionOrganDictDO>()
            .eqIfPresent(TransmissionOrganDictDO::getOrganId, reqVO.getOrganId())
            .eqIfPresent(TransmissionOrganDictDO::getDictType, reqVO.getDictType())
            .likeIfPresent(TransmissionOrganDictDO::getDictName, reqVO.getDictName())
            .eqIfPresent(TransmissionOrganDictDO::getParentValue, reqVO.getParentValue())
            .eqIfPresent(TransmissionOrganDictDO::getEndNode, reqVO.getEndNode())
            .eqIfPresent(TransmissionOrganDictDO::getValue, reqVO.getValue())
            .eqIfPresent(TransmissionOrganDictDO::getOuterValue, reqVO.getOuterValue())
            .eqIfPresent(TransmissionOrganDictDO::getStatus, reqVO.getStatus())
            .eqIfPresent(TransmissionOrganDictDO::getRemark, reqVO.getRemark())
            .eqIfPresent(TransmissionOrganDictDO::getSort, reqVO.getSort())
            .betweenIfPresent(TransmissionOrganDictDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(TransmissionOrganDictDO::getId);

        if (StringUtils.isNotBlank(reqVO.getLabel())) {
            queryWrapper.and(o -> o.like(TransmissionOrganDictDO::getLabel, reqVO.getLabel()))
                .or(a -> a.like(TransmissionOrganDictDO::getValue, reqVO.getLabel()));
        }
        return selectPage(reqVO, queryWrapper);
    }

    default List<TransmissionOrganDictDO> selectByCondition(TransmissionOrganDictPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TransmissionOrganDictDO>()
            .inIfPresent(TransmissionOrganDictDO::getId, reqVO.getIds())
            .eqIfPresent(TransmissionOrganDictDO::getOrganId, reqVO.getOrganId())
            .eqIfPresent(TransmissionOrganDictDO::getDictType, reqVO.getDictType())
            .inIfPresent(TransmissionOrganDictDO::getDictName, reqVO.getDictNames())
            .inIfPresent(TransmissionOrganDictDO::getValue, reqVO.getValues()));
    }

    default List<Integer> queryOrganByDictType(String dictType) {
        return selectObjs(new LambdaQueryWrapperX<TransmissionOrganDictDO>()
            .select(TransmissionOrganDictDO::getOrganId)
            .eq(TransmissionOrganDictDO::getDictType, dictType)
            .groupBy(TransmissionOrganDictDO::getOrganId));
    }

    default Long selectCount(Integer organId, String dictType) {
        return selectCount(new LambdaQueryWrapperX<TransmissionOrganDictDO>().eqIfPresent(TransmissionOrganDictDO::getOrganId, organId)
            .eqIfPresent(TransmissionOrganDictDO::getDictType, dictType));
    }

    default List<TransmissionOrganDictDO> getDictDatas(Integer organId, String dictType, String organDictName) {
        return selectList(new LambdaQueryWrapperX<TransmissionOrganDictDO>()
            .eqIfPresent(TransmissionOrganDictDO::getDictType, dictType)
            .eqIfPresent(TransmissionOrganDictDO::getOrganId, organId)
            .and((q) -> q.like(StringUtils.isNotBlank(organDictName), TransmissionOrganDictDO::getLabel, organDictName).or().like(StringUtils.isNotBlank(organDictName), TransmissionOrganDictDO::getValue, organDictName)));
    }
}