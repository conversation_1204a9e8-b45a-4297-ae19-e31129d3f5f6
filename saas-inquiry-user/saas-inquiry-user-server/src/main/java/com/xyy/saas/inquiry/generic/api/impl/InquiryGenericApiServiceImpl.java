package com.xyy.saas.inquiry.generic.api.impl;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xyy.saas.inquiry.generic.api.InquiryGenericApiService;
import com.xyy.saas.inquiry.generic.api.dto.diagnosis.InquiryDiagnosisDto;
import com.xyy.saas.inquiry.generic.api.dto.hospital.InquiryHospitalReqDto;
import com.xyy.saas.inquiry.generic.api.dto.hospital.InquiryHospitalRespDto;
import com.xyy.saas.inquiry.generic.model.GenericInvokeResponse;
import com.xyy.saas.inquiry.generic.util.GenericInvokerUtil;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 统一的问诊系统泛化API调用服务实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class InquiryGenericApiServiceImpl implements InquiryGenericApiService {

    // ==================== 定义返回结果的类型引用 ====================

    private final TypeReference<Map<String, InquiryHospitalRespDto>> TR_Map_String_InquiryHospitalRespDto = new TypeReference<>() {};

    private final TypeReference<List<InquiryDiagnosisDto>> TR_List_InquiryDiagnosisDto = new TypeReference<>() {};

    private final TypeReference<PageResult<InquiryDiagnosisDto>> TR_PageResult_InquiryDiagnosisDto = new TypeReference<>() {};


    // ==================== 医院相关API ====================

    /**
     * 查询医院列表-仅基础信息
     *
     * @param reqDto 请求入参
     * @return 医院列表
     * @see com.xyy.saas.inquiry.hospital.api.hospital.InquiryHospitalApi#getInquiryHospitalsBaseInfoMap(com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalReqDto)
     */
    @Override
    public Map<String, InquiryHospitalRespDto> getInquiryHospitalsBaseInfoMap(InquiryHospitalReqDto reqDto) {
        GenericInvokeResponse response = GenericInvokerUtil.invoke(
            "com.xyy.saas.inquiry.hospital.api.hospital.InquiryHospitalApi",
            "getInquiryHospitalsBaseInfoMap",
            new String[]{"com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalReqDto"},
            new Object[]{reqDto}
        );

        if (response.isSuccess()) {
            return GenericInvokerUtil.getResult(response, TR_Map_String_InquiryHospitalRespDto);
        } else {
            log.error("调用getInquiryHospitalsBaseInfoMap失败: {}", response.getErrorMessage());
            return Collections.emptyMap();
        }
    }

    // ==================== 诊断相关API ====================

    /**
     * 根据条件查询诊断列表
     *
     * @see com.xyy.saas.inquiry.hospital.api.diagnosis.InquiryDiagnosisApi#queryDiagnosisByCondition(List)
     */
    public List<InquiryDiagnosisDto> queryDiagnosisByCondition(List<String> dictNames) {
        GenericInvokeResponse response = GenericInvokerUtil.invoke(
            "com.xyy.saas.inquiry.hospital.api.diagnosis.InquiryDiagnosisApi",
            "queryDiagnosisByCondition",
            new String[]{"java.util.List"},
            new Object[]{dictNames}
        );
        
        if (response.isSuccess()) {
            return GenericInvokerUtil.getResult(response, TR_List_InquiryDiagnosisDto);
        } else {
            log.error("调用queryDiagnosisByCondition失败: {}", response.getErrorMessage());
            return Collections.emptyList();
        }
    }

    /**
     * 分页查询诊断信息（用于字典匹配）
     *
     * @see com.xyy.saas.inquiry.hospital.api.diagnosis.InquiryDiagnosisApi#getDiagnosisPage(com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDto)
     * @param pageReqDto 分页查询参数
     * @return 分页结果
     */
    @Override
    public PageResult<InquiryDiagnosisDto> getDiagnosisPage(InquiryDiagnosisDto pageReqDto) {
        GenericInvokeResponse response = GenericInvokerUtil.invoke(
            "com.xyy.saas.inquiry.hospital.api.diagnosis.InquiryDiagnosisApi",
            "getDiagnosisPage",
            new String[]{"com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDto"},
            new Object[]{pageReqDto}
        );

        if (response.isSuccess()) {
            return GenericInvokerUtil.getResult(response, TR_PageResult_InquiryDiagnosisDto);
        } else {
            log.error("调用getDiagnosisPage失败: {}", response.getErrorMessage());
            return PageResult.empty();
        }
    }

    /**
     * 根据诊断名称模糊查询诊断列表（用于字典匹配）
     *
     * @see com.xyy.saas.inquiry.hospital.api.diagnosis.InquiryDiagnosisApi#getDiagnosisByName(String)
     * @param diagnosisName 诊断名称
     * @return 诊断列表
     */
    @Override
    public List<InquiryDiagnosisDto> getDiagnosisByName(String diagnosisName) {
        GenericInvokeResponse response = GenericInvokerUtil.invoke(
            "com.xyy.saas.inquiry.hospital.api.diagnosis.InquiryDiagnosisApi",
            "getDiagnosisByName",
            new String[]{"java.lang.String"},
            new Object[]{diagnosisName}
        );

        if (response.isSuccess()) {
            return GenericInvokerUtil.getResult(response, TR_List_InquiryDiagnosisDto);
        } else {
            log.error("调用getDiagnosisByName失败: {}", response.getErrorMessage());
            return Collections.emptyList();
        }
    }

    /**
     * 根据ID列表查询诊断信息（用于字典匹配）
     *
     *
     * @see com.xyy.saas.inquiry.hospital.api.diagnosis.InquiryDiagnosisApi#getDiagnosisByIds(List)
     * @param ids 诊断ID列表
     * @return 诊断列表
     */
    @Override
    public List<InquiryDiagnosisDto> getDiagnosisByIds(List<Long> ids) {
        GenericInvokeResponse response = GenericInvokerUtil.invoke(
            "com.xyy.saas.inquiry.hospital.api.diagnosis.InquiryDiagnosisApi",
            "getDiagnosisByIds",
            new String[]{"java.util.List"},
            new Object[]{ids}
        );

        if (response.isSuccess()) {
            return GenericInvokerUtil.getResult(response, TR_List_InquiryDiagnosisDto);
        } else {
            log.error("调用getDiagnosisByIds失败: {}", response.getErrorMessage());
            return Collections.emptyList();
        }
    }

    // ==================== 药师相关API ====================

    /**
     * 定时任务处理药师离线
     *
     * @see com.xyy.saas.inquiry.pharmacist.api.pharmacist.InquiryPharmacistApi#jobHandPharmacistOffline()
     */
    @Override
    public void jobHandPharmacistOffline() {
        GenericInvokeResponse response = GenericInvokerUtil.invoke(
            "com.xyy.saas.inquiry.pharmacist.api.pharmacist.InquiryPharmacistApi",
            "jobHandPharmacistOffline"
        );

        if (!response.isSuccess()) {
            log.error("调用jobHandPharmacistOffline失败: {}", response.getErrorMessage());
        }
    }

    /**
     * 定时任务处理saas迁移
     *
     * @see com.xyy.saas.inquiry.pharmacist.api.pharmacist.InquiryPharmacistApi#jobHandSaasMigration()
     */
    @Override
    public void jobHandSaasMigration() {
        GenericInvokeResponse response = GenericInvokerUtil.invoke(
            "com.xyy.saas.inquiry.pharmacist.api.pharmacist.InquiryPharmacistApi",
            "jobHandSaasMigration"
        );

        if (!response.isSuccess()) {
            log.error("调用jobHandSaasMigration失败: {}", response.getErrorMessage());
        }
    }

    // ==================== 医生相关API ====================

    /**
     * 定时任务处理自动开方医生 出停诊
     *
     * @see com.xyy.saas.inquiry.hospital.api.doctor.InquiryDoctorApi#jobHandAutoInquiryDoctor()
     */
    @Override
    public void jobHandAutoInquiryDoctor() {
        GenericInvokeResponse response = GenericInvokerUtil.invoke(
            "com.xyy.saas.inquiry.hospital.api.doctor.InquiryDoctorApi",
            "jobHandAutoInquiryDoctor"
        );

        if (!response.isSuccess()) {
            log.error("调用jobHandAutoInquiryDoctor失败: {}", response.getErrorMessage());
        }
    }


    // ==================== 通用泛化调用方法 ====================

    @Override
    public GenericInvokeResponse invokeGeneric(String interfaceName, String methodName, String[] parameterTypes, Object[] args) {
        return GenericInvokerUtil.invoke(interfaceName, methodName, parameterTypes, args);
    }

    @Override
    public GenericInvokeResponse invokeGeneric(String interfaceName, String methodName, String[] parameterTypes, Object[] args, String version) {
        return GenericInvokerUtil.invoke(interfaceName, methodName, parameterTypes, args, version);
    }
}