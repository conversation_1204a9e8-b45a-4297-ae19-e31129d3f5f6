package com.xyy.saas.inquiry.user.server.controller.admin.tenant.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import java.util.List;

@Schema(description = "管理后台 - 门店三方应用配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TenantThirdAppPageReqVO extends PageParam {

    @Schema(description = "租户编号")
    private Long tenantId;

    @Schema(description = "租户编号集合")
    private List<Long> tenantIdList;

    @Schema(description = "应用名称", example = "ERP")
    private String appName;

    @Schema(description = "状态", example = "1")
    private Integer status;

    @Schema(description = "ERP对接机构id", example = "1")
    private Integer transmissionOrganId;

    @Schema(description = "id集合")
    private List<Long> idList;
}