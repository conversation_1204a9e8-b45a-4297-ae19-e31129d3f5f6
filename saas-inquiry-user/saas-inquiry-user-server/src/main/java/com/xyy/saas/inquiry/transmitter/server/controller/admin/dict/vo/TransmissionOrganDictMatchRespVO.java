package com.xyy.saas.inquiry.transmitter.server.controller.admin.dict.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.pojo.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 服务商数据字典配对 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TransmissionOrganDictMatchRespVO extends BaseDto {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "4371")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "医药行业行政机构ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "4327")
    @ExcelProperty("医药行业行政机构ID")
    private Integer organId;

    @Schema(description = "医药行业行政机构ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "4327")
    private String organName;

    @Schema(description = "字典类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("字典类型")
    private String dictType;


    @Schema(description = "saas字典id", requiredMode = Schema.RequiredMode.REQUIRED, example = "26981")
    @ExcelProperty("saas字典id")
    private Long dictId;

    @Schema(description = "saas字典项名称")
    private String dictName;

    @Schema(description = "saas字典项code")
    private String dictValue;

    @Schema(description = "服务商字典id", example = "2115")
    @ExcelProperty("服务商字典id")
    private Long organDictId;

    @Schema(description = "服务商字典项名称")
    private String organDictName;

    @Schema(description = "服务商字典项code")
    private String organDictValue;

    @Schema(description = "配对状态：0未配对 1已配对 2无法配对", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("配对状态：0未配对 1已配对")
    private Integer status;


}