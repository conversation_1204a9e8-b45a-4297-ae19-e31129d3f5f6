package com.xyy.saas.inquiry.generic.api.dto.hospital;

import com.xyy.saas.inquiry.pojo.condition.ConditionGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @see com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalSpecificPrescriptionCaDto
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class InquiryHospitalSpecificPrescriptionCaDto extends ConditionGroup {

    @Schema(description = "特定CA平台")
    private Integer signaturePlatform;

    @Schema(description = "特定CA平台配置id")
    private Integer signaturePlatformConfigId;

}