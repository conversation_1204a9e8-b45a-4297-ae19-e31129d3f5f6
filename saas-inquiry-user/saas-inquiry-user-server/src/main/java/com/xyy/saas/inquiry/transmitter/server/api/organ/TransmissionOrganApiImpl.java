package com.xyy.saas.inquiry.transmitter.server.api.organ;

import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.transmitter.api.organ.TransmissionOrganApi;
import com.xyy.saas.inquiry.transmitter.api.organ.dto.TransmissionOrganDTO;
import com.xyy.saas.inquiry.transmitter.server.convert.organ.TransmissionOrganConvert;
import com.xyy.saas.inquiry.transmitter.server.dal.dataobject.organ.TransmissionOrganDO;
import com.xyy.saas.inquiry.transmitter.server.service.organ.TransmissionOrganService;
import jakarta.annotation.Resource;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * 医药行业机构API
 *
 * <AUTHOR>
 * @date 2025/02/24 15:39
 */
@DubboService
public class TransmissionOrganApiImpl implements TransmissionOrganApi {

    @Resource
    private TransmissionOrganService organService;

    /**
     * 根据机构ID查询机构详细信息
     * <p>
     * 主要信息包括： 1. 基础信息：机构名称、类型、地区等 2. 网络配置：接入网络、认证信息等 3. 业务配置：业务规则、参数等
     *
     * @param id 机构ID
     * @return 机构详细信息
     */
    @Override
    public TransmissionOrganDTO getTransmissionOrgan(Integer id) {
        return TransmissionOrganConvert.INSTANCE.convert2DTO(organService.getTransmissionOrgan(id));
    }

    @Override
    public List<TransmissionOrganDTO> getTransmissionOrgans(List<Integer> ids) {

        List<TransmissionOrganDO> transmissionOrgans = organService.getTransmissionOrgans(ids);

        if (CollectionUtils.isEmpty(transmissionOrgans)) {
            return Lists.newArrayList();
        }

        return TransmissionOrganConvert.INSTANCE.convert2DTOList(transmissionOrgans);
    }
}
