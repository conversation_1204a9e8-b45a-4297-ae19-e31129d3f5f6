package com.xyy.saas.inquiry.user.server.bean;


import cn.iocoder.yudao.framework.tenant.core.context.TenantContextInfoProvider;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.xyy.saas.inquiry.pojo.TenantDto;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import java.util.Map;

import static com.xyy.saas.inquiry.constant.TenantConstant.TENANT_CONTEXT_KEY_TENANT_DTO;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Component
public class DefaultTenantContextInfoProvider implements TenantContextInfoProvider {

    @Resource
    private TenantApi tenantApi;

    /**
     * 获取租户相关信息
     *
     * @param tenantId 租户ID
     * @return 租户相关信息的键值对
     */
    @Override
    public Map<String, Object> getTenantInfo(Long tenantId) {
        TenantDto tenant = null;
        try {
            tenant = tenantApi.getTenant(tenantId);
        } catch (Throwable ignore) {}

        if (tenant == null) {
            return Map.of();
        }

        return Map.of(TENANT_CONTEXT_KEY_TENANT_DTO, tenant);
    }
}
