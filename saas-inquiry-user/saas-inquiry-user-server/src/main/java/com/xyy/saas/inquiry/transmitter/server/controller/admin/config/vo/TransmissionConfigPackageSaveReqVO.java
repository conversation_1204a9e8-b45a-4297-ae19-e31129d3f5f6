package com.xyy.saas.inquiry.transmitter.server.controller.admin.config.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

@Schema(description = "管理后台 - 协议配置包新增/修改 Request VO")
@Data
@Builder
public class TransmissionConfigPackageSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "29798")
    private Integer id;

    @Schema(description = "机构类型（1-医保、2-药监、3-互联网监管、4-ERP对接、5-HIS对接、99-其他）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "业务类型不能为空")
    private Integer organType;

    @Schema(description = "服务提供商名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    // @NotEmpty(message = "服务提供商名称不能为空")
    private String providerName;

    @Schema(description = "父节点id", example = "28667")
    private Integer parentPackageId;

    @Schema(description = "配置包名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "配置包名称不能为空")
    private String name;

    @Schema(description = "版本号（实际存储：**********；页面展示：服务提供商名称+机构类型名称+日期+小时，比如：创智医保20250122）", requiredMode = Schema.RequiredMode.REQUIRED)
    // @NotNull(message = "版本号（实际存储：**********；页面展示：服务提供商名称+机构类型名称+日期+小时，比如：创智医保20250122）不能为空")
    private Long version;

    @Schema(description = "描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    // @NotEmpty(message = "描述不能为空")
    private String description;

    @Schema(description = "是否启用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否启用不能为空")
    private Boolean disable;

}