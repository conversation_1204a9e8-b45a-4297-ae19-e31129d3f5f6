package com.xyy.saas.inquiry.transmitter.server.api.servicepack;

import static com.xyy.saas.inquiry.transmitter.enums.ErrorCodeConstants.TENANT_TRANSMISSION_SERVICE_PACK_NOT_EXISTS;
import static com.xyy.saas.inquiry.transmitter.enums.ErrorCodeConstants.TRANSMISSION_CONFIG_ITEM_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.xyy.saas.inquiry.transmitter.api.servicepack.TransmissionServicePackApi;
import com.xyy.saas.inquiry.transmitter.api.servicepack.dto.TenantTransmissionServicePackRespDTO;
import com.xyy.saas.inquiry.transmitter.api.config.dto.TransmissionConfigItemDTO;
import com.xyy.saas.inquiry.transmitter.api.config.dto.TransmissionConfigReqDTO;
import com.xyy.saas.inquiry.transmitter.api.servicepack.dto.TransmissionServicePackDTO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.servicepack.vo.TransmissionServicePackPageReqVO;
import com.xyy.saas.inquiry.transmitter.server.convert.servicepack.TransmissionServicePackConvert;
import com.xyy.saas.inquiry.transmitter.server.service.config.TransmissionConfigItemService;
import com.xyy.saas.inquiry.transmitter.server.service.servicepack.TransmissionServicePackService;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

/**
 * @Author:chenxiaoyi
 * @Date:2025/02/07 9:48
 */
@DubboService
@Slf4j
public class TransmissionServicePackApiImpl implements TransmissionServicePackApi {

    private static final ObjectMapper objectMapper = new ObjectMapper(new YAMLFactory()).configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);


    @Resource
    private TransmissionServicePackService servicePackService;

    @Resource
    private TransmissionConfigItemService configItemService;

    /**
     * 批量查询服务包信息
     * <p>
     * 查询内容： 1. 服务包基本信息 2. 关联的配置信息 3. 资源信息（动态库、模板等）
     *
     * @param servicePackIds 服务包ID列表
     * @return 服务包信息列表
     */
    @Override
    public List<TenantTransmissionServicePackRespDTO> selectServicePacksByIds(List<Integer> servicePackIds) {
        return servicePackService.selectList(TransmissionServicePackPageReqVO.builder().ids(servicePackIds).build());
    }


    @Override
    public List<TenantTransmissionServicePackRespDTO> selectServicePacks(TransmissionServicePackDTO packDTO) {
        return servicePackService.selectList(TransmissionServicePackConvert.INSTANCE.convert(packDTO));
    }

    /**
     * 查询租户服务包的配置项
     * <p>
     * 查询规则： 1. 根据业务节点类型匹配配置 2. 按配置类型过滤 3. 支持泛型返回结果
     *
     * @param reqDTO 查询条件（租户ID、业务节点、配置类型等）
     * @param clazz  返回数据类型
     * @return 配置项内容·
     */
    @Override
    public <T> CommonResult<T> selectConfigItem(TransmissionConfigReqDTO reqDTO, Class<T> clazz) {
        CommonResult<List<TransmissionConfigItemDTO>> configItem = getConfigItem(reqDTO);
        if (configItem.isError() || CollUtil.isEmpty(configItem.getData())) {
            return CommonResult.error(configItem);
        }
        // 创建最终合并的Map
        Map<String, Object> map = new HashMap<>();
        // 遍历每个配置项
        for (TransmissionConfigItemDTO item : configItem.getData()) {
            try {
                Map<String, Object> itemMap = objectMapper.readValue(item.getConfigValue(), Map.class);
                map.putAll(itemMap);
            } catch (JsonProcessingException e) {
                log.error("解析配置项 {} 失败", item.getId(), e);
            }
        }
        try {
            return CommonResult.success(JSON.parseObject(JSON.toJSONString(map), clazz));
        } catch (Exception e) {
            log.error("[selectConfigItem]configMap({})  解析配置项内容异常]", map, e);
        }
        return CommonResult.error("解析配置项内容异常");
    }


    private CommonResult<List<TransmissionConfigItemDTO>> getConfigItem(TransmissionConfigReqDTO reqDTO) {
        //1. 获取租户信息
        Long tenantId = Optional.ofNullable(reqDTO.getTenantId()).orElseGet(TenantContextHolder::getRequiredTenantId);

        //2. 查询服务包列表
        List<TransmissionServicePackDTO> servicePacks = servicePackService.queryTenantServicePackByNode(
            tenantId,
            reqDTO.getOrganType(),
            reqDTO.getNodeType(),
            reqDTO.getServicePackId(),
            false);

        //3. 校验服务包是否存在&配置项是否存在
        if (CollectionUtils.isEmpty(servicePacks)) {
            log.warn("[getConfigItem][tenantId({})  租户-开通服务包不存在]", tenantId);
            return CommonResult.error(TENANT_TRANSMISSION_SERVICE_PACK_NOT_EXISTS);
        }

        //4. 根据配置类型筛选配置项
        List<TransmissionConfigItemDTO> list = servicePacks.stream().map(servicePack -> servicePack.getConfigPackage().getConfigItems().stream()
            .filter(item -> !item.getDisable() && item.getDslType().equals(reqDTO.getDslType().getCode()))
            .findFirst()
            .orElse(null)).filter(Objects::nonNull).toList();

        if (CollUtil.isEmpty(list)) {
            return CommonResult.error(TRANSMISSION_CONFIG_ITEM_NOT_EXISTS);
        }
        return CommonResult.success(list);

        // TransmissionServicePackDTO servicePack = servicePacks.getFirst(); // 默认获取第一个服务包
        // TransmissionConfigItemDTO configItem = servicePack.getConfigPackage().getConfigItems().stream()
        //     .filter(item -> !item.getDisable() && item.getDslType().equals(reqDTO.getDslType().getCode()))
        //     .findFirst()
        //     .orElse(null);
        // if (configItem == null) {
        //     return CommonResult.error(TRANSMISSION_CONFIG_ITEM_NOT_EXISTS);
        // }
        // //5. 合并父配置
        // configItemService.setMergedConfigValue(configItem);
        // return CommonResult.success(configItem);
    }

}
