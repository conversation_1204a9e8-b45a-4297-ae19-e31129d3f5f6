package com.xyy.saas.inquiry.transmitter.server.service.dict;


import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.transmitter.enums.ErrorCodeConstants.SAAS_DICT_NOT_EXISTS;
import static com.xyy.saas.inquiry.transmitter.enums.ErrorCodeConstants.TRANSMISSION_PROVIDER_DICT_MATCH_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import cn.iocoder.yudao.module.system.api.dict.DictDataApi;
import cn.iocoder.yudao.module.system.api.dict.dto.DictDataRespDTO;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import com.xyy.saas.inquiry.enums.dict.DictMatchEnum;
import com.xyy.saas.inquiry.generic.api.InquiryGenericApiService;
import com.xyy.saas.inquiry.generic.api.dto.diagnosis.InquiryDiagnosisDto;
import com.xyy.saas.inquiry.transmitter.enums.DictTypeConstants;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictMatchGetReqVO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictMatchPageReqVO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictMatchRespVO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictMatchSaveReqVO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.organ.vo.TransmissionOrganPageReqVO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.organ.vo.TransmissionOrganRespVO;
import com.xyy.saas.inquiry.transmitter.server.convert.dict.TransmitterDictConvert;
import com.xyy.saas.inquiry.transmitter.server.convert.organ.TransmissionOrganConvert;
import com.xyy.saas.inquiry.transmitter.server.dal.dataobject.dict.TransmissionOrganDictDO;
import com.xyy.saas.inquiry.transmitter.server.dal.dataobject.dict.TransmissionOrganDictMatchDO;
import com.xyy.saas.inquiry.transmitter.server.dal.dataobject.organ.TransmissionOrganDO;
import com.xyy.saas.inquiry.transmitter.server.dal.mysql.dict.TransmissionOrganDictMapper;
import com.xyy.saas.inquiry.transmitter.server.dal.mysql.dict.TransmissionOrganDictMatchMapper;
import com.xyy.saas.inquiry.transmitter.server.dal.mysql.organ.TransmissionOrganMapper;
import com.xyy.saas.inquiry.util.UserUtil;
import com.xyy.saas.localserver.medicare.dsl.executor.value.DSLValue;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 服务商数据字典配对 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TransmissionOrganDictMatchServiceImpl implements TransmissionOrganDictMatchService {

    @Resource
    private TransmissionOrganDictMatchMapper transmissionOrganDictMatchMapper;

    @Resource
    private TransmissionOrganDictMapper transmissionOrganDictMapper;

    @Resource
    private DictDataApi dictDataApi;

    @Resource
    private AdminUserApi adminUserApi;


    @Resource
    private TransmissionOrganMapper organMapper;

    @Resource
    private InquiryGenericApiService inquiryGenericApiService;

    @Override
    public List<TransmissionOrganRespVO> listDictMatchOrgan(String dictType) {
        List<Integer> organIds = transmissionOrganDictMapper.queryOrganByDictType(dictType);
        if (CollUtil.isEmpty(organIds)) {
            return null;
        }
        // 查机构返回列表
        List<TransmissionOrganDO> organDOS = organMapper.selectList(TransmissionOrganPageReqVO.builder().ids(organIds).build());
        return TransmissionOrganConvert.INSTANCE.convert(organDOS);
    }

    /**
     * 为了 不连表，拆分成条件控制走不通的分页
     *
     * @param pageReqVO 分页查询
     * @return
     */
    @Override
    public PageResult<TransmissionOrganDictMatchRespVO> getTransmissionOrganDictMatchPage(TransmissionOrganDictMatchPageReqVO pageReqVO) {

        // 未匹配 || 无法匹配 还查三方字典名称，直接返回空
        if ((DictMatchEnum.isUnMatch(pageReqVO.getStatus()) || DictMatchEnum.isUnableMatch(pageReqVO.getStatus()))
            && StringUtils.isNotBlank(pageReqVO.getOrganDictName())) {
            return new PageResult<>();
        }

        if (DictTypeConstants.DIAGNOSIS_DICT.equals(pageReqVO.getDictType())) {
            return getTransmissionOrganDictMatchPageForDiagnosis(pageReqVO);
        }

        // 原有逻辑：处理其他字典类型
        if (StringUtils.isNotBlank(pageReqVO.getDictName())) {
            List<DictDataRespDTO> dictDataRespDTOS = dictDataApi.getDictDatas(null, pageReqVO.getDictType(), pageReqVO.getDictName());
            pageReqVO.setDictIds(CollectionUtils.convertList(dictDataRespDTOS, DictDataRespDTO::getId));
            if (CollUtil.isEmpty(pageReqVO.getDictIds())) {
                return new PageResult<>();
            }
        }
        if (StringUtils.isNotBlank(pageReqVO.getOrganDictName())) {
            List<TransmissionOrganDictDO> dictDataRespDTOS = transmissionOrganDictMapper.getDictDatas(pageReqVO.getOrganId(), pageReqVO.getDictType(), pageReqVO.getOrganDictName());
            pageReqVO.setOrganDictIds(CollectionUtils.convertList(dictDataRespDTOS, TransmissionOrganDictDO::getId));
            if (CollUtil.isEmpty(pageReqVO.getOrganDictIds())) {
                return new PageResult<>();
            }
        }
        // 查匹配表分页 ：  已匹配 || 无法匹配 || 有三方字典名
        if (DictMatchEnum.isMatch(pageReqVO.getStatus()) || DictMatchEnum.isUnableMatch(pageReqVO.getStatus()) || StringUtils.isNotBlank(pageReqVO.getOrganDictName())) {
            return getTransmissionOrganDictMatchPageResp(pageReqVO);
        }

        // 未匹配,全查match表后过滤
        if (DictMatchEnum.isUnMatch(pageReqVO.getStatus())) {
            List<TransmissionOrganDictMatchDO> matchDOS = transmissionOrganDictMatchMapper.selectByCondition(
                TransmissionOrganDictMatchPageReqVO.builder().organId(pageReqVO.getOrganId()).dictType(pageReqVO.getDictType()).build());
            pageReqVO.setNoDictIds(CollectionUtils.convertList(matchDOS, TransmissionOrganDictMatchDO::getDictId));
        }

        // 查saas字典表分页
        return getDictMatchPageRespVO(pageReqVO);
    }

    /**
     * 查saas字典表分页
     *
     * @param pageReqVO
     * @return
     */
    private PageResult<TransmissionOrganDictMatchRespVO> getDictMatchPageRespVO(TransmissionOrganDictMatchPageReqVO pageReqVO) {
        PageResult<DictDataRespDTO> dictDataPage = dictDataApi.getDictDataPage(TransmitterDictConvert.INSTANCE.convertPageVo(pageReqVO));
        if (dictDataPage == null || CollUtil.isEmpty(dictDataPage.getList())) {
            return new PageResult<>();
        }
        // 2.查 三方字典匹配表
        List<TransmissionOrganDictMatchDO> dictMatchDOS = transmissionOrganDictMatchMapper.selectByCondition(TransmissionOrganDictMatchPageReqVO.builder()
            .organId(pageReqVO.getOrganId()).dictType(pageReqVO.getDictType()).dictIds(dictDataPage.getList().stream().map(DictDataRespDTO::getId).toList()).build());
        // 3. 查三方字典
        Map<Long, TransmissionOrganDictDO> organDictDOMap =
            CollUtil.isEmpty(dictMatchDOS) ? new HashMap<>() : transmissionOrganDictMapper.selectBatchIds(dictMatchDOS.stream().map(TransmissionOrganDictMatchDO::getOrganDictId).toList())
                .stream().collect(Collectors.toMap(TransmissionOrganDictDO::getId, Function.identity(), (a, b) -> b));
        Map<Long, TransmissionOrganDictMatchDO> matchDOMap = dictMatchDOS.stream().collect(Collectors.toMap(TransmissionOrganDictMatchDO::getDictId, Function.identity(), (a, b) -> b));

        List<Integer> organIds = CollectionUtils.convertList(dictMatchDOS, TransmissionOrganDictMatchDO::getOrganId);
        Map<Integer, TransmissionOrganDO> organDOMap = CollUtil.isEmpty(organIds) ? new HashMap<>() : organMapper.selectList(TransmissionOrganPageReqVO.builder().ids(new ArrayList<>(organIds)).build())
            .stream().collect(Collectors.toMap(TransmissionOrganDO::getId, Function.identity(), (a, b) -> b));

        // 4.组装数据
        List<TransmissionOrganDictMatchRespVO> respVoList = dictDataPage.getList().stream().map(item -> {
            TransmissionOrganDictMatchDO matchDO = matchDOMap.get(item.getId());
            return TransmitterDictConvert.INSTANCE.convertMatchRespVO(matchDO == null ? null : organDOMap.get(matchDO.getOrganId()), item, matchDO, matchDO == null ? null : organDictDOMap.get(matchDO.getOrganDictId()));
        }).toList();
        UserUtil.fillUserInfo(respVoList, adminUserApi::getUserNameMap);

        return new PageResult<>(respVoList, dictDataPage.getTotal());
    }

    /**
     * 查匹配表分页
     *
     * @param pageReqVO
     * @return
     */
    private PageResult<TransmissionOrganDictMatchRespVO> getTransmissionOrganDictMatchPageResp(TransmissionOrganDictMatchPageReqVO pageReqVO) {
        // 1.查匹配表分页
        PageResult<TransmissionOrganDictMatchDO> dictDataPage = transmissionOrganDictMatchMapper.selectPage(pageReqVO);
        if (dictDataPage == null || CollUtil.isEmpty(dictDataPage.getList())) {
            return new PageResult<>();
        }
        List<Integer> organIds = CollectionUtils.convertList(dictDataPage.getList(), TransmissionOrganDictMatchDO::getOrganId);
        Map<Integer, TransmissionOrganDO> organDOMap = CollUtil.isEmpty(organIds) ? new HashMap<>() : organMapper.selectList(TransmissionOrganPageReqVO.builder().ids(new ArrayList<>(organIds)).build())
            .stream().collect(Collectors.toMap(TransmissionOrganDO::getId, Function.identity(), (a, b) -> b));
        // 2. 查三方字典
        Map<Long, TransmissionOrganDictDO> organDictDOMap = transmissionOrganDictMapper.selectBatchIds(dictDataPage.getList().stream().map(TransmissionOrganDictMatchDO::getOrganDictId).toList())
            .stream().collect(Collectors.toMap(TransmissionOrganDictDO::getId, Function.identity(), (a, b) -> b));
        // 3. 查saas字典
        Map<Long, DictDataRespDTO> dictDTOMap = dictDataApi.getDictDataList(null, CollectionUtils.convertList(dictDataPage.getList(), TransmissionOrganDictMatchDO::getDictId))
            .stream().collect(Collectors.toMap(DictDataRespDTO::getId, Function.identity(), (a, b) -> b));

        // 4.组装数据
        List<TransmissionOrganDictMatchRespVO> respVoList = dictDataPage.getList().stream()
            .map(item -> TransmitterDictConvert.INSTANCE.convertMatchRespVO(organDOMap.get(item.getOrganId()), dictDTOMap.get(item.getDictId()), item, organDictDOMap.get(item.getOrganDictId()))).toList();
        UserUtil.fillUserInfo(respVoList, adminUserApi::getUserNameMap);

        return new PageResult<>(respVoList, dictDataPage.getTotal());
    }

    @Override
    public void createTransmissionOrganDictMatch(TransmissionOrganDictMatchSaveReqVO createReqVO) {

        // 校验字典存在性
        if (DictTypeConstants.DIAGNOSIS_DICT.equals(createReqVO.getDictType())) {
            // 校验诊断字典
            List<InquiryDiagnosisDto> diagnosisList = inquiryGenericApiService.getDiagnosisByIds(Collections.singletonList(createReqVO.getDictId()));
            if (CollUtil.isEmpty(diagnosisList)) {
                throw exception(SAAS_DICT_NOT_EXISTS);
            }
        } else {
            // 校验其他类型字典
            List<DictDataRespDTO> dictDataList = dictDataApi.getDictDataList(null, Collections.singletonList(createReqVO.getDictId()));
            if (CollUtil.isEmpty(dictDataList)) {
                throw exception(SAAS_DICT_NOT_EXISTS);
            }
        }

        // 1.查询匹配表数据
        List<TransmissionOrganDictMatchDO> matchDOS = transmissionOrganDictMatchMapper.selectByCondition(
            TransmissionOrganDictMatchPageReqVO.builder().organId(createReqVO.getOrganId()).dictType(createReqVO.getDictType()).dictId(createReqVO.getDictId()).build());
        // 为空新增匹配关系
        if (CollUtil.isEmpty(matchDOS)) {
            TransmissionOrganDictMatchDO dictMatchDO = TransmitterDictConvert.INSTANCE.convertMatchSaveVO(createReqVO);
            dictMatchDO.setOrganDictId(DictMatchEnum.isUnableMatch(createReqVO.getStatus()) ? null : createReqVO.getOrganDictId());
            transmissionOrganDictMatchMapper.insert(dictMatchDO);
            return;
        }
        // 批量更新状态和关系
        transmissionOrganDictMatchMapper.updateMatch(CollectionUtils.convertList(matchDOS, TransmissionOrganDictMatchDO::getId), DictMatchEnum.isUnableMatch(createReqVO.getStatus()) ? null : createReqVO.getOrganDictId(),
            createReqVO.getStatus(), WebFrameworkUtils.getLoginUserId());
    }


    @Override
    public TransmissionOrganDictMatchRespVO getTransmissionOrganDictMatch(TransmissionOrganDictMatchGetReqVO getReqVO) {
        if (DictTypeConstants.DIAGNOSIS_DICT.equals(getReqVO.getDictType())) {
            // 校验诊断字典
            List<InquiryDiagnosisDto> diagnosisList = inquiryGenericApiService.getDiagnosisByIds(Collections.singletonList(getReqVO.getDictId()));
            if (CollUtil.isEmpty(diagnosisList)) {
                throw exception(SAAS_DICT_NOT_EXISTS);
            }

            // 查匹配
            List<TransmissionOrganDictMatchDO> matchDOS = transmissionOrganDictMatchMapper.selectByCondition(
                TransmissionOrganDictMatchPageReqVO.builder().organId(getReqVO.getOrganId()).dictType(getReqVO.getDictType()).dictId(getReqVO.getDictId()).build());

            DictDataRespDTO dictDataRespDTO = TransmitterDictConvert.INSTANCE.convertDictDto(diagnosisList.getFirst());

            if (CollUtil.isEmpty(matchDOS)) {
                return TransmitterDictConvert.INSTANCE.convertMatchRespVO(null, dictDataRespDTO, null, null);
            }

            // 组装三方字典
            TransmissionOrganDictMatchDO matchDO = matchDOS.getFirst();
            TransmissionOrganDO organDO = organMapper.selectById(matchDO.getOrganId());
            TransmissionOrganDictDO organDictDO = matchDO.getOrganDictId() == null ? null : transmissionOrganDictMapper.selectById(matchDO.getOrganDictId());

            TransmissionOrganDictMatchRespVO respVO = TransmitterDictConvert.INSTANCE.convertMatchRespVO(organDO, dictDataRespDTO, matchDO, organDictDO);
            UserUtil.fillUserInfo(Collections.singletonList(respVO), adminUserApi::getUserNameMap);
            return respVO;
        }

        // 原有逻辑：处理其他字典类型
        List<DictDataRespDTO> dictDataList = dictDataApi.getDictDataList(null, Collections.singletonList(getReqVO.getDictId()));
        if (CollUtil.isEmpty(dictDataList)) {
            throw exception(SAAS_DICT_NOT_EXISTS);
        }
        // 查匹配
        List<TransmissionOrganDictMatchDO> matchDOS = transmissionOrganDictMatchMapper.selectByCondition(
            TransmissionOrganDictMatchPageReqVO.builder().organId(getReqVO.getOrganId()).dictType(getReqVO.getDictType()).dictId(getReqVO.getDictId()).build());
        if (CollUtil.isEmpty(matchDOS)) {
            return TransmitterDictConvert.INSTANCE.convertMatchRespVO(null, dictDataList.getFirst(), null, null);
        }
        // 组装三方字典
        TransmissionOrganDictMatchDO matchDO = matchDOS.getFirst();
        TransmissionOrganDictDO dictDO = matchDO.getOrganDictId() == null ? null : transmissionOrganDictMapper.selectById(matchDO.getOrganDictId());
        return TransmitterDictConvert.INSTANCE.convertMatchRespVO(null, dictDataList.getFirst(), matchDO, dictDO);
    }


    @Override
    public void deleteTransmissionOrganDictMatch(Long id) {
        // 校验存在
        validateTransmissionProviderDictMatchExists(id);
        // 删除
        transmissionOrganDictMatchMapper.deleteById(id);
    }


    @Override
    public TransmissionOrganDictDO getOrganDictValue(DSLValue dslValue, String dictType, String dictValue) {
        DictDataRespDTO dictData = dictDataApi.getDictData(dslValue.getTenantId(), dictType, dictValue);
        if (dictData == null) {
            return null;
        }
        TransmissionOrganDictMatchDO dictMatchDO = transmissionOrganDictMatchMapper.selectByCondition(TransmissionOrganDictMatchPageReqVO.builder()
                .organId(dslValue.getOrganId()).dictType(dictType).dictId(dictData.getId()).build())
            .stream().filter(m -> m.getOrganDictId() != null).findFirst().orElse(null);
        if (dictMatchDO == null) {
            return null;
        }
        return transmissionOrganDictMapper.selectById(dictMatchDO.getOrganDictId());
    }

    private void validateTransmissionProviderDictMatchExists(Long id) {
        if (transmissionOrganDictMatchMapper.selectById(id) == null) {
            throw exception(TRANSMISSION_PROVIDER_DICT_MATCH_NOT_EXISTS);
        }
    }

    /**
     * 处理diagnosis_dict类型的分页查询
     */
    private PageResult<TransmissionOrganDictMatchRespVO> getTransmissionOrganDictMatchPageForDiagnosis(TransmissionOrganDictMatchPageReqVO pageReqVO) {
        // 处理诊断名称模糊查询
        if (StringUtils.isNotBlank(pageReqVO.getDictName())) {
            List<InquiryDiagnosisDto> diagnosisList = inquiryGenericApiService.getDiagnosisByName(pageReqVO.getDictName());
            pageReqVO.setDictIds(CollectionUtils.convertList(diagnosisList, InquiryDiagnosisDto::getId));
            if (CollUtil.isEmpty(pageReqVO.getDictIds())) {
                return new PageResult<>();
            }
        }

        if (StringUtils.isNotBlank(pageReqVO.getOrganDictName())) {
            List<TransmissionOrganDictDO> dictDataRespDTOS = transmissionOrganDictMapper.getDictDatas(pageReqVO.getOrganId(), pageReqVO.getDictType(), pageReqVO.getOrganDictName());
            pageReqVO.setOrganDictIds(CollectionUtils.convertList(dictDataRespDTOS, TransmissionOrganDictDO::getId));
            if (CollUtil.isEmpty(pageReqVO.getOrganDictIds())) {
                return new PageResult<>();
            }
        }

        // 查匹配表分页 ：  已匹配 || 无法匹配 || 有三方字典名
        if (DictMatchEnum.isMatch(pageReqVO.getStatus()) || DictMatchEnum.isUnableMatch(pageReqVO.getStatus()) || StringUtils.isNotBlank(pageReqVO.getOrganDictName())) {
            return getTransmissionOrganDictMatchPageRespForDiagnosis(pageReqVO);
        }

        // 未匹配,全查match表后过滤
        if (DictMatchEnum.isUnMatch(pageReqVO.getStatus())) {
            List<TransmissionOrganDictMatchDO> matchDOS = transmissionOrganDictMatchMapper.selectByCondition(
                TransmissionOrganDictMatchPageReqVO.builder().organId(pageReqVO.getOrganId()).dictType(pageReqVO.getDictType()).build());
            pageReqVO.setNoDictIds(CollectionUtils.convertList(matchDOS, TransmissionOrganDictMatchDO::getDictId));
        }

        // 查诊断字典表分页
        return getDictMatchPageRespVOForDiagnosis(pageReqVO);
    }

    /**
     * 查诊断字典表分页（针对diagnosis_dict类型）
     */
    private PageResult<TransmissionOrganDictMatchRespVO> getDictMatchPageRespVOForDiagnosis(TransmissionOrganDictMatchPageReqVO pageReqVO) {
        // 构建诊断查询参数
        InquiryDiagnosisDto diagnosisPageReqDto = new InquiryDiagnosisDto();
        diagnosisPageReqDto.setPageNo(pageReqVO.getPageNo());
        diagnosisPageReqDto.setPageSize(pageReqVO.getPageSize());
        diagnosisPageReqDto.setStatus(cn.iocoder.yudao.framework.common.enums.CommonStatusEnum.ENABLE.getStatus());
        diagnosisPageReqDto.setNoIds(pageReqVO.getNoDictIds());
        diagnosisPageReqDto.setIds(pageReqVO.getDictIds());
        // 如果有dictName查询条件，设置诊断名称模糊查询
        // if (StringUtils.isNotBlank(pageReqVO.getDictName())) {
        //     diagnosisPageReqDto.setShowName(pageReqVO.getDictName());
        // }

        PageResult<InquiryDiagnosisDto> diagnosisPage = inquiryGenericApiService.getDiagnosisPage(diagnosisPageReqDto);
        if (diagnosisPage == null || CollUtil.isEmpty(diagnosisPage.getList())) {
            return new PageResult<>();
        }

        // 查询匹配表数据
        List<TransmissionOrganDictMatchDO> dictMatchDOS = transmissionOrganDictMatchMapper.selectByCondition(
            TransmissionOrganDictMatchPageReqVO.builder()
                .organId(pageReqVO.getOrganId())
                .dictType(pageReqVO.getDictType())
                .dictIds(diagnosisPage.getList().stream().map(InquiryDiagnosisDto::getId).toList())
                .build());

        // 查询三方字典
        List<Long> organDictIds = dictMatchDOS.stream().map(TransmissionOrganDictMatchDO::getOrganDictId).filter(java.util.Objects::nonNull).toList();
        Map<Long, TransmissionOrganDictDO> organDictDOMap = CollUtil.isEmpty(organDictIds) ? new HashMap<>() :
            transmissionOrganDictMapper.selectBatchIds(organDictIds).stream().collect(Collectors.toMap(TransmissionOrganDictDO::getId, Function.identity(), (a, b) -> b));

        Map<Long, TransmissionOrganDictMatchDO> matchDOMap = dictMatchDOS.stream()
            .collect(Collectors.toMap(TransmissionOrganDictMatchDO::getDictId, Function.identity(), (a, b) -> b));

        List<Integer> organIds = CollectionUtils.convertList(dictMatchDOS, TransmissionOrganDictMatchDO::getOrganId);
        Map<Integer, TransmissionOrganDO> organDOMap = CollUtil.isEmpty(organIds) ? new HashMap<>() :
            organMapper.selectList(TransmissionOrganPageReqVO.builder().ids(new ArrayList<>(organIds)).build())
                .stream().collect(Collectors.toMap(TransmissionOrganDO::getId, Function.identity(), (a, b) -> b));

        // 组装数据
        List<TransmissionOrganDictMatchRespVO> respVoList = diagnosisPage.getList().stream().map(item -> {
            TransmissionOrganDictMatchDO matchDO = matchDOMap.get(item.getId());
            // 将InquiryDiagnosisDto转换为DictDataRespDTO格式
            DictDataRespDTO dictDataRespDTO = new DictDataRespDTO();
            dictDataRespDTO.setId(item.getId());
            dictDataRespDTO.setLabel(item.getShowName());
            dictDataRespDTO.setValue(item.getDiagnosisCode());
            dictDataRespDTO.setDictType(DictTypeConstants.DIAGNOSIS_DICT);

            return TransmitterDictConvert.INSTANCE.convertMatchRespVO(
                matchDO == null ? null : organDOMap.get(matchDO.getOrganId()),
                dictDataRespDTO,
                matchDO,
                matchDO == null ? null : organDictDOMap.get(matchDO.getOrganDictId())
            );
        }).toList();

        UserUtil.fillUserInfo(respVoList, adminUserApi::getUserNameMap);
        return new PageResult<>(respVoList, diagnosisPage.getTotal());
    }

    /**
     * 查匹配表分页（针对diagnosis_dict类型）
     */
    private PageResult<TransmissionOrganDictMatchRespVO> getTransmissionOrganDictMatchPageRespForDiagnosis(TransmissionOrganDictMatchPageReqVO pageReqVO) {
        // 查匹配表分页
        PageResult<TransmissionOrganDictMatchDO> dictDataPage = transmissionOrganDictMatchMapper.selectPage(pageReqVO);
        if (dictDataPage == null || CollUtil.isEmpty(dictDataPage.getList())) {
            return new PageResult<>();
        }

        // 查三方字典
        List<Long> organDictIds = dictDataPage.getList().stream()
            .map(TransmissionOrganDictMatchDO::getOrganDictId)
            .filter(java.util.Objects::nonNull)
            .toList();
        Map<Long, TransmissionOrganDictDO> organDictDOMap = CollUtil.isEmpty(organDictIds) ? new HashMap<>() :
            transmissionOrganDictMapper.selectBatchIds(organDictIds)
                .stream().collect(Collectors.toMap(TransmissionOrganDictDO::getId, Function.identity(), (a, b) -> b));

        List<Integer> organIds = CollectionUtils.convertList(dictDataPage.getList(), TransmissionOrganDictMatchDO::getOrganId);
        Map<Integer, TransmissionOrganDO> organDOMap = CollUtil.isEmpty(organIds) ? new HashMap<>() :
            organMapper.selectList(TransmissionOrganPageReqVO.builder().ids(new ArrayList<>(organIds)).build())
                .stream().collect(Collectors.toMap(TransmissionOrganDO::getId, Function.identity(), (a, b) -> b));

        // 查诊断字典数据
        List<Long> dictIds = CollectionUtils.convertList(dictDataPage.getList(), TransmissionOrganDictMatchDO::getDictId);
        List<InquiryDiagnosisDto> diagnosisList = inquiryGenericApiService.getDiagnosisByIds(dictIds);
        Map<Long, InquiryDiagnosisDto> diagnosisMap = diagnosisList.stream()
            .collect(Collectors.toMap(InquiryDiagnosisDto::getId, Function.identity(), (a, b) -> b));

        // 组装数据
        List<TransmissionOrganDictMatchRespVO> respVoList = dictDataPage.getList().stream()
            .map(item -> {
                InquiryDiagnosisDto diagnosis = diagnosisMap.get(item.getDictId());
                // 将InquiryDiagnosisDto转换为DictDataRespDTO格式
                DictDataRespDTO dictDataRespDTO = null;
                if (diagnosis != null) {
                    dictDataRespDTO = new DictDataRespDTO();
                    dictDataRespDTO.setId(diagnosis.getId());
                    dictDataRespDTO.setLabel(diagnosis.getShowName());
                    dictDataRespDTO.setValue(diagnosis.getDiagnosisCode());
                    dictDataRespDTO.setDictType(DictTypeConstants.DIAGNOSIS_DICT);
                }

                return TransmitterDictConvert.INSTANCE.convertMatchRespVO(
                    organDOMap.get(item.getOrganId()),
                    dictDataRespDTO,
                    item,
                    organDictDOMap.get(item.getOrganDictId())
                );
            }).toList();

        UserUtil.fillUserInfo(respVoList, adminUserApi::getUserNameMap);
        return new PageResult<>(respVoList, dictDataPage.getTotal());
    }

    @Override
    public List<TransmissionOrganDictMatchDO> getTransmissionDictMatch(TransmissionOrganDictMatchGetReqVO getReqVO) {
        TransmissionOrganDictMatchPageReqVO matchPageReqVO = TransmitterDictConvert.INSTANCE.convertMatchReqVO(getReqVO);
        return transmissionOrganDictMatchMapper.selectByCondition(matchPageReqVO);
    }
}