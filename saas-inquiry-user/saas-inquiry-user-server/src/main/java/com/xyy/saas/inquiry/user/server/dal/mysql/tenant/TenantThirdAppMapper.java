package com.xyy.saas.inquiry.user.server.dal.mysql.tenant;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.user.server.controller.admin.tenant.vo.TenantThirdAppPageReqVO;
import com.xyy.saas.inquiry.user.server.dal.dataobject.tenant.TenantThirdAppDO;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

@Mapper
public interface TenantThirdAppMapper extends BaseMapperX<TenantThirdAppDO> {

    default PageResult<TenantThirdAppDO> selectPage(TenantThirdAppPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TenantThirdAppDO>()
                .eqIfPresent(TenantThirdAppDO::getTenantId, reqVO.getTenantId())
                .likeIfPresent(TenantThirdAppDO::getAppName, reqVO.getAppName())
                .eqIfPresent(TenantThirdAppDO::getStatus, reqVO.getStatus())
                .orderByDesc(TenantThirdAppDO::getId));
    }

    default TenantThirdAppDO selectByAppKey(String appKey) {
        return selectOne(TenantThirdAppDO::getAppKey, appKey);
    }

    default List<TenantThirdAppDO> queryByCondition(TenantThirdAppPageReqVO reqVO) {

        List<TenantThirdAppDO> tenantThirdAppDOS = selectList(new LambdaQueryWrapperX<TenantThirdAppDO>()
            .eq(TenantThirdAppDO::getDeleted, false)
            .inIfPresent(TenantThirdAppDO::getId, reqVO.getIdList())
            .eqIfPresent(TenantThirdAppDO::getTenantId, reqVO.getTenantId())
            .inIfPresent(TenantThirdAppDO::getTenantId, reqVO.getTenantIdList())
            .eqIfPresent(TenantThirdAppDO::getTransmissionOrganId, reqVO.getTransmissionOrganId())
            .eqIfPresent(TenantThirdAppDO::getStatus, reqVO.getStatus())
            .orderByDesc(TenantThirdAppDO::getId));

        if (CollUtil.isEmpty(tenantThirdAppDOS)) {
            return Lists.newArrayList();
        }

        return tenantThirdAppDOS;
    }
}