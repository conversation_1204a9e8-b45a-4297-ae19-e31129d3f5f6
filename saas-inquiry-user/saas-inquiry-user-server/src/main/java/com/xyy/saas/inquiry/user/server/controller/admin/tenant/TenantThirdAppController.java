package com.xyy.saas.inquiry.user.server.controller.admin.tenant;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.user.server.controller.admin.tenant.vo.TenantThirdAppPageReqVO;
import com.xyy.saas.inquiry.user.server.controller.admin.tenant.vo.TenantThirdAppRespVO;
import com.xyy.saas.inquiry.user.server.controller.admin.tenant.vo.TenantThirdAppSaveReqVO;
import com.xyy.saas.inquiry.user.server.dal.dataobject.tenant.TenantThirdAppDO;
import com.xyy.saas.inquiry.user.server.service.tenant.TenantThirdAppService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Tag(name = "管理后台 - 门店三方应用配置")
@RestController
@RequestMapping("/system/tenant/third-app")
@Validated
public class TenantThirdAppController {

    @Resource
    private TenantThirdAppService tenantThirdAppService;

    @PostMapping("/create")
    @Operation(summary = "创建门店三方应用配置")
    @PreAuthorize("@ss.hasPermission('saas:tenant:third-app:create')")
    public CommonResult<Long> createThirdApp(@Valid @RequestBody TenantThirdAppSaveReqVO createReqVO) {
        return success(tenantThirdAppService.saveOrUpdateThirdApp(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新门店三方应用配置")
    @PreAuthorize("@ss.hasPermission('saas:tenant:third-app:update')")
    public CommonResult<Boolean> updateThirdApp(@Valid @RequestBody TenantThirdAppSaveReqVO updateReqVO) {
        tenantThirdAppService.saveOrUpdateThirdApp(updateReqVO);
        return success(true);
    }

    @PutMapping("/reset")
    @Operation(summary = "重置门店三方应用配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:tenant:third-app:update')")
    public CommonResult<Boolean> resetThirdApp(@RequestParam("id") Long id) {
        tenantThirdAppService.resetThirdApp(id);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除门店三方应用配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:tenant:third-app:delete')")
    public CommonResult<Boolean> deleteThirdApp(@RequestParam("id") Long id) {
        tenantThirdAppService.deleteThirdApp(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得门店三方应用配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:tenant:third-app:query')")
    public CommonResult<TenantThirdAppRespVO> getThirdApp(@RequestParam("id") Long id) {
        TenantThirdAppDO thirdApp = tenantThirdAppService.getThirdApp(id);
        return success(BeanUtils.toBean(thirdApp, TenantThirdAppRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得门店三方应用配置分页")
    @PreAuthorize("@ss.hasPermission('saas:tenant:third-app:query')")
    public CommonResult<PageResult<TenantThirdAppRespVO>> getThirdAppPage(@Valid TenantThirdAppPageReqVO pageVO) {
        PageResult<TenantThirdAppDO> pageResult = tenantThirdAppService.getThirdAppPage(pageVO);
        return success(BeanUtils.toBean(pageResult, TenantThirdAppRespVO.class));
    }

    @PutMapping("/update-status")
    @Operation(summary = "修改门店三方应用状态")
    @PreAuthorize("@ss.hasPermission('saas:tenant:third-app:update')")
    public CommonResult<Boolean> updateThirdAppStatus(@RequestParam("id") Long id,
            @RequestParam("status") Integer status) {
        tenantThirdAppService.updateThirdAppStatus(id, status);
        return success(true);
    }

} 