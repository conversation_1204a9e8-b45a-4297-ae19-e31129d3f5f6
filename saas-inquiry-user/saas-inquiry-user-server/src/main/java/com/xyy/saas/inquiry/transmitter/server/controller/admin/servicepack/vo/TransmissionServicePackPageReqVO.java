package com.xyy.saas.inquiry.transmitter.server.controller.admin.servicepack.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 服务包分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
public class TransmissionServicePackPageReqVO extends PageParam {

    /**
     * 服务包ids
     */
    private List<Integer> ids;

    @Schema(description = "服务包名称", example = "张三")
    private String name;

    @Schema(description = "医药行业行政机构ID", example = "12781")
    private Integer organId;

    @Schema(description = "机构类型（1-医保、2-药监、3-互联网监管、4-ERP对接、5-HIS对接、99-其他）", example = "1")
    private Integer organType;

    @Schema(description = "省份编码", example = "330000")
    private String provinceCode;

    @Schema(description = "省份")
    private String province;

    @Schema(description = "城市编码", example = "330100")
    private String cityCode;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "区域编码", example = "330106")
    private String areaCode;

    @Schema(description = "区域")
    private String area;

    @Schema(description = "协议配置包id", example = "8583")
    private Integer configPackageId;

    @Schema(description = "动态库资源")
    private String dllResource;

    @Schema(description = "小票模板资源")
    private String ticketResource;

    @Schema(description = "账单模板资源")
    private String billResource;

    @Schema(description = "拓展资源")
    private String extResource;

    @Schema(description = "接口文档")
    private String apiDoc;

    @Schema(description = "版本号（实际存储：2025012209；页面展示：医药行业行政机构名称+服务提供商名称+机构类型名称+日期+小时，比如：陕西省医保局创智医保20250122；）")
    private Long version;

    @Schema(description = "环境：0-测试；1-灰度；2-上线；")
    private Integer env;

    @Schema(description = "是否禁用")
    private Boolean disable;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}