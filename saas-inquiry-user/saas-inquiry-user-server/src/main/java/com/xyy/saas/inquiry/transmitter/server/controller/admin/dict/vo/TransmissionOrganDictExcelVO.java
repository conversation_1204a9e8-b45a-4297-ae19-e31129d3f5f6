package com.xyy.saas.inquiry.transmitter.server.controller.admin.dict.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.pojo.excel.ImportExcelVoDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 用户 Excel 导入 VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免用户导入有问题
public class TransmissionOrganDictExcelVO extends ImportExcelVoDto {


    @ExcelProperty("字典标签")
    private String label;

    @ExcelProperty("字典值")
    private String value;

    @ExcelProperty("父字典值")
    private String parentValue;

    @ExcelProperty("字典外码")
    private String outerValue;

    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.COMMON_STATUS)
    private Integer status;

    @ExcelProperty(value = "末级节点")
    private Integer endNode;

    @ExcelProperty("排序")
    private Integer sort;

    @ExcelProperty("备注")
    private String remark;

    // 失败原因
    private String errMsg;


}
