package com.xyy.saas.inquiry.generic.api.dto.hospital;

import com.xyy.saas.inquiry.pojo.HospitalDeptDto.Dept;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @see com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalSettingDto
 */
@Data
@Accessors(chain = true)
public class InquiryHospitalSettingDto implements Serializable {

    /**
     * 西成药问诊默认科室
     */
    private List<Dept> defaultInquiryWesternMedicineDept;
    /**
     * 中草药问诊默认科室
     */
    private List<Dept> defaultInquiryChineseMedicineDept;
    /**
     * 默认处方笺模板（西成药）ID
     */
    private Long defaultWesternPrescriptionTemplate;
    /**
     * 默认处方笺模板（中草药）ID
     */
    private Long defaultChinesePrescriptionTemplate;
    /**
     * 扩展配置
     */
    private InquiryHospitalSettingExtDto extend;


}
