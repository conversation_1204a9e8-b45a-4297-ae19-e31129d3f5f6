package com.xyy.saas.inquiry.transmitter.server.api.dict;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.enums.dict.DictMatchEnum;
import com.xyy.saas.inquiry.transmitter.api.dict.TransmissionOrganDictApi;
import com.xyy.saas.inquiry.transmitter.api.dict.dto.TransmissionOrganDictDTO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictMatchGetReqVO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictPageReqVO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictRespVO;
import com.xyy.saas.inquiry.transmitter.server.convert.dict.TransmitterDictConvert;
import com.xyy.saas.inquiry.transmitter.server.dal.dataobject.dict.TransmissionOrganDictDO;
import com.xyy.saas.inquiry.transmitter.server.dal.dataobject.dict.TransmissionOrganDictMatchDO;
import com.xyy.saas.inquiry.transmitter.server.service.dict.TransmissionOrganDictMatchService;
import com.xyy.saas.inquiry.transmitter.server.service.dict.TransmissionOrganDictService;
import jakarta.annotation.Resource;
import java.util.List;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * 服务商字典 API 实现类
 *
 * @Author:chenxiaoyi
 * @Date:2025/07/17 15:48
 */
@DubboService
public class TransmissionOrganDictApiImpl implements TransmissionOrganDictApi {

    @Resource
    private TransmissionOrganDictService transmissionOrganDictService;

    @Resource
    private TransmissionOrganDictMatchService transmissionOrganDictMatchService;

    @Override
    public List<TransmissionOrganDictDTO> queryDictList(TransmissionOrganDictDTO transmissionOrganDictDTO) {
        if (transmissionOrganDictDTO == null) {
            return List.of();
        }
        TransmissionOrganDictPageReqVO pageReqVO = TransmitterDictConvert.INSTANCE.convertToPageReqVO(transmissionOrganDictDTO);

        List<TransmissionOrganDictDO> transmissionDict = transmissionOrganDictService.getTransmissionDict(pageReqVO);

        return TransmitterDictConvert.INSTANCE.convertDo2Dtos(transmissionDict);
    }

    @Override
    public PageResult<TransmissionOrganDictDTO> queryDictPage(TransmissionOrganDictDTO pageReqDTO) {
        if (pageReqDTO == null) {
            return PageResult.empty();
        }

        // 构建查询条件
        TransmissionOrganDictPageReqVO pageReqVO = TransmitterDictConvert.INSTANCE.convertToPageReqVO(pageReqDTO);

        // 调用Service层分页查询
        PageResult<TransmissionOrganDictRespVO> pageResult = transmissionOrganDictService.getTransmissionProviderDictPage(pageReqVO);

        if (CollUtil.isEmpty(pageResult.getList())) {
            return new PageResult<>(List.of(), pageResult.getTotal());
        }
        return new PageResult<>(TransmitterDictConvert.INSTANCE.convertDtos(pageResult.getList()), pageResult.getTotal());
    }

    @Override
    public List<Long> queryMatchSaasDictByValues(TransmissionOrganDictDTO transmissionOrganDictDTO) {

        List<TransmissionOrganDictDO> transmissionDicts = transmissionOrganDictService.getTransmissionDict(TransmitterDictConvert.INSTANCE.convertToPageReqVO(transmissionOrganDictDTO));

        if (CollUtil.isEmpty(transmissionDicts)) {
            return List.of();
        }

        TransmissionOrganDictMatchGetReqVO matchGetReqVO = TransmissionOrganDictMatchGetReqVO.builder().organId(transmissionOrganDictDTO.getOrganId())
            .dictType(transmissionOrganDictDTO.getDictType())
            .organDictIds(transmissionDicts.stream().map(TransmissionOrganDictDO::getId).toList())
            .status(DictMatchEnum.MATCH.getCode())
            .build();

        List<TransmissionOrganDictMatchDO> dictMatchs = transmissionOrganDictMatchService.getTransmissionDictMatch(matchGetReqVO);

        return dictMatchs.stream().map(TransmissionOrganDictMatchDO::getDictId).toList();
    }

    @Override
    public List<TransmissionOrganDictDTO> queryMatchSaasDictByDictIds(TransmissionOrganDictDTO transmissionOrganDictDTO) {

        TransmissionOrganDictMatchGetReqVO matchGetReqVO = TransmissionOrganDictMatchGetReqVO.builder().organId(transmissionOrganDictDTO.getOrganId())
            .dictType(transmissionOrganDictDTO.getDictType())
            .dictIds(transmissionOrganDictDTO.getDictIds())
            .status(DictMatchEnum.MATCH.getCode())
            .build();

        List<TransmissionOrganDictMatchDO> dictMatchs = transmissionOrganDictMatchService.getTransmissionDictMatch(matchGetReqVO);

        if (CollUtil.isEmpty(dictMatchs)) {
            return List.of();
        }

        TransmissionOrganDictPageReqVO reqVO = TransmitterDictConvert.INSTANCE.convertToPageReqVO(transmissionOrganDictDTO);

        reqVO.setIds(dictMatchs.stream().map(TransmissionOrganDictMatchDO::getOrganDictId).toList());

        List<TransmissionOrganDictDO> transmissionDicts = transmissionOrganDictService.getTransmissionDict(reqVO);

        return TransmitterDictConvert.INSTANCE.convertDo2Dtos(transmissionDicts);
    }
}