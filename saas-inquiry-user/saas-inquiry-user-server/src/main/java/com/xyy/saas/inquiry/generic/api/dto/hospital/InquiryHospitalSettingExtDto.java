package com.xyy.saas.inquiry.generic.api.dto.hospital;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * @see com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalSettingExtDto
 */
@Data
@Accessors(chain = true)
public class InquiryHospitalSettingExtDto implements Serializable {

    /**
     * 特定处方笺
     */
    private List<InquiryHospitalSpecificPrescriptionTemplateDto> specificPrescriptionTemplates;

    /**
     * 特定CA
     */
    private List<InquiryHospitalSpecificPrescriptionCaDto> specificPrescriptionCas;
}
