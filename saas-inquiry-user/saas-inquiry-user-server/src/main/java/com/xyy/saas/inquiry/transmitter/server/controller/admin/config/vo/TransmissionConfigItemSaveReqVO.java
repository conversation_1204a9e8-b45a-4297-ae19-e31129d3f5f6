package com.xyy.saas.inquiry.transmitter.server.controller.admin.config.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 协议配置新增/修改 Request VO")
@Data
public class TransmissionConfigItemSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15565")
    private Integer id;

    @Schema(description = "节点名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "节点名称")
    private String description;

    @Schema(description = "协议配置包ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31380")
    @NotNull(message = "协议配置包ID不能为空")
    private Integer configPackageId;

    @Schema(description = "父节点id", example = "16154")
    private Integer parentItemId;

    @Schema(description = "配置类型（1-视图配置、2-逻辑配置、3-协议配置）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "配置类型（1-视图配置、2-逻辑配置、3-协议配置）不能为空")
    private Integer dslType;

    @Schema(description = "节点业务类型（比如:1-药监-日结存、2-药监-商品信息...）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "节点业务类型（比如:1-药监-日结存、2-药监-商品信息...）不能为空")
    private Integer nodeType;

    @Schema(description = "接口编码", example = "3501")
    private String apiCode;


    @Schema(description = "配置值,yaml")
    private String configValue;

    @Schema(description = "是否禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否禁用不能为空")
    private Boolean disable;

}