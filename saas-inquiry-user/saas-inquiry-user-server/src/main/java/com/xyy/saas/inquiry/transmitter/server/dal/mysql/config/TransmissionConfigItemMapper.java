package com.xyy.saas.inquiry.transmitter.server.dal.mysql.config;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.pojo.CommonGroupStatisticsDto;
import java.util.List;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.config.vo.TransmissionConfigItemPageReqVO;
import com.xyy.saas.inquiry.transmitter.server.dal.dataobject.config.TransmissionConfigItemDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 协议配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TransmissionConfigItemMapper extends BaseMapperX<TransmissionConfigItemDO> {

    /**
     * 构建配置项查询条件
     */
    default LambdaQueryWrapperX<TransmissionConfigItemDO> buildConfigItemWrapper(TransmissionConfigItemPageReqVO reqVO) {
        final LambdaQueryWrapperX<TransmissionConfigItemDO> wrapperX = new LambdaQueryWrapperX<TransmissionConfigItemDO>()
            .inIfPresent(TransmissionConfigItemDO::getConfigPackageId, reqVO.getConfigPackageIds())
            .eqIfPresent(TransmissionConfigItemDO::getConfigPackageId, reqVO.getConfigPackageId())
            .eqIfPresent(TransmissionConfigItemDO::getParentItemId, reqVO.getParentItemId())
            .eqIfPresent(TransmissionConfigItemDO::getDslType, reqVO.getDslType())
            .inIfPresent(TransmissionConfigItemDO::getNodeType, reqVO.getNodeTypes())
            .eqIfPresent(TransmissionConfigItemDO::getNodeType, reqVO.getNodeType())
            .likeIfPresent(TransmissionConfigItemDO::getApiCode, reqVO.getApiCode())
            .likeIfPresent(TransmissionConfigItemDO::getDescription, reqVO.getDescription())
            .eqIfPresent(TransmissionConfigItemDO::getConfigValue, reqVO.getConfigValue())
            .eqIfPresent(TransmissionConfigItemDO::getDisable, reqVO.getDisable())
            .betweenIfPresent(TransmissionConfigItemDO::getCreateTime, reqVO.getCreateTime());
        wrapperX.orderByAsc(TransmissionConfigItemDO::getNodeType);
        wrapperX.orderByAsc(TransmissionConfigItemDO::getDslType);
        return wrapperX;
    }

    /**
     * 分页查询配置项
     */
    default PageResult<TransmissionConfigItemDO> selectPage(TransmissionConfigItemPageReqVO reqVO) {
        return selectPage(reqVO, buildConfigItemWrapper(reqVO));
    }

    /**
     * 列表查询配置项
     */
    default List<TransmissionConfigItemDO> selectList(TransmissionConfigItemPageReqVO reqVO) {
        return selectList(buildConfigItemWrapper(reqVO));
    }

    default Long selectItemCount(TransmissionConfigItemPageReqVO reqVO) {
        return selectCount(buildConfigItemWrapper(reqVO));
    }


    /**
     * 统计开通的当前服务商包的 租户数
     *
     * @return
     */
    List<CommonGroupStatisticsDto> selectItemCountByPackId(@Param("configPackIds") List<Integer> configPackIds, @Param("disable") Boolean disable);

}