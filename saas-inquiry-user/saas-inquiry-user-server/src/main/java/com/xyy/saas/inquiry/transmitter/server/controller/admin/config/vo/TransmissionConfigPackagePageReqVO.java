package com.xyy.saas.inquiry.transmitter.server.controller.admin.config.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 协议配置包分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
public class TransmissionConfigPackagePageReqVO extends PageParam {

    @Schema(description = "id")
    private Integer id;

    @Schema(description = "id列表", example = "[1,2,3]")
    private List<Integer> ids;

    @Schema(description = "机构类型（1-医保、2-药监、3-互联网监管、4-ERP对接、5-HIS对接、99-其他）", example = "2")
    private Integer organType;

    @Schema(description = "服务提供商名称", example = "王五")
    private String providerName;

    @Schema(description = "父节点id", example = "28667")
    private Integer parentPackageId;

    @Schema(description = "配置包名称", example = "张三")
    private String name;

    @Schema(description = "版本号（实际存储：**********；页面展示：服务提供商名称+机构类型名称+日期+小时，比如：创智医保20250122）")
    private Long version;

    @Schema(description = "描述", example = "你说的对")
    private String description;

    @Schema(description = "是否禁用")
    private Boolean disable;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "接口名称/接口编号")
    private String apiCode;

    @Schema(description = "节点名称")
    private String itemDescription;

}