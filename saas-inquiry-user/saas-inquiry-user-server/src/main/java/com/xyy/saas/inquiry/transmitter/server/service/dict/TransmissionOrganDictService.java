package com.xyy.saas.inquiry.transmitter.server.service.dict;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.pojo.excel.ImportResultDto;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictExcelVO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictPageReqVO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictRespVO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictSaveReqVO;
import com.xyy.saas.inquiry.transmitter.server.dal.dataobject.dict.TransmissionOrganDictDO;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 服务商字典 Service 接口
 *
 * <AUTHOR>
 */
public interface TransmissionOrganDictService {

    /**
     * 创建服务商字典
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTransmissionProviderDict(@Valid TransmissionOrganDictSaveReqVO createReqVO);

    /**
     * 更新服务商字典
     *
     * @param updateReqVO 更新信息
     */
    void updateTransmissionProviderDict(@Valid TransmissionOrganDictSaveReqVO updateReqVO);

    /**
     * 删除服务商字典
     *
     * @param id 编号
     */
    void deleteTransmissionProviderDict(Long id);

    /**
     * 获得服务商字典
     *
     * @param id 编号
     * @return 服务商字典
     */
    TransmissionOrganDictDO getTransmissionProviderDict(Long id);


    List<TransmissionOrganDictDO> getTransmissionDict(TransmissionOrganDictPageReqVO reqVO);

    /**
     * 获得服务商字典分页
     *
     * @param pageReqVO 分页查询
     * @return 服务商字典分页
     */
    PageResult<TransmissionOrganDictRespVO> getTransmissionProviderDictPage(TransmissionOrganDictPageReqVO pageReqVO);

    /**
     * 导入三方字典
     *
     * @param list          字典list
     * @param updateSupport 否更新已经存在的数据
     * @param organId       三方服务商id
     * @param dictValue     字典值
     * @return 导入结果
     */
    ImportResultDto importDictList(List<TransmissionOrganDictExcelVO> list, Boolean updateSupport, Integer organId, String dictValue);

    /**
     * 诊断匹配
     *
     * @param organId 三方服务商id
     * @return 导入结果
     */
    void diagnosisMatch(Integer organId);
}