package com.xyy.saas.inquiry.generic.api;

import com.xyy.saas.inquiry.generic.api.dto.diagnosis.InquiryDiagnosisDto;
import com.xyy.saas.inquiry.generic.api.dto.hospital.InquiryHospitalReqDto;
import com.xyy.saas.inquiry.generic.api.dto.hospital.InquiryHospitalRespDto;
import com.xyy.saas.inquiry.generic.model.GenericInvokeResponse;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import java.util.List;
import java.util.Map;

/**
 * 统一的问诊系统泛化API调用服务
 * 
 * <AUTHOR>
 */
public interface InquiryGenericApiService {

    // ==================== 医院相关API ====================
    
    /**
     * 查询医院列表-仅基础信息
     *
     * @see com.xyy.saas.inquiry.hospital.api.hospital.InquiryHospitalApi#getInquiryHospitalsBaseInfoMap(com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalReqDto)
     * @param reqDto 请求入参
     * @return 医院列表
     */
    Map<String, InquiryHospitalRespDto> getInquiryHospitalsBaseInfoMap(InquiryHospitalReqDto reqDto);

    // ==================== 诊断相关API ====================
    
    /**
     * 根据条件查询诊断列表
     *
     * @see com.xyy.saas.inquiry.hospital.api.diagnosis.InquiryDiagnosisApi#queryDiagnosisByCondition(List)
     */
    List<InquiryDiagnosisDto> queryDiagnosisByCondition(List<String> showNames);

    /**
     * 分页查询诊断信息（用于字典匹配）
     *
     * @see com.xyy.saas.inquiry.hospital.api.diagnosis.InquiryDiagnosisApi#getDiagnosisPage(com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDto)
     * @param pageReqDto 分页查询参数
     * @return 分页结果
     */
    PageResult<InquiryDiagnosisDto> getDiagnosisPage(InquiryDiagnosisDto pageReqDto);

    /**
     * 根据诊断名称模糊查询诊断列表（用于字典匹配）
     *
     * @see com.xyy.saas.inquiry.hospital.api.diagnosis.InquiryDiagnosisApi#getDiagnosisByName(String)
     * @param diagnosisName 诊断名称
     * @return 诊断列表
     */
    List<InquiryDiagnosisDto> getDiagnosisByName(String diagnosisName);

    /**
     * 根据ID列表查询诊断信息（用于字典匹配）
     *
     *
     * @see com.xyy.saas.inquiry.hospital.api.diagnosis.InquiryDiagnosisApi#getDiagnosisByIds(List)
     * @param ids 诊断ID列表
     * @return 诊断列表
     */
    List<InquiryDiagnosisDto> getDiagnosisByIds(List<Long> ids);

    // ==================== 药师相关API ====================

    /**
     * 定时任务处理药师离线
     *
     * @see com.xyy.saas.inquiry.pharmacist.api.pharmacist.InquiryPharmacistApi#jobHandPharmacistOffline()
     */
    void jobHandPharmacistOffline();
    
    /**
     * 定时任务处理saas迁移
     *
     * @see com.xyy.saas.inquiry.pharmacist.api.pharmacist.InquiryPharmacistApi#jobHandSaasMigration()
     */
    void jobHandSaasMigration();

    // ==================== 医生相关API ====================
    
    /**
     * 定时任务处理自动开方医生 出停诊
     *
     * @see com.xyy.saas.inquiry.hospital.api.doctor.InquiryDoctorApi#jobHandAutoInquiryDoctor()
     */
    void jobHandAutoInquiryDoctor();

    // ==================== 通用泛化调用方法 ====================
    
    /**
     * 通用泛化调用方法
     */
    GenericInvokeResponse invokeGeneric(String interfaceName, String methodName, String[] parameterTypes, Object[] args);
    
    /**
     * 带版本的通用泛化调用方法
     */
    GenericInvokeResponse invokeGeneric(String interfaceName, String methodName, String[] parameterTypes, Object[] args, String version);
}