package com.xyy.saas.inquiry.transmitter.server.controller.admin.dict.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - 服务商数据字典配对详情 Request VO")
@Data
@ToString(callSuper = true)
@Builder
public class TransmissionOrganDictMatchGetReqVO implements Serializable {

    @Schema(description = "医药行业行政机构ID", example = "4327")
    private Integer organId;

    @Schema(description = "字典类型", example = "1")
    private String dictType;

    @Schema(description = "saas字典名", example = "26981")
    private String dictName;

    @Schema(description = "saas字典id", example = "26981")
    private Long dictId;

    @Schema(description = "三方字典ids", example = "26981")
    private List<Long> organDictIds;

    @Schema(description = "saas字典ids", example = "26981")
    private List<Long> dictIds;

    /**
     * 配对状态：0未配对 1已配对
     */
    private Integer status;

}