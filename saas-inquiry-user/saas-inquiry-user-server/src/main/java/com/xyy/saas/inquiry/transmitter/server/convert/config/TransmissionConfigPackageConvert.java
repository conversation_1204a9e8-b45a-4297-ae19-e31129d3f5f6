package com.xyy.saas.inquiry.transmitter.server.convert.config;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.xyy.saas.inquiry.transmitter.api.config.dto.TransmissionConfigItemDTO;
import com.xyy.saas.inquiry.transmitter.api.config.dto.TransmissionConfigPackageDTO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.config.vo.TransmissionConfigPackageRespVO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.config.vo.TransmissionConfigPackageSaveReqVO;
import com.xyy.saas.inquiry.transmitter.server.dal.dataobject.config.TransmissionConfigPackageDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

@Mapper(uses = {TransmissionConfigItemConvert.class})
public interface TransmissionConfigPackageConvert {

    TransmissionConfigPackageConvert INSTANCE = Mappers.getMapper(TransmissionConfigPackageConvert.class);

    /**
     * 生成当前版本号（年月日小时）
     */
    default Long generateCurrentVersion() {
        return Long.parseLong(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHH")));
    }

    /**
     * SaveReqVO 转换为 DO
     */
    TransmissionConfigPackageDO convert(TransmissionConfigPackageSaveReqVO bean);

    /**
     * DO 转换为 RespVO
     */
    TransmissionConfigPackageRespVO convert2RespVO(TransmissionConfigPackageDO bean);

    /**
     * DO 分页转换为 RespVO 分页
     */
    default PageResult<TransmissionConfigPackageRespVO> convertPage(IPage<TransmissionConfigPackageDO> page) {
        if (page == null) {
            return null;
        }
        PageResult<TransmissionConfigPackageRespVO> result = new PageResult<>();
        result.setTotal(page.getTotal());
        result.setList(convert(page.getRecords()));
        return result;
    }

    /**
     * DO 列表转换为 RespVO 列表
     */
    List<TransmissionConfigPackageRespVO> convert(List<TransmissionConfigPackageDO> list);

    /**
     * 复制配置包 DO
     */
    default TransmissionConfigPackageDO copyConfigPackage(TransmissionConfigPackageDO original) {
        if (original == null) {
            return null;
        }
        TransmissionConfigPackageDO result = convert(original);
        // 清空ID，让数据库自增
        result.setId(null);
        // 使用当前时间生成版本号
        result.setVersion(generateCurrentVersion());
        // 清空创建时间，让数据库自动填充
        result.setCreateTime(null);
        // 清空更新时间，让数据库自动填充
        result.setUpdateTime(null);
        return result;
    }

    /**
     * DO 转换为 DO（用于复制）
     */
    TransmissionConfigPackageDO convert(TransmissionConfigPackageDO bean);

    /**
     * DO 转 DTO
     */
    @Mapping(target = "configItems", ignore = true)
    TransmissionConfigPackageDTO convert2DTO(TransmissionConfigPackageDO bean);

    /**
     * DO 列表转 DTO 列表
     */
    List<TransmissionConfigPackageDTO> convert2DTOList(List<TransmissionConfigPackageDO> list);

    /**
     * 组装完整的配置包列表
     *
     * @param configPackages      配置包DO列表
     * @param configItemMap       节点配置项Map
     * @param commonConfigItemMap 公共配置项Map
     * @return 配置包ID到配置包DO的映射
     */
    default Map<Integer, TransmissionConfigPackageDTO> assembleConfigPackages(
        List<TransmissionConfigPackageDO> configPackages,
        Map<Integer, List<TransmissionConfigItemDTO>> configItemMap,
        Map<Integer, List<TransmissionConfigItemDTO>> commonConfigItemMap) {

        // 1. 转换并组装配置包
        List<TransmissionConfigPackageDTO> configPackageDTOs = convert2DTOList(configPackages);
        configPackageDTOs.forEach(pack -> {
            // 2. 设置节点配置项
            if (!CollectionUtils.isEmpty(configItemMap.get(pack.getId()))) {
                pack.setConfigItems(configItemMap.get(pack.getId()));
            }

            // 3. 设置公共配置项
            if (!CollectionUtils.isEmpty(commonConfigItemMap.get(pack.getId()))) {
                pack.setCommonConfigItems(commonConfigItemMap.get(pack.getId()));
            }
        });

        // 4. 返回配置包映射
        return configPackageDTOs.stream()
            .collect(Collectors.toMap(TransmissionConfigPackageDTO::getId, configPackage -> configPackage));
    }

}
