package com.xyy.saas.inquiry.transmitter.server.service.organ;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.transmitter.enums.ErrorCodeConstants.*;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.system.api.tenant.TenantServicePackRelationApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantThirdAppApi;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantServicePackRelationDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantThirdAppRespDto;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.enums.tenant.TenantServicePackRelationStatusEnum;
import com.xyy.saas.inquiry.enums.transmitter.OrganTypeEnum;
import com.xyy.saas.inquiry.transmitter.api.organ.dto.TransmissionOrganNetworkConfigDTO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.organ.vo.TransmissionOrganPageReqVO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.organ.vo.TransmissionOrganRespVO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.organ.vo.TransmissionOrganSaveReqVO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.servicepack.vo.TransmissionServicePackPageReqVO;
import com.xyy.saas.inquiry.transmitter.server.convert.organ.TransmissionOrganConvert;
import com.xyy.saas.inquiry.transmitter.server.dal.dataobject.organ.TransmissionOrganDO;
import com.xyy.saas.inquiry.transmitter.server.dal.dataobject.servicepack.TransmissionServicePackDO;
import com.xyy.saas.inquiry.transmitter.server.dal.mysql.organ.TransmissionOrganMapper;
import com.xyy.saas.inquiry.transmitter.server.dal.mysql.servicepack.TransmissionServicePackMapper;
import com.xyy.saas.inquiry.util.PrefUtil;
import com.xyy.saas.inquiry.util.UserUtil;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 医药行业行政机构 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class TransmissionOrganServiceImpl implements TransmissionOrganService {

    @Resource
    private TransmissionOrganMapper organMapper;

    @Resource
    private TransmissionServicePackMapper servicePackMapper;

    @Resource
    private TenantServicePackRelationApi tenantServicePackRelationApi;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private TenantThirdAppApi tenantThirdAppApi;

    @Override
    public Integer createTransmissionOrgan(TransmissionOrganSaveReqVO createReqVO) {
        validateTransmissionOrganNotExists(createReqVO.getId(), createReqVO.getName(), createReqVO.getOrganType());
        validateAndProcessNetworkConfig(createReqVO.getNetworkConfig());

        TransmissionOrganDO transmissionOrgan = TransmissionOrganConvert.INSTANCE.convert(createReqVO);
        organMapper.insert(transmissionOrgan);
        return transmissionOrgan.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTransmissionOrgan(TransmissionOrganSaveReqVO updateReqVO) {
        // 校验基础数据
        validateOrganForUpdate(updateReqVO);

        // 校验并处理网络配置
        validateAndProcessNetworkConfig(updateReqVO.getNetworkConfig());

        // 更新
        TransmissionOrganDO updateObj = TransmissionOrganConvert.INSTANCE.convert(updateReqVO);
        organMapper.updateById(updateObj);
    }

    @Override
    public void deleteTransmissionOrgan(Integer id) {
        // 校验存在
        validateTransmissionOrganExists(id);
        // 删除
        organMapper.deleteById(id);
    }

    @Override
    public TransmissionOrganDO getTransmissionOrgan(Integer id) {
        return organMapper.selectById(id);
    }

    @Override
    public List<TransmissionOrganDO> getTransmissionOrgans(List<Integer> ids) {
        return organMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<TransmissionOrganRespVO> getTransmissionOrganPage(TransmissionOrganPageReqVO pageReqVO) {
        PageResult<TransmissionOrganRespVO> pageResult = TransmissionOrganConvert.INSTANCE.convertPage(
            organMapper.selectPage(pageReqVO));

        if (CollectionUtils.isEmpty(pageResult.getList())) {
            return pageResult;
        }

        // 补充额外信息
        List<Integer> organIds = pageResult.getList().stream()
            .map(TransmissionOrganRespVO::getId)
            .toList();

        enrichOrganPageResult(pageResult, organIds);
        return pageResult;
    }

    @Override
    public Map<Integer, TransmissionOrganDO> getTenantOrganMap(List<TenantServicePackRelationDto> tenantServicePacks) {

        //1. 获取租户开通的网络配置
        Map<Integer, String> organNetworkMap = tenantServicePacks.stream().filter(item -> item.extGet().getNetworkCode() != null)
            .collect(Collectors.toMap(
                TenantServicePackRelationDto::getOrganId, item -> item.extGet().getNetworkCode(),
                (v1, v2) -> v1));

        //2. 批量查询所有租户对接的所有机构信息
        List<Integer> organIds = organNetworkMap.keySet().stream().toList();
        List<TransmissionOrganDO> organs = organMapper.selectList(
            TransmissionOrganPageReqVO.builder()
                .ids(organIds)
                .disable(false)
                .build());

        if (CollectionUtils.isEmpty(organs)) {
            log.warn("[getValidOrgans][医药行业机构({})不存在或已禁用]", organIds);
            return new HashMap<>();
        }

        //3.匹配租户开通的网络配置
        organs.forEach(organ -> {
            String networkCode = organNetworkMap.get(organ.getId());
            if (CollUtil.isNotEmpty(organ.getNetworkConfig())) {
                organ.setNetworkConfig(
                    organ.getNetworkConfig().stream()
                        .filter(item -> item.getCode().equals(networkCode))
                        .findFirst()
                        .map(Collections::singletonList)
                        .orElse(null));
            }
        });

        return organs.stream()
            .collect(Collectors.toMap(TransmissionOrganDO::getId, organ -> organ));
    }

    // ====================== 私有方法 ======================

    private void validateAndProcessNetworkConfig(List<TransmissionOrganNetworkConfigDTO> networkConfig) {
        if (CollectionUtils.isEmpty(networkConfig)) {
            return;
        }

        // 校验网络配置名称唯一性
        if (networkConfig.stream().map(TransmissionOrganNetworkConfigDTO::getName).distinct().count()
            != networkConfig.size()) {
            throw exception(TRANSMISSION_ORGAN_NETWORK_CONFIG_EXISTS);
        }

        // 校验并处理每个配置项
        networkConfig.forEach(this::validateAndProcessNetworkConfigItem);
    }

    private void validateAndProcessNetworkConfigItem(TransmissionOrganNetworkConfigDTO config) {
        //未配置网络地址，直接返回
        if (MapUtil.isEmpty(config.getNetworkItem())) {
            return;
        }

        // 校验网络配置节点唯一性
        // if (config.getNetworkItems().stream()
        //     .map(TransmissionOrganNetworkConfigItemDTO::getCode)
        //     .distinct()
        //     .count() != config.getNetworkItems().size()) {
        //     throw exception(TRANSMISSION_ORGAN_NETWORK_CONFIG_ITEM_EXISTS);
        // }

        // 设置新的配置编码
        if (StringUtils.isBlank(config.getCode())) {
            config.setCode(PrefUtil.getTransmissionOrganNetworkConfigPref());
        }
    }

    private void validateOrganForUpdate(TransmissionOrganSaveReqVO updateReqVO) {
        // 校验机构存在
        TransmissionOrganDO oldOrgan = organMapper.selectById(updateReqVO.getId());
        if (oldOrgan == null) {
            throw exception(TRANSMISSION_ORGAN_NOT_EXISTS);
        }

        // 校验重名
        validateTransmissionOrganNotExists(updateReqVO.getId(), updateReqVO.getName(), updateReqVO.getOrganType());

        // 校验类型变更
        if (!oldOrgan.getOrganType().equals(updateReqVO.getOrganType())) {
            validateOrganTypeChange(updateReqVO.getId());
        }
    }

    private void validateOrganTypeChange(Integer organId) {
        List<TransmissionServicePackDO> servicePackList = servicePackMapper.selectList(
            TransmissionServicePackPageReqVO.builder().organId(organId).build()
        );
        if (CollectionUtils.isNotEmpty(servicePackList)) {
            throw exception(TRANSMISSION_ORGAN_TYPE_CHANGE_NOT_ALLOW);
        }
    }

    private void enrichOrganPageResult(PageResult<TransmissionOrganRespVO> pageResult, List<Integer> organIds) {
        // 获取并设置租户数量
        Map<Integer, Long> tenantCountMap = tenantServicePackRelationApi.selectCountByOrgans(organIds, TenantServicePackRelationStatusEnum.OPEN.getCode());

        // 补充描述信息
        pageResult.getList().forEach(item -> {
            item.setTenantCount(tenantCountMap.getOrDefault(item.getId(), 0L));
            item.setOrganTypeDesc(OrganTypeEnum.getDesc(item.getOrganType()));
        });

        // 补充用户信息
        UserUtil.fillUserInfo(pageResult.getList(), adminUserApi::getUserNameMap);
    }

    private void validateTransmissionOrganExists(Integer id) {
        if (organMapper.selectById(id) == null) {
            throw exception(TRANSMISSION_ORGAN_NOT_EXISTS);
        }
    }

    private void validateTransmissionOrganNotExists(Integer id, String name, Integer organType) {
        TransmissionOrganDO transmissionOrganDO = organMapper.selectByName(name, organType);
        if (transmissionOrganDO != null && !transmissionOrganDO.getId().equals(id)) {
            throw exception(TRANSMISSION_ORGAN_EXISTS);
        }
    }

    @Override
    public List<TransmissionOrganRespVO> getInquiryBizChannelType(TransmissionOrganPageReqVO pageReqVO) {

        // 需要查询的机构类型
        List<Integer> organTypeList = pageReqVO.getOrganTypeList();
        Long tenantId = TenantContextHolder.getTenantId();

        if (CollectionUtils.isEmpty(organTypeList) || tenantId == null) {
            return Lists.newArrayList();
        }

        List<TransmissionOrganRespVO> resultList = new ArrayList<>();

        // 处理ERP类型机构 , 当为非运营端租户时 , 则只查询自己开通了的erp业务渠道
        this.handleErpOrganType(tenantId, organTypeList, resultList);

        if (CollectionUtils.isEmpty(organTypeList)) {
            return resultList;
        }

        // 查询其他类型的机构
        List<TransmissionOrganDO> transmissionOrganDOS = organMapper.selectList(new LambdaQueryWrapperX<TransmissionOrganDO>()
            .in(TransmissionOrganDO::getOrganType, organTypeList)
            .eq(TransmissionOrganDO::getDeleted, false)
            .orderByAsc(TransmissionOrganDO::getId));

        resultList.addAll(this.convertDictToRespVOList(transmissionOrganDOS));

        return resultList;
    }

    /**
     * 处理 ERP 类型机构
     *
     * @param tenantId
     * @param organTypeList
     * @param transmissionOrganRespVOList
     */
    private void handleErpOrganType(Long tenantId, List<Integer> organTypeList, List<TransmissionOrganRespVO> transmissionOrganRespVOList) {

        // 当为运营端或者查询条件不包含erp业务类型时 , 直接返回
        if (tenantId == 1 || !organTypeList.contains(OrganTypeEnum.ERP.getCode())) {
            return;
        }

        organTypeList.remove(OrganTypeEnum.ERP.getCode());

        // 查询租户关联的erp机构
        List<TenantThirdAppRespDto> tenantThirdAppRespDtoList = tenantThirdAppApi.getByTenantId(tenantId);

        if (CollectionUtils.isEmpty(tenantThirdAppRespDtoList)) {
            return;
        }

        List<Integer> transmissionOrganIdList = tenantThirdAppRespDtoList.stream()
            .map(TenantThirdAppRespDto::getTransmissionOrganId)
            .distinct()
            .toList();

        List<TransmissionOrganDO> transmissionOrganDOS = organMapper.selectList(new LambdaQueryWrapperX<TransmissionOrganDO>()
            .in(TransmissionOrganDO::getId, transmissionOrganIdList)
            .eq(TransmissionOrganDO::getDeleted, false));

        transmissionOrganRespVOList.addAll(this.convertDictToRespVOList(transmissionOrganDOS));
    }

    /**
     * 数据转换
     *
     * @param transmissionOrganDOS
     * @return
     */
    private List<TransmissionOrganRespVO> convertDictToRespVOList(List<TransmissionOrganDO> transmissionOrganDOS) {

        return transmissionOrganDOS.stream().map(item -> {
            TransmissionOrganRespVO respVO = new TransmissionOrganRespVO();
            respVO.setId(item.getId());
            respVO.setName(item.getName());
            return respVO;
        }).toList();
    }
}