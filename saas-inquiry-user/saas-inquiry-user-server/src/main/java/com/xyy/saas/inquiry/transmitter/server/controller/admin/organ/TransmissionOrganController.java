package com.xyy.saas.inquiry.transmitter.server.controller.admin.organ;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.organ.vo.TransmissionOrganPageReqVO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.organ.vo.TransmissionOrganRespVO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.organ.vo.TransmissionOrganSaveReqVO;
import com.xyy.saas.inquiry.transmitter.server.dal.dataobject.organ.TransmissionOrganDO;
import com.xyy.saas.inquiry.transmitter.server.service.organ.TransmissionOrganService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 医药行业行政机构")
@RestController
@RequestMapping(value = {"/admin-api/v2/transmitter/transmission-organ", "/app-api/v2/transmitter/transmission-organ"})
@Validated
public class TransmissionOrganController {

    @Resource
    private TransmissionOrganService transmissionOrganService;

    @PostMapping("/create")
    @Operation(summary = "创建医药行业行政机构")
    @PreAuthorize("@ss.hasPermission('saas:transmission-organ:create')")
    public CommonResult<Integer> createTransmissionOrgan(@Valid @RequestBody TransmissionOrganSaveReqVO createReqVO) {
        return success(transmissionOrganService.createTransmissionOrgan(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新医药行业行政机构")
    @PreAuthorize("@ss.hasPermission('saas:transmission-organ:update')")
    public CommonResult<Boolean> updateTransmissionOrgan(@Valid @RequestBody TransmissionOrganSaveReqVO updateReqVO) {
        transmissionOrganService.updateTransmissionOrgan(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除医药行业行政机构")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:transmission-organ:delete')")
    public CommonResult<Boolean> deleteTransmissionOrgan(@RequestParam("id") Integer id) {
        transmissionOrganService.deleteTransmissionOrgan(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得医药行业行政机构")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasAnyPermissions('saas:transmission-organ:query','saas:tenant:third-app:query')")
    public CommonResult<TransmissionOrganRespVO> getTransmissionOrgan(@RequestParam("id") Integer id) {
        TransmissionOrganDO transmissionOrgan = transmissionOrganService.getTransmissionOrgan(id);
        return success(BeanUtils.toBean(transmissionOrgan, TransmissionOrganRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得医药行业行政机构分页")
    @PreAuthorize("@ss.hasAnyPermissions('saas:transmission-organ:query','saas:tenant:third-app:query')")
    public CommonResult<PageResult<TransmissionOrganRespVO>> getTransmissionOrganPage(@Valid TransmissionOrganPageReqVO pageReqVO) {
        PageResult<TransmissionOrganRespVO> pageResult = transmissionOrganService.getTransmissionOrganPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出医药行业行政机构 Excel")
    @PreAuthorize("@ss.hasPermission('saas:transmission-organ:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTransmissionOrganExcel(@Valid TransmissionOrganPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TransmissionOrganRespVO> list = transmissionOrganService.getTransmissionOrganPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "医药行业行政机构.xls", "数据", TransmissionOrganRespVO.class,
            list);
    }

    @GetMapping("/get-inquiry-biz-channel-type")
    @Operation(summary = "获取问诊渠道")
    public CommonResult<List<TransmissionOrganRespVO>> getInquiryBizChannelType(TransmissionOrganPageReqVO pageReqVO) {

        return success(transmissionOrganService.getInquiryBizChannelType(pageReqVO));
    }

}