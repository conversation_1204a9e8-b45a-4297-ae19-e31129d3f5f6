package com.xyy.saas.inquiry.user.server.service.tenant;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.user.server.controller.admin.tenant.vo.TenantThirdAppPageReqVO;
import com.xyy.saas.inquiry.user.server.controller.admin.tenant.vo.TenantThirdAppSaveReqVO;
import com.xyy.saas.inquiry.user.server.dal.dataobject.tenant.TenantThirdAppDO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 门店三方应用配置 Service 接口
 */
public interface TenantThirdAppService {

    /**
     * 创建或更新门店三方应用配置
     *
     * @param reqVO 创建/更新信息
     * @return 配置编号
     */
    Long saveOrUpdateThirdApp(@Valid TenantThirdAppSaveReqVO reqVO);

    /**
     * 重置门店三方应用配置
     *
     * @param id 编号
     */
    void resetThirdApp(Long id);

    /**
     * 删除门店三方应用配置
     *
     * @param id 编号
     */
    void deleteThirdApp(Long id);

    /**
     * 获得门店三方应用配置
     *
     * @param id 编号
     * @return 门店三方应用配置
     */
    TenantThirdAppDO getThirdApp(Long id);

    /**
     * 获得门店三方应用配置
     *
     * @param appKey
     * @return 门店三方应用配置
     */
    TenantThirdAppDO getByAppKey(String appKey);

    /**
     * 获得门店三方应用配置分页
     *
     * @param pageReqVO 分页查询
     * @return 门店三方应用配置分页
     */
    PageResult<TenantThirdAppDO> getThirdAppPage(TenantThirdAppPageReqVO pageReqVO);

    /**
     * 更新门店三方应用状态
     *
     * @param id     编号
     * @param status 状态
     */
    void updateThirdAppStatus(Long id, Integer status);

    /**
     * 条件查询门店三方应用配置
     *
     * @param pageReqVO 分页查询
     * @return 门店三方应用配置分页
     */
    List<TenantThirdAppDO> queryByCondition(TenantThirdAppPageReqVO pageReqVO);

} 