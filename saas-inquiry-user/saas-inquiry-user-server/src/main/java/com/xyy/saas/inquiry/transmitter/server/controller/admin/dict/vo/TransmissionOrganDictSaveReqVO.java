package com.xyy.saas.inquiry.transmitter.server.controller.admin.dict.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Schema(description = "管理后台 - 服务商字典新增/修改 Request VO")
@Data
@EqualsAndHashCode
public class TransmissionOrganDictSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20041")
    private Long id;

    @Schema(description = "服务提供方id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13973")
    @NotNull(message = "服务提供方id不能为空")
    private Integer organId;

    @Schema(description = "字典类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "字典类型不能为空")
    private String dictType;

    @Schema(description = "字典名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    private String dictName;

    @Schema(description = "父值 空无父值 ", example = "23921")
    private String parentValue;

    @Schema(description = "是否末级节点", example = "23921")
    private Integer endNode;

    @Schema(description = "字典标签", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "字典标签不能为空")
    private String label;

    @Schema(description = "字典值", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "字典值不能为空")
    private String value;

    @Schema(description = "字典外码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String outerValue;

    @Schema(description = "状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer sort;

}