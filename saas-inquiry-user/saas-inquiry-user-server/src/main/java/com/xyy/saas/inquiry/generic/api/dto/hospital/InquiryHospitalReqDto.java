package com.xyy.saas.inquiry.generic.api.dto.hospital;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @see com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalReqDto
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InquiryHospitalReqDto implements Serializable {

    /**
     * 医院ids
     */
    private List<Long> inquiryHospitalIds;

    /**
     * 医院prefs
     */
    private List<String> inquiryHospitalPrefs;

    /**
     * 是否禁用 默认启用
     */
    private Integer disable = CommonStatusEnum.ENABLE.getStatus();

}
