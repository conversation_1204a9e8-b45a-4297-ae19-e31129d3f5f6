package com.xyy.saas.inquiry.user.server.property;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import java.util.List;

/**
 * desc 登陆踢下线配置
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "yudao.login.kick-off")
public class LoginKickOffProperties {

    /** 是否开启踢下线 */
    private boolean enable;

    /** 需要踢下线的设备类型, 为空则表示所有设备类型 */
    private List<String> deviceTypes;

    /** 不需要踢下线的用户ID */
    private List<Long> excludeUserIds;


    /**
     * 是否需要踢下线
     * @param deviceType
     * @param userId
     * @return
     */
    public boolean needKickOff(String deviceType, Long userId) {
        return enable
            && (deviceTypes == null || CollUtil.contains(deviceTypes, i -> i.equalsIgnoreCase(deviceType)))
            && (excludeUserIds == null || !excludeUserIds.contains(userId));
    }
}
