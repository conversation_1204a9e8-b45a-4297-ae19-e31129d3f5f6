package com.xyy.saas.inquiry.transmitter.server.dal.mysql.organ;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import java.util.List;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.organ.vo.TransmissionOrganPageReqVO;
import com.xyy.saas.inquiry.transmitter.server.dal.dataobject.organ.TransmissionOrganDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 医药行业行政机构 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TransmissionOrganMapper extends BaseMapperX<TransmissionOrganDO> {

    /**
     * 构建医药行业行政机构查询条件
     */
    default LambdaQueryWrapperX<TransmissionOrganDO> buildOrganWrapper(TransmissionOrganPageReqVO reqVO) {
        return new LambdaQueryWrapperX<TransmissionOrganDO>()
            .inIfPresent(TransmissionOrganDO::getId, reqVO.getIds())
            .eqIfPresent(TransmissionOrganDO::getId, reqVO.getId())
            .eqIfPresent(TransmissionOrganDO::getOrganType, reqVO.getOrganType())
            .likeIfPresent(TransmissionOrganDO::getName, reqVO.getName())
            .eqIfPresent(TransmissionOrganDO::getBasicConfig, reqVO.getBasicConfig())
            .eqIfPresent(TransmissionOrganDO::getLogo, reqVO.getLogo())
            .eqIfPresent(TransmissionOrganDO::getProvince, reqVO.getProvince())
            .eqIfPresent(TransmissionOrganDO::getCity, reqVO.getCity())
            .eqIfPresent(TransmissionOrganDO::getArea, reqVO.getArea())
            .eqIfPresent(TransmissionOrganDO::getRemark, reqVO.getRemark())
            .eqIfPresent(TransmissionOrganDO::getDisable, reqVO.getDisable())
            .betweenIfPresent(TransmissionOrganDO::getCreateTime, reqVO.getCreateTime())
            .eqIfPresent(TransmissionOrganDO::getProvinceCode, reqVO.getProvinceCode())
            .eqIfPresent(TransmissionOrganDO::getCityCode, reqVO.getCityCode())
            .eqIfPresent(TransmissionOrganDO::getAreaCode, reqVO.getAreaCode())
            .orderByDesc(TransmissionOrganDO::getId);
    }

    default PageResult<TransmissionOrganDO> selectPage(TransmissionOrganPageReqVO reqVO) {
        return selectPage(reqVO, buildOrganWrapper(reqVO));
    }

    default List<TransmissionOrganDO> selectList(TransmissionOrganPageReqVO reqVO) {
        return selectList(buildOrganWrapper(reqVO));
    }

    default TransmissionOrganDO selectByName(String name, Integer organType) {
        return selectOne(new LambdaQueryWrapperX<TransmissionOrganDO>()
                .eq(TransmissionOrganDO::getName, name)
                .eq(TransmissionOrganDO::getOrganType, organType));
    }

}