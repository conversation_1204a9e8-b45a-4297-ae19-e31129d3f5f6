package com.xyy.saas.inquiry.transmitter.server.dal.mysql.config;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.config.vo.TransmissionConfigPackagePageReqVO;
import com.xyy.saas.inquiry.transmitter.server.dal.dataobject.config.TransmissionConfigPackageDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 协议配置包 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TransmissionConfigPackageMapper extends BaseMapperX<TransmissionConfigPackageDO> {

    /**
     * 构建配置包查询条件
     */
    default LambdaQueryWrapperX<TransmissionConfigPackageDO> buildConfigPackageWrapper(TransmissionConfigPackagePageReqVO reqVO) {
        return new LambdaQueryWrapperX<TransmissionConfigPackageDO>()
            .inIfPresent(TransmissionConfigPackageDO::getId, reqVO.getIds())
            .eqIfPresent(TransmissionConfigPackageDO::getOrganType, reqVO.getOrganType())
            .likeIfPresent(TransmissionConfigPackageDO::getProviderName, reqVO.getProviderName())
            .eqIfPresent(TransmissionConfigPackageDO::getParentPackageId, reqVO.getParentPackageId())
            .likeIfPresent(TransmissionConfigPackageDO::getName, reqVO.getName())
            .eqIfPresent(TransmissionConfigPackageDO::getVersion, reqVO.getVersion())
            .eqIfPresent(TransmissionConfigPackageDO::getDescription, reqVO.getDescription())
            .eqIfPresent(TransmissionConfigPackageDO::getDisable, reqVO.getDisable())
            .betweenIfPresent(TransmissionConfigPackageDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(TransmissionConfigPackageDO::getId);
    }

    /**
     * 根据名称、机构类型和版本查询配置包
     */
    TransmissionConfigPackageDO selectByNameAndOrganTypeAndVersion(String name, Integer organType, Long version);

    /**
     * 分页查询配置包
     */
    IPage<TransmissionConfigPackageDO> selectTransmissionConfigPage(Page<TransmissionConfigPackagePageReqVO> objectPage, TransmissionConfigPackagePageReqVO reqVO);

    /**
     * 列表查询配置包
     */
    default List<TransmissionConfigPackageDO> selectList(TransmissionConfigPackagePageReqVO reqVO) {
        return selectList(buildConfigPackageWrapper(reqVO));
    }


    default Long selectConfigCount(TransmissionConfigPackagePageReqVO reqVO) {
        return selectCount(buildConfigPackageWrapper(reqVO));
    }
}