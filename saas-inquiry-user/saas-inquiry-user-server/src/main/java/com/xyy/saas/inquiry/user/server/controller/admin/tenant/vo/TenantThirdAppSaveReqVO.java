package com.xyy.saas.inquiry.user.server.controller.admin.tenant.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

@Schema(description = "管理后台 - 门店三方应用配置创建/修改 Request VO")
@Data
public class TenantThirdAppSaveReqVO {

    @Schema(description = "ID", example = "1024")
    private Long id;

    @Schema(description = "租户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    // @NotNull(message = "租户编号不能为空")
    private Long tenantId;

    @Schema(description = "应用名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "ERP")
    // @NotBlank(message = "应用名称不能为空")
    @Size(max = 64, message = "应用名称长度不能超过64个字符")
    private String appName;

    @Schema(description = "ERP对接机构id", example = "2048")
    @NotNull(message = "ERP对接机构不能为空")
    private Integer transmissionOrganId;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    // @NotNull(message = "状态不能为空")
    private Integer status;

    @Schema(description = "备注", example = "我是备注")
    private String remark;

} 