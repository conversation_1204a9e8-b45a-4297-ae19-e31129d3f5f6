package cn.iocoder.yudao.module.system.controller.app.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 用户人脸信息新增/修改 Request VO")
@Data
public class UserFaceSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21292")
    private Long id;

    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "25295")
    private Long userId;

    @Schema(description = "三方人脸id", example = "24607")
    private String faceId;

    @Schema(description = "人脸图片")
    @NotNull(message = "人脸图片不能为空")
    private String faceImage;

    @Schema(description = "图片类型（0-BASE64 1-URL）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer faceType;

    @Schema(description = "状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

}