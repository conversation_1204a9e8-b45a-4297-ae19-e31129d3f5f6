package cn.iocoder.yudao.module.system.controller.admin.appversion;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.system.controller.admin.appversion.vo.AppVersionPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.appversion.vo.AppVersionRespVO;
import cn.iocoder.yudao.module.system.controller.admin.appversion.vo.AppVersionSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.appversion.AppVersionDO;
import cn.iocoder.yudao.module.system.service.appversion.AppVersionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - App版本")
@RestController
@RequestMapping("/system/app-version")
@Validated
public class AppVersionManageController {

    @Resource
    private AppVersionService appVersionService;

    @PostMapping("/create")
    @Operation(summary = "创建App版本")
    @PreAuthorize("@ss.hasPermission('system:app-version:create')")
    public CommonResult<Integer> createAppVersion(@Valid @RequestBody AppVersionSaveReqVO createReqVO) {
        return success(appVersionService.createAppVersion(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新App版本")
    @PreAuthorize("@ss.hasPermission('system:app-version:update')")
    public CommonResult<Boolean> updateAppVersion(@Valid @RequestBody AppVersionSaveReqVO updateReqVO) {
        appVersionService.updateAppVersion(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除App版本")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:app-version:delete')")
    public CommonResult<Boolean> deleteAppVersion(@RequestParam("id") Integer id) {
        appVersionService.deleteAppVersion(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得App版本")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:app-version:query')")
    public CommonResult<AppVersionRespVO> getAppVersion(@RequestParam("id") Integer id) {
        return success(appVersionService.getAppVersion(id));
    }

    @GetMapping("/page")
    @Operation(summary = "获得App版本分页")
    @PreAuthorize("@ss.hasPermission('system:app-version:query')")
    public CommonResult<PageResult<AppVersionRespVO>> getAppVersionPage(@Valid AppVersionPageReqVO pageReqVO) {
        PageResult<AppVersionDO> pageResult = appVersionService.getAppVersionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AppVersionRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出App版本 Excel")
    @PreAuthorize("@ss.hasPermission('system:app-version:query')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAppVersionExcel(@Valid AppVersionPageReqVO pageReqVO, HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AppVersionDO> list = appVersionService.getAppVersionPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "App版本.xls", "数据", AppVersionRespVO.class, BeanUtils.toBean(list, AppVersionRespVO.class));
    }

}