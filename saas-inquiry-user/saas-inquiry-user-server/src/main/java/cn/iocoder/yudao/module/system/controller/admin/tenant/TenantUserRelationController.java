package cn.iocoder.yudao.module.system.controller.admin.tenant;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserClockInDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantUserRelationPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantUserRelationRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantUserRelationUpdateVO;
import cn.iocoder.yudao.module.system.service.tenant.TenantUserRelationService;
import com.xyy.saas.inquiry.enums.user.ClockInTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 门店")
@RestController
@RequestMapping({"/admin-api/system/tenant-user-relation", "/app-api/system/admin/tenant-user-relation"})
@Validated
public class TenantUserRelationController {

    @Resource
    private TenantUserRelationService tenantUserRelationService;


    @PutMapping("/clock-in-out")
    @Operation(summary = "门店员工签到/签退")
    public CommonResult<Long> tenantUserClockIn(Integer clockInType, @RequestParam(required = false) Long userId) {
        TenantUserClockInDto clockInDto = new TenantUserClockInDto()
            .setUserId(userId)
            .setClockInType(ClockInTypeEnum.fromCode(clockInType))
            .setUserIp(ServletUtils.getClientIP()).setUserAgent(ServletUtils.getUserAgent());
        return success(tenantUserRelationService.tenantUserClockIn(clockInDto));
    }


    @GetMapping("/page")
    @Operation(summary = "根据userId，获得门店员工关系列表", description = "传userId查某个用户关联门店(并过滤当前登录用户所在门店id),不传userId查当前登录用户关联门店")
    @TenantIgnore
    public CommonResult<PageResult<TenantUserRelationRespVO>> pageTenantUserRelation(@Valid TenantUserRelationPageReqVO pageVO) {
        PageResult<TenantUserRelationRespVO> pageResult = tenantUserRelationService.pageTenantUserRelation(pageVO);
        return success(pageResult);
    }

    @PostMapping("/update-need-clock-in")
    @Operation(summary = "修改打卡标识")
    public CommonResult<Boolean> updateNeedClockIn(@Valid @RequestBody TenantUserRelationUpdateVO bindVO) {
        tenantUserRelationService.updateNeedClockIn(bindVO);
        return success(true);
    }


    @PostMapping("/bind")
    @Operation(summary = "用户绑定门店")
    public CommonResult<Boolean> bindTenant(@Valid @RequestBody TenantUserRelationUpdateVO bindVO) {
        tenantUserRelationService.bindTenant(bindVO);
        return success(true);
    }


    @PostMapping("/unbind")
    @Operation(summary = "用户解绑门店")
    public CommonResult<Boolean> unBindTenant(@Valid @RequestBody TenantUserRelationUpdateVO bindVO) {
        tenantUserRelationService.unBindTenant(bindVO);
        return success(true);
    }


}
