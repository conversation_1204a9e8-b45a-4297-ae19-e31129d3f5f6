package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.excel;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.nacos.common.utils.NumberUtils;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageRelationStatusEnum;
import com.xyy.saas.inquiry.pojo.excel.ImportExcelVoDto;
import com.xyy.saas.inquiry.util.excel.validator.DictValid;
import com.xyy.saas.inquiry.util.excel.validator.ValidDateFormat;
import com.xyy.saas.inquiry.util.excel.validator.ValidNumberFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.Length;

/**
 * 批量修改订单信息ExcelVO
 */
@Getter
public class TenantPackageRelationUpdateExcelVO extends ImportExcelVoDto {

    @ExcelProperty(value = "订单编号", index = 0)
    @NotBlank(message = "订单编号不能为空")
    @Size(max = 64, message = "订单编号超出最大长度64限制")
    private String pref;

    @ExcelProperty(value = "服务包性质", index = 1)
    @DictValid(dictType = DictTypeConstants.TENANT_PACKAGE_RELATION_NATURE, message = "无法找到对应的服务包性质")
    private String packageNature;

    @ExcelProperty(value = "收款方式", index = 2)
    @DictValid(dictType = DictTypeConstants.TENANT_PACKAGE_RELATION_PAYMENT_TYPE, message = "无法找到对应的收款方式")
    private String paymentType;

    @ExcelProperty(value = "实收金额", index = 3)
    @ValidNumberFormat(message = "实收金额不正确,请填写0-9999999.99的数字类型(保留两位小数)")
    private String actualAmount;

    @ExcelProperty(value = "收款账户", index = 4)
    @DictValid(dictType = DictTypeConstants.TENANT_PACKAGE_RELATION_COLLECT_ACCOUNT, message = "无法找到对应的收款账户")
    private String collectAccount;

    @ExcelProperty(value = "付款流水号", index = 5)
    @Size(max = 256, message = "付款流水号超出最大长度256限制")
    private String payNo;

    @ExcelProperty(value = "服务失效时间", index = 6)
    @ValidDateFormat(message = "服务失效时间格式错误，应为 yyyy-MM-dd")
    private String endTime;

    @ExcelProperty(value = "签约日期", index = 7)
    @ValidDateFormat(message = "签约日期格式错误，应为 yyyy-MM-dd")
    private String signTime;

    @ExcelProperty(value = "签约渠道", index = 8)
    @DictValid(dictType = DictTypeConstants.TENANT_PACKAGE_RELATION_SIGN_CHANNEL, message = "无法找到对应的签约渠道")
    private String signChannel;

    @ExcelProperty(value = "签约人", index = 9)
    @Size(max = 64, message = "签约人超出最大长度64限制")
    private String signUser;

    @ExcelProperty(value = "代理人", index = 10)
    @Size(max = 64, message = "代理人超出最大长度64限制")
    private String proxyUser;

    /**
     * {@link TenantPackageRelationStatusEnum}
     */
    @ExcelProperty(value = "订单状态", index = 11)
    @DictValid(dictType = DictTypeConstants.TENANT_PACKAGE_RELATION_STATUS, message = "无法找到对应的订单状态")
    private String status;

    @ExcelProperty(value = "退款类型", index = 12)
    @DictValid(dictType = DictTypeConstants.TENANT_PACKAGE_RELATION_REFUND_TYPE, message = "无法找到对应的退款类型")
    private String refundType;

    @ExcelProperty(value = "退款价格", index = 13)
    @ValidNumberFormat(message = "退款价格不正确,请填写0-9999999.99的数字类型(保留两位小数)")
    private String refundPrice;

    @ExcelProperty(value = "订单状态变更原因", index = 14)
    @Length(max = 256, message = "订单状态变更原因超出最大长度256限制")
    private String reason;

    @ExcelProperty(value = "备注", index = 15)
    @Size(max = 256, message = "备注超出最大长度256限制")
    private String remark;


    /**
     * 校验当前对象基础字段信息
     */
    public void valid() {
        if (StringUtils.isNotBlank(pref) &&
            BeanUtil.beanToMap(this).entrySet().stream()
                .filter(e -> !"pref".equals(e.getKey()))
                .allMatch(e -> ObjectUtil.isEmpty(e.getValue()))) {
            this.errMsg = "无效数据,";
        }

        if (status() != null && StringUtils.isBlank(reason)) {
            this.errMsg = "订单状态变更原因不能为空,";
        }

        if (StringUtils.equals(status, TenantPackageRelationStatusEnum.REFUND.getCode() + "")) {
            if (refundType == null) {
                this.errMsg += "退款类型不能为空,";
            }
            if (refundPrice == null) {
                this.errMsg += "退款价格不能为空,";
            }
        }
    }


    public Integer status() {
        return NumberUtils.isDigits(status) ? NumberUtils.toInt(status) : null;
    }

    public Integer refundType() {
        return NumberUtils.isDigits(refundType) ? NumberUtils.toInt(refundType) : null;
    }

    public LocalDateTime localEndTime() {
        return StringUtils.isBlank(endTime) ? null : LocalDate.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd")).atTime(23, 59, 59);
    }

    public LocalDateTime localSignTime() {
        return StringUtils.isBlank(signTime) ? null : LocalDate.parse(signTime, DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay();
    }

    public BigDecimal actualAmount() {
        if (StringUtils.isNotBlank(actualAmount)) {
            try {
                return new BigDecimal(actualAmount);
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }

    public BigDecimal refundPrice() {
        if (StringUtils.isNotBlank(refundPrice)) {
            try {
                return new BigDecimal(refundPrice);
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }


    public void setErrMsg(String errMsg) {
        super.setErrMsg(errMsg);
    }


    public void setPref(String pref) {
        this.pref = pref;
    }

    public void setPackageNature(String packageNature) {
        this.packageNature = packageNature;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public void setActualAmount(String actualAmount) {
        this.actualAmount = actualAmount;
    }

    public void setCollectAccount(String collectAccount) {
        this.collectAccount = collectAccount;
    }

    public void setPayNo(String payNo) {
        this.payNo = payNo;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public void setSignTime(String signTime) {
        this.signTime = signTime;
    }

    public void setSignChannel(String signChannel) {
        this.signChannel = signChannel;
    }

    public void setSignUser(String signUser) {
        this.signUser = signUser;
    }

    public void setProxyUser(String proxyUser) {
        this.proxyUser = proxyUser;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setRefundType(String refundType) {
        this.refundType = refundType;
    }

    public void setRefundPrice(String refundPrice) {
        this.refundPrice = refundPrice;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
