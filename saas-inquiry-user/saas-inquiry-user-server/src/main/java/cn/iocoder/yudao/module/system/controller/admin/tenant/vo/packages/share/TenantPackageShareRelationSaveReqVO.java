package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.share;

import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 总部门店套餐共享新增/修改 Request VO")
@Data
public class TenantPackageShareRelationSaveReqVO {

    @Schema(description = "主键", example = "12924")
    private Long id;

    @Schema(description = "总部id", example = "9866")
    private Long headTenantId;

    @Schema(description = "门店id", requiredMode = Schema.RequiredMode.REQUIRED, example = "9866")
    private Long tenantId;

    @Schema(description = "门店ids", requiredMode = Schema.RequiredMode.REQUIRED, example = "9866")
    @NotEmpty(message = "共享门店Ids不能为空")
    private List<Long> tenantIds;

    @Schema(description = "业务类型 0-问诊,1-智慧脸...", example = "1")
    private Integer bizType = BizTypeEnum.HYWZ.getCode();

    @Schema(description = "共享套餐开通关系表id", requiredMode = Schema.RequiredMode.REQUIRED, example = "574")
    @NotNull(message = "共享套餐开通关系表id不能为空")
    private Long tenantPackageId;

    @Schema(description = "扩展信息")
    private String ext;

}