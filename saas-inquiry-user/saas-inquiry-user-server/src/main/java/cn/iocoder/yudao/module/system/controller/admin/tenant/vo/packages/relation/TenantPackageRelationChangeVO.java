package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 批量切换门店套餐包 VO")
@Data
public class TenantPackageRelationChangeVO {


    @Schema(description = "新套餐包id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private Long packageId;


    @Schema(description = "问诊业务类型", example = "1")
    // @NotNull
    private Integer inquiryBizType;


    @Schema(description = "门店套餐包ids", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private List<Long> tenantPackageRelationIds;


}