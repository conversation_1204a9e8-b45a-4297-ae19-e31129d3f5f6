package cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Schema(description = "管理后台 - 门店员工指纹Check VO")
@Data
public class TenantUserFingerPrintCheckReqVO {

    @Schema(description = "用户ID", example = "14788")
    private Long userId;
    /**
     * 门店id
     */
    private Long tenantId;

    @Schema(description = "指纹信息")
    @NotBlank(message = "指纹信息不能为空")
    private String fingerPrintInfo;

    // @Schema(description = "生产厂商")
    // private String manufacturer;
    //
    // @Schema(description = "设备id", example = "28417")
    // private String deviceId;

}