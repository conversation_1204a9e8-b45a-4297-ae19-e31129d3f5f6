package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack;


import com.xyy.saas.inquiry.pojo.servicepack.ServicePackRelationExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 门店-开通服务包关系新增/修改 Request VO")
@Data
public class TenantServicePackRelationBatchChangeVO {

    @Schema(description = "门店id列表")
    @NotEmpty(message = "门店列表不可为空")
    private List<Long> tenantIds;

    @Schema(description = "服务类型")
    @NotNull(message = "服务类型不可为空")
    private Integer organType;

    // @Schema(description = "服务包信息")
    // private List<TenantServicePackRelationItemVO> servicePackRelationItems;

    @Schema(description = "服务包id ,其他单选，兼容药监单独处理多选")
    private Integer servicePackId;

    @Schema(description = "服务包扩展信息ext")
    private ServicePackRelationExtDto ext;

    @Schema(description = "目录版本id - 单选，药监没有目录")
    private Long catalogId;


}