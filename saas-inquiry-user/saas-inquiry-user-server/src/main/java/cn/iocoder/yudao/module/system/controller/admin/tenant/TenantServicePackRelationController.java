package cn.iocoder.yudao.module.system.controller.admin.tenant;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack.TenantServicePackRelationBatchChangeVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack.TenantServicePackRelationDetailRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack.TenantServicePackRelationPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack.TenantServicePackRelationRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack.TenantServicePackRelationSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantRespVO;
import cn.iocoder.yudao.module.system.service.tenant.servicepack.TenantTransmissionServicePackRelationService;
import com.baomidou.lock.annotation.Lock4j;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 门店-开通服务包关系")
@RestController
@RequestMapping("/system/tenant-transmission-service-pack-relation")
@Validated
public class TenantServicePackRelationController {

    @Resource
    private TenantTransmissionServicePackRelationService tenantTransmissionServicePackRelationService;

    @PostMapping("/save")
    @Operation(summary = "创建/修改门店-开通服务包关系")
    @PreAuthorize("@ss.hasPermission('saas:tenant-transmission-service-pack-relation:create')")
    public CommonResult<Integer> createTenantTransmissionServicePackRelation(@Valid @RequestBody TenantServicePackRelationSaveReqVO createReqVO) {
        return success(tenantTransmissionServicePackRelationService.createTenantTransmissionServicePackRelation(createReqVO));
    }

    @GetMapping("/get")
    @Operation(summary = "获得门店-开通服务包关系")
    @Parameter(name = "tenantId", description = "门店id", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:tenant-transmission-service-pack-relation:query')")
    public CommonResult<TenantServicePackRelationDetailRespVO> getTenantTransmissionServicePackRelation(@RequestParam("tenantId") Long tenantId) {
        TenantServicePackRelationDetailRespVO servicePackRelationRespVO = tenantTransmissionServicePackRelationService.getTenantTransmissionServicePackRelation(tenantId);
        return success(servicePackRelationRespVO);
    }

    @GetMapping("/page")
    @Operation(summary = "获得门店-开通服务包关系分页")
    @PreAuthorize("@ss.hasPermission('saas:tenant-transmission-service-pack-relation:query')")
    public CommonResult<PageResult<TenantServicePackRelationRespVO>> getTenantTransmissionServicePackRelationPage(@Valid TenantServicePackRelationPageReqVO pageReqVO) {
        PageResult<TenantServicePackRelationRespVO> pageResult = tenantTransmissionServicePackRelationService.getTenantTransmissionServicePackRelationPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/page-tenant")
    @Operation(summary = "获得门店-开通服务包关系分页")
    @PreAuthorize("@ss.hasAnyPermissions('saas:transmission-servicepack:query','saas:transmission-organ:query','saas:tenant:third-app:query','saas:tenant-transmission-service-pack-relation:query')")
    public CommonResult<PageResult<TenantRespVO>> getTransmissionServicePackRelationTenantPage(@Valid TenantServicePackRelationPageReqVO pageReqVO) {
        PageResult<TenantRespVO> pageResult = tenantTransmissionServicePackRelationService.getTransmissionServicePackRelationTenantPage(pageReqVO);
        return success(pageResult);
    }

    @PostMapping("/batch-change-service-pack")
    @Operation(summary = "门店批量切换服务包")
    @PreAuthorize("@ss.hasPermission('saas:tenant-transmission-service-pack-relation:create')")
    @Lock4j(expire = 30000)
    public CommonResult<Integer> batchChangeServicePack(@Valid @RequestBody TenantServicePackRelationBatchChangeVO changeVO) {
        return success(tenantTransmissionServicePackRelationService.batchChangeServicePack(changeVO));
    }

    @PostMapping("/batch-change-service-catalog")
    @Operation(summary = "门店批量切换目录")
    @PreAuthorize("@ss.hasPermission('saas:tenant-transmission-service-pack-relation:create')")
    @Lock4j(expire = 30000)
    public CommonResult<Integer> batchChangeServiceCatalog(@Valid @RequestBody TenantServicePackRelationBatchChangeVO changeVO) {
        return success(tenantTransmissionServicePackRelationService.batchChangeServiceCatalog(changeVO));
    }

    //
    // @GetMapping("/export-excel")
    // @Operation(summary = "导出门店-开通服务包关系 Excel")
    // @PreAuthorize("@ss.hasPermission('saas:tenant-transmission-service-pack-relation:export')")
    // @ApiAccessLog(operateType = EXPORT)
    // public void exportTenantTransmissionServicePackRelationExcel(@Valid TenantTransmissionServicePackRelationPageReqVO pageReqVO,
    //     HttpServletResponse response) throws IOException {
    //     pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
    //     List<TenantTransmissionServicePackRelationDO> list = tenantTransmissionServicePackRelationService.getTenantTransmissionServicePackRelationPage(pageReqVO).getList();
    //     // 导出 Excel
    //     ExcelUtils.write(response, "门店-开通服务包关系.xls", "数据", TenantTransmissionServicePackRelationRespVO.class,
    //         BeanUtils.toBean(list, TenantTransmissionServicePackRelationRespVO.class));
    // }

}