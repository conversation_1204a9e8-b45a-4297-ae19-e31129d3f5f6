package cn.iocoder.yudao.module.system.controller.admin.appversion.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.pojo.TenantDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Schema(description = "管理后台 - App版本 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppVersionRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "26315")
    @ExcelProperty("主键")
    private Integer id;

    @Schema(description = "app类型 0-荷叶问诊,1-智慧脸...", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("app类型 0-荷叶问诊,1-智慧脸...")
    private Integer appBiz;

    @Schema(description = "app版本，如:v1.0.0", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("app版本，如:v1.0.0")
    private String appVersion;

    @Schema(description = "当前版本编码,如100", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("当前版本编码,如100")
    private Integer appVersionCode;

    @Schema(description = "app版本升级内容描述", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("app版本升级内容描述")
    private String appVersionDesc;

    @Schema(description = "系统类型: android 、 ios", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("系统类型: android 、 ios")
    private String osType;

    @Schema(description = "当前版本支持的最低操作系统版本", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("当前版本支持的最低操作系统版本")
    private String minOsType;

    @Schema(description = "下载地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("下载地址")
    private String downloadUrl;

    @Schema(description = "升级更新类型：0-强制更新  1-提示可选更新  2-不提示可选更新", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("升级更新类型：0-强制更新  1-提示可选更新  2-不提示可选更新")
    private Integer upgradeType;

    @Schema(description = "升级范围：0-全量升级  1-比例用户灰度   2-指定用户灰度", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("升级范围：0-全量升级  1-比例用户灰度   2-指定用户灰度")
    private Integer upgradeScope;

    @Schema(description = "灰度比例0-100", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("灰度比例0-100")
    private Integer grayRatio;

    @Schema(description = "是否禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否禁用")
    private Boolean disable;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "灰度租户", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("灰度租户")
    private List<TenantDto> grayTenant;

    @Schema(description = "已上架应用市场", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("已上架应用市场")
    private List<Integer> releasedChannels;

}