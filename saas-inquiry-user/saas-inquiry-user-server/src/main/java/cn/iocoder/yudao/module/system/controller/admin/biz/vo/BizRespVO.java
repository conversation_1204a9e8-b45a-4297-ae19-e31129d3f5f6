package cn.iocoder.yudao.module.system.controller.admin.biz.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 业务 Response VO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ExcelIgnoreUnannotated
public class BizRespVO {

    @Schema(description = "业务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "17842")
    @ExcelProperty("业务ID")
    private Long id;

    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("类型")
    private Integer type;

    @Schema(description = "共享类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("共享类型")
    private Integer shareType;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("名称")
    private String name;

    @Schema(description = "总部角色id", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("总部角色id")
    private Set<Long> headRoleIds;

    @Schema(description = "门店角色id", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店角色id")
    private Set<Long> storeRoleIds;

    @Schema(description = "菜单id", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("菜单id")
    private Set<Long> menuIds;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}