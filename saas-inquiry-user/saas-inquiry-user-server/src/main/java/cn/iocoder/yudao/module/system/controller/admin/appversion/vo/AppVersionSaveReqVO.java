package cn.iocoder.yudao.module.system.controller.admin.appversion.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Data;

@Schema(description = "管理后台 - App版本新增/修改 Request VO")
@Data
public class AppVersionSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "26315")
    private Integer id;

    @Schema(description = "app类型 0-荷叶问诊,1-智慧脸...", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "app类型 0-荷叶问诊,1-智慧脸...不能为空")
    private Integer appBiz;

    @Schema(description = "app版本，如:v1.0.0", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "app版本，如:v1.0.0不能为空")
    @Size(max = 32, message = "app版本长度不能超过32个字符")
    private String appVersion;

    @Schema(description = "app内部版本号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "app内部版本号不能为空")
    @Max(value = 99999999, message = "app内部版本号长度不能超过99999999")
    private Integer appVersionCode;

    @Schema(description = "app版本升级内容描述", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "app版本升级内容描述不能为空")
    @Size(max = 255, message = "app版本升级内容描述长度不能超过255个字符")
    private String appVersionDesc;

    @Schema(description = "系统类型: android 、 ios", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "系统类型: android 、 ios不能为空")
    private String osType;

    @Schema(description = "当前版本支持的最低操作系统版本", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @Size(max = 32, message = "系统类型: android 、 ios长度不能超过32个字符")
    private String minOsType;

    @Schema(description = "下载地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @NotEmpty(message = "下载地址不能为空")
    @Size(max = 255, message = "下载地址长度不能超过255个字符")
    private String downloadUrl;

    @Schema(description = "升级更新类型：0-强制更新  1-提示可选更新  2-不提示可选更新", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "升级更新类型：0-强制更新  1-提示可选更新  2-不提示可选更新不能为空")
    private Integer upgradeType;

    @Schema(description = "升级范围：0-全量升级  1-比例用户灰度   2-指定用户灰度", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "升级范围：0-全量升级  1-比例用户灰度   2-指定用户灰度不能为空")
    private Integer upgradeScope;

    @Schema(description = "灰度比例0-100", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer grayRatio;

    @Schema(description = "是否禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean disable;

    @Schema(description = "灰度租户列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Long> grayTenant;

    @Schema(description = "已上架应用商店", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> releasedChannels;
}