package cn.iocoder.yudao.module.system.controller.app.user;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserRespVO;
import cn.iocoder.yudao.module.system.service.user.AdminUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 App - 用户(员工)")
@RestController
@RequestMapping("/system/user")
@Validated
public class AppUserController {

    @Resource
    private AdminUserService userService;


    @GetMapping("/pageUserSignature")
    @Operation(summary = "获得员工处方签名管理分页列表")
    @PreAuthorize("@ss.hasPermission('system:employee:list')")
    public CommonResult<PageResult<UserRespVO>> pageInquiryUserSignature(@Valid UserPageReqVO pageReqVO) {
        // 获得员工分页列表
        PageResult<UserRespVO> pageResult = userService.pageInquiryUserSignature(pageReqVO);
        return success(pageResult);
    }

}
