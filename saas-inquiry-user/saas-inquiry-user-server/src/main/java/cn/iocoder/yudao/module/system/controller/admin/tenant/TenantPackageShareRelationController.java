package cn.iocoder.yudao.module.system.controller.admin.tenant;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.share.TenantPackageShareRelationPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.share.TenantPackageShareRelationRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.share.TenantPackageShareRelationSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.share.TenantPackageShareRelationUpdateReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageShareRelationDO;
import cn.iocoder.yudao.module.system.service.tenant.TenantPackageShareRelationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "管理后台 - 总部门店套餐共享")
@RestController
@RequestMapping("/system/tenant-package-share-relation")
@Validated
public class TenantPackageShareRelationController {

    @Resource
    private TenantPackageShareRelationService tenantPackageShareRelationService;

    @PostMapping("/create")
    @Operation(summary = "创建总部门店套餐共享关系")
    // @PreAuthorize("@ss.hasPermission('system:tenant-package-share-relation:create')")
    public CommonResult<Long> createTenantPackageShareRelation(@Valid @RequestBody TenantPackageShareRelationSaveReqVO createReqVO) {
        return success(tenantPackageShareRelationService.createTenantPackageShareRelation(createReqVO));
    }


    @PostMapping("/delete")
    @Operation(summary = "删除总部门店套餐共享")
    // @PreAuthorize("@ss.hasPermission('system:tenant-package-share-relation:delete')")
    public CommonResult<Boolean> deleteTenantPackageShareRelation(@RequestBody TenantPackageShareRelationUpdateReqVO updateReqVO) {
        tenantPackageShareRelationService.deleteTenantPackageShareRelation(updateReqVO.getIds());
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得总部门店套餐共享")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    // @PreAuthorize("@ss.hasPermission('system:tenant-package-share-relation:query')")
    public CommonResult<TenantPackageShareRelationRespVO> getTenantPackageShareRelation(@RequestParam("id") Long id) {
        TenantPackageShareRelationDO tenantPackageShareRelation = tenantPackageShareRelationService.getTenantPackageShareRelation(id);
        return success(BeanUtils.toBean(tenantPackageShareRelation, TenantPackageShareRelationRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得总部门店套餐共享分页")
    // @PreAuthorize("@ss.hasPermission('system:tenant-package-share-relation:query')")
    public CommonResult<PageResult<TenantPackageShareRelationRespVO>> getTenantPackageShareRelationPage(@Valid TenantPackageShareRelationPageReqVO pageReqVO) {
        PageResult<TenantPackageShareRelationRespVO> pageResult = tenantPackageShareRelationService.getTenantPackageShareRelationPage(pageReqVO);
        return success(pageResult);
    }


}