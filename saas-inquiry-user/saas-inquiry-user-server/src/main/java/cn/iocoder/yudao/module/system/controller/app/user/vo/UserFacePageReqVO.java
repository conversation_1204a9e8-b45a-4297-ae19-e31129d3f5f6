package cn.iocoder.yudao.module.system.controller.app.user.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 用户人脸信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserFacePageReqVO extends PageParam {

    @Schema(description = "用户账号", example = "25295")
    private Long userId;

    @Schema(description = "三方人脸id", example = "24607")
    private String faceId;

    @Schema(description = "人脸图片")
    private String faceImage;

    @Schema(description = "图片类型（0-BASE64 1-URL）", example = "1")
    private Integer faceType;

    @Schema(description = "帐号状态（0正常 1停用）", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}