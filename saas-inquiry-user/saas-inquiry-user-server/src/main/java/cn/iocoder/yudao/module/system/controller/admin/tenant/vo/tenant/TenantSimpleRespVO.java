package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 门店精简 Response VO")
@Data
public class TenantSimpleRespVO {

    @Schema(description = "门店编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    /**
     * 租户类型（1-单店 2连锁门店 3连锁总部）
     */
    private Integer type;

    @Schema(description = "编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private String pref;

    @Schema(description = "门店名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道")
    private String name;

    @Schema(description = "地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "湖北省武汉市xxx")
    private String address;

}
