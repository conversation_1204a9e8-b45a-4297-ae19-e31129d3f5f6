package cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 门店员工指纹分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TenantUserFingerPrintPageReqVO extends PageParam {

    @Schema(description = "用户ID", example = "14788")
    private Long userId;

    /**
     * 门店id
     */
    private Long tenantId;

    @Schema(description = "指纹信息")
    private String fingerPrintInfo;

    @Schema(description = "生产厂商")
    private String manufacturer;

    @Schema(description = "设备id", example = "28417")
    private String deviceId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}