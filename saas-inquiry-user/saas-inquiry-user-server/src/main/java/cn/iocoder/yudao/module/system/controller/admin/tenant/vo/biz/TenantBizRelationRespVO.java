package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.biz;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 门店业务关系 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TenantBizRelationRespVO {

    @Schema(description = "自增编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "2563")
    @ExcelProperty("自增编号")
    private Long id;

    @Schema(description = "租户Id", example = "2563")
    private Long tenantId;

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("业务类型")
    private Integer bizType;

    @Schema(description = "租户类型（1-单店 2连锁门店 3连锁总部）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("租户类型（1-单店 2连锁门店 3连锁总部）")
    private Integer tenantType;

    @Schema(description = "租户编号(总部)", example = "30385")
    @ExcelProperty("租户编号(总部)")
    private Long headTenantId;

    @Schema(description = "账号数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "23146")
    @ExcelProperty("账号数量")
    private Integer accountCount;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}