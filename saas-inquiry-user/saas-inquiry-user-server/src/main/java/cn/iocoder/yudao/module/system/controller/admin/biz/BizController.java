package cn.iocoder.yudao.module.system.controller.admin.biz;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.module.system.controller.admin.biz.vo.*;
import cn.iocoder.yudao.module.system.dal.dataobject.biz.BizDO;
import cn.iocoder.yudao.module.system.service.biz.BizService;

@Tag(name = "管理后台 - 业务")
@RestController
@RequestMapping("/system/biz")
@Validated
public class BizController {

    @Resource
    private BizService bizService;

    @PostMapping("/create")
    @Operation(summary = "创建业务")
    @PreAuthorize("@ss.hasPermission('system:biz:create')")
    public CommonResult<Long> createBiz(@Valid @RequestBody BizSaveReqVO createReqVO) {
        return success(bizService.createBiz(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新业务")
    @PreAuthorize("@ss.hasPermission('system:biz:update')")
    public CommonResult<Boolean> updateBiz(@Valid @RequestBody BizSaveReqVO updateReqVO) {
        bizService.updateBiz(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除业务")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:biz:delete')")
    public CommonResult<Boolean> deleteBiz(@RequestParam("id") Long id) {
        bizService.deleteBiz(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得业务")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:biz:query')")
    public CommonResult<BizRespVO> getBiz(@RequestParam("id") Long id) {
        BizDO biz = bizService.getBiz(id);
        return success(BeanUtils.toBean(biz, BizRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得业务列表")
    @PreAuthorize("@ss.hasPermission('system:biz:list')")
    public CommonResult<List<BizSimpleRespVO>> getBizList(@Valid BizListReqVO reqVO) {
        List<BizDO> result = bizService.getBizList(reqVO);
        return success(BeanUtils.toBean(result, BizSimpleRespVO.class));
    }

}