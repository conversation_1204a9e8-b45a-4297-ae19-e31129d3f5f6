package cn.iocoder.yudao.module.system.controller.admin.appversion.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - app版本用户详情分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppVersionDetailPageReqVO extends PageParam {

    @Schema(description = "app版本id", example = "15576")
    private Integer appVersionId;

    @Schema(description = "业务类型：0-灰度租户  1-用户忽略版本", example = "1")
    private Integer bussnissType;

    @Schema(description = "忽略用户id", example = "3351")
    private Long userId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}