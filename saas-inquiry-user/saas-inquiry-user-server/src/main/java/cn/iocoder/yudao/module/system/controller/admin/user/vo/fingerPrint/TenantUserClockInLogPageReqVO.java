package cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 门店员工打卡记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TenantUserClockInLogPageReqVO extends PageParam {

    @Schema(description = "用户ID", example = "19808")
    private Long userId;

    @Schema(description = "用户 IP")
    private String userIp;
    /**
     * 打卡类型 {@link ClockInTypeEnum}
     */
    private Integer clockInType;

    @Schema(description = "浏览器 UA")
    private String userAgent;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}