package cn.iocoder.yudao.module.system.controller.app.appversion.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: xucao
 * @Date: 2025/01/21 9:50
 * @Description: app 版本忽略升级请求参数
 */
@Schema(description = "用户 App - 版本忽略升级请求参数")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class IgnoreUpgradeReqVO {

    @Schema(description = "app版本id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "app版本id不能为空")
    private Integer versionId;

    @Schema(description = "忽略用户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "忽略用户id不能为空")
    private Long userId;
}
