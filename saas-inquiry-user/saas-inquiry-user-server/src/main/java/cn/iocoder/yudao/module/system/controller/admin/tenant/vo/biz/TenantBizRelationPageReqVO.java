package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.biz;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 门店业务关系分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TenantBizRelationPageReqVO extends PageParam {

    @Schema(description = "业务类型", example = "2")
    private Integer bizType;

    @Schema(description = "租户类型（1-单店 2连锁门店 3连锁总部）", example = "1")
    private Integer tenantType;

    @Schema(description = "租户编号(总部)", example = "30385")
    private Long headTenantId;

    @Schema(description = "账号数量", example = "23146")
    private Integer accountCount;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}