package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.biz;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 门店业务关系新增/修改 Request VO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantBizRelationSaveReqVO {

    @Schema(description = "自增编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "2563")
    private Long id;

    @Schema(description = "门店id", example = "2")
    private Long tenantId;

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "业务类型不能为空")
    private Integer bizType;

    @Schema(description = "租户类型（1-单店 2连锁门店 3连锁总部）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "租户类型（1-单店 2连锁门店 3连锁总部）不能为空")
    private Integer tenantType;

    @Schema(description = "租户编号(总部)", example = "30385")
    private Long headTenantId;

    @Schema(description = "账号数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "23146")
    @NotNull(message = "账号数量不能为空")
    private Integer accountCount;

}