package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.certificate;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 门店资质证件信息新增/修改 Request VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class TenantCertificateSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "1572")
    private Long id;

    @Schema(description = "门店id", example = "2")
    private Long tenantId;

    @Schema(description = "证件类型 1营业执照 2药品经营许可证 3药品经营质量管理规范认证号", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "证件类型 1营业执照 2药品经营许可证 3药品经营质量管理规范认证号不能为空")
    private Integer certificateType;

    @Schema(description = "证件名称", example = "芋艿")
    private String certificateName;

    @Schema(description = "证件号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "证件号不能为空")
    private String certificateNo;

    @Schema(description = "证件url地址")
    private List<String> certificateImgUrls;

    @Schema(description = "注册时间")
    private LocalDateTime registerTime;

    @Schema(description = "有效期至")
    private LocalDateTime validTime;

}