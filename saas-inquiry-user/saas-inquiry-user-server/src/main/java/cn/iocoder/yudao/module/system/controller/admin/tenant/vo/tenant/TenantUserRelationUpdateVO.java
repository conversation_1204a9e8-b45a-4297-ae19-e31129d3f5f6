package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Schema(description = "管理后台 - 门店用户更新绑定 VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TenantUserRelationUpdateVO extends PageParam {


    @Schema(description = "处理的门店列表", example = "111")
    @NotNull
    private List<Long> tenantIds;


    @Schema(description = "用户id", example = "111")
    @NotNull
    private Long userId;

    /**
     * {@link com.xyy.saas.inquiry.enums.system.RoleCodeEnum}
     */
    @Schema(description = "赋予的角色code", example = "wz_admin")
    private String roleCode;

    /**
     * 是否需要打卡 (0是 1否) {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "是否需要打卡 (0是 1否)")
    private Integer needClockIn;

}
