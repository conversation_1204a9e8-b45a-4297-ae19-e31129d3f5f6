package cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 门店员工打卡记录新增/修改 Request VO")
@Data
public class TenantUserClockInLogSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21706")
    private Long id;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19808")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 门店id
     */
    private Long tenantId;


    /**
     * 打卡类型 {@link ClockInTypeEnum}
     */
    private Integer clockInType;

    @Schema(description = "用户 IP", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "用户 IP不能为空")
    private String userIp;

    @Schema(description = "浏览器 UA", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "浏览器 UA不能为空")
    private String userAgent;

}