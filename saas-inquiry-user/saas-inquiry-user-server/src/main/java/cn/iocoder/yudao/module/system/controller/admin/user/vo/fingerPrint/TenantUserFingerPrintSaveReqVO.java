package cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Schema(description = "管理后台 - 门店员工指纹新增/修改 Request VO")
@Data
public class TenantUserFingerPrintSaveReqVO {

    @Schema(description = "指纹ID", example = "7353")
    private Long id;

    @Schema(description = "用户ID", example = "14788")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "门店ID", example = "14788")
    @NotNull(message = "门店ID不能为空")
    private Long tenantId;

    @Schema(description = "指纹信息")
    private String fingerPrintInfo;

    @Schema(description = "生产厂商")
    private String manufacturer;

    @Schema(description = "设备id", example = "28417")
    private String deviceId;

    @Schema(description = "密码", example = "28417")
    private String password;

    @Schema(description = "是否验证密码", example = "28417")
    private boolean checkPwd;

    @AssertTrue(message = "密码不能为空")
    @JsonIgnore
    public boolean isPasswordValidWhenRequired() {
        // 仅当需要检查密码时，才校验 password 是否非空
        return !checkPwd || StringUtils.isNotBlank(password);
    }


    @AssertTrue(message = "指纹信息不能为空")
    @JsonIgnore
    public boolean isCheckFingerPrintInfo() {
        if (checkPwd) {
            return true;
        }
        return StringUtils.isNotBlank(fingerPrintInfo); // 不校验密码时，必须传递 指纹信息
    }

}