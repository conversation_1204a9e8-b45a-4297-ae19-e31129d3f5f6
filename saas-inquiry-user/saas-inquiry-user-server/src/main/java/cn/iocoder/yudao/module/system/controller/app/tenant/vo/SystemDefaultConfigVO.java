package cn.iocoder.yudao.module.system.controller.app.tenant.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2024/10/29 17:28
 */
@Schema(description = "系统默认配置 VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class SystemDefaultConfigVO implements Serializable {

    @Schema(description = "密码登录是否需要验证码 默认false", requiredMode = Schema.RequiredMode.REQUIRED, example = "false")
    private boolean pwdLoginRequireVerifyCode = false;


}
