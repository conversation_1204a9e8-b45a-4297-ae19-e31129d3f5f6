package cn.iocoder.yudao.module.system.controller.admin.biz.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 业务 Response VO")
@Data
@ExcelIgnoreUnannotated
public class BizSimpleRespVO {

    @Schema(description = "业务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "17842")
    @ExcelProperty("业务ID")
    private Long id;

    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("类型")
    private Integer type;

    @Schema(description = "共享类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("共享类型")
    private Integer shareType;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("名称")
    private String name;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}