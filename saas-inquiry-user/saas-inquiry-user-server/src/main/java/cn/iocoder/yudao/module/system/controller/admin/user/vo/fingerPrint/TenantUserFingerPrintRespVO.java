package cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 门店员工指纹 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TenantUserFingerPrintRespVO {

    @Schema(description = "指纹ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "7353")
    @ExcelProperty("指纹ID")
    private Long id;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "14788")
    @ExcelProperty("用户ID")
    private Long userId;

    /**
     * 门店id
     */
    private Long tenantId;

    @Schema(description = "指纹信息", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("指纹信息")
    private String fingerPrintInfo;

    @Schema(description = "生产厂商", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("生产厂商")
    private String manufacturer;

    @Schema(description = "设备id", requiredMode = Schema.RequiredMode.REQUIRED, example = "28417")
    @ExcelProperty("设备id")
    private String deviceId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}