package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "管理后台 - 短信验证码的登录 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SetFrequentTenantReqVO {

    @Schema(description = "门店编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "门店编号不能为空")
    private Long tenantId;

    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "用户id不能为空")
    private Long userId;
}
