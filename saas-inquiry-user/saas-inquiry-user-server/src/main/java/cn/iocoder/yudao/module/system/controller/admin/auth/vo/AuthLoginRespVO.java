package cn.iocoder.yudao.module.system.controller.admin.auth.vo;

import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantSimpleRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 登录 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class AuthLoginRespVO {

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long userId;

    @Schema(description = "用户登录的门店id,当仅有一个门店时返回 并且返回一下token信息")
    private Long tenantId;

    @Schema(description = "门店类型")
    private Integer tenantType;

    @Schema(description = "当前用户是否多门店", requiredMode = Schema.RequiredMode.REQUIRED)
    private boolean multiTenant;

    @Schema(description = "用户所属门店列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<TenantSimpleRespVO> tenantList;

    @Schema(description = "访问令牌", requiredMode = Schema.RequiredMode.REQUIRED, example = "happy")
    private String accessToken;

    @Schema(description = "刷新令牌", requiredMode = Schema.RequiredMode.REQUIRED, example = "nice")
    private String refreshToken;

    @Schema(description = "过期时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime expiresTime;

}
