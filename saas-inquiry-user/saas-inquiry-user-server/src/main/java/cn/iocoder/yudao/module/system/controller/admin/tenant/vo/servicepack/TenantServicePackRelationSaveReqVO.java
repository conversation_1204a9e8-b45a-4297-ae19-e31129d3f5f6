package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack;


import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 门店-开通服务包关系新增/修改 Request VO")
@Data
public class TenantServicePackRelationSaveReqVO {

    /**
     * 门店id
     */
    @NotNull
    private Long tenantId;


    @Schema(description = "医保服务包状态 0未开通 1开通")
    private Integer medicalInsuranceStatus;

    @Schema(description = "医保服务包")
    private List<TenantServicePackRelationItemVO> medicalInsurances;


    @Schema(description = "药监服务包状态 0未开通 1开通")
    private Integer drugSupervisionStatus;

    @Schema(description = "药监服务包")
    private List<TenantServicePackRelationItemVO> drugSupervisions;


    @Schema(description = "互联网医院监管服务包状态 0未开通 1开通")
    private Integer internetHospitalStatus;

    @Schema(description = "互联网医院监管服务包")
    private List<TenantServicePackRelationItemVO> internetHospitals;


}