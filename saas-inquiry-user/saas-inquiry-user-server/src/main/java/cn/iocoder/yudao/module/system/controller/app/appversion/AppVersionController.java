package cn.iocoder.yudao.module.system.controller.app.appversion;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.system.controller.admin.appversion.vo.AppVersionRespVO;
import cn.iocoder.yudao.module.system.controller.app.appversion.vo.CheckAppVersionReqVO;
import cn.iocoder.yudao.module.system.controller.app.appversion.vo.IgnoreUpgradeReqVO;
import cn.iocoder.yudao.module.system.service.appversion.AppVersionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: xucao
 * @Date: 2025/01/20 9:36
 * @Description: app 版本升级相关接口
 */
@Tag(name = "用户 App - 版本升级管理")
@RestController
@RequestMapping("/system/app-version")
@Validated
public class AppVersionController {

    @Resource
    private AppVersionService appVersionService;

    @PostMapping("/check-upgrade")
    @Operation(summary = "检查当前客户端版本是否需要升级")
    @PermitAll
    @ApiAccessLog(enable = false)
    public CommonResult<AppVersionRespVO> checkAppVersionUpgrade(@Valid @RequestBody CheckAppVersionReqVO reqVO) {
        return success(appVersionService.checkAppVersionUpgrade(reqVO));
    }

    @PutMapping("/ignore-upgrade")
    @Operation(summary = "用户忽略当前版本")
    @PermitAll
    public CommonResult<Boolean> ignoreUpgrade(@Valid @RequestBody IgnoreUpgradeReqVO reqVO) {
        return success(appVersionService.ignoreUpgrade(reqVO));
    }

}
