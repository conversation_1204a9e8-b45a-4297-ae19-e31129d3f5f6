package cn.iocoder.yudao.module.system.controller.admin.appversion.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

@Schema(description = "管理后台 - app版本用户详情 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppVersionDetailRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "16523")
    @ExcelProperty("主键")
    private Integer id;

    @Schema(description = "app版本id", requiredMode = Schema.RequiredMode.REQUIRED, example = "15576")
    @ExcelProperty("app版本id")
    private Integer appVersionId;

    @Schema(description = "业务类型：0-灰度租户  1-用户忽略版本", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("业务类型：0-灰度租户  1-用户忽略版本")
    private Integer bussnissType;

    @Schema(description = "忽略用户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "3351")
    @ExcelProperty("忽略用户id")
    private Long userId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}