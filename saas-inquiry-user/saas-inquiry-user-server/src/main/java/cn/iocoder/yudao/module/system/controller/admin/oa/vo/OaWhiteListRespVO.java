package cn.iocoder.yudao.module.system.controller.admin.oa.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - OA用户白名单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class OaWhiteListRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "28867")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "系统类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("系统类型")
    private List<Integer> bizTypes;

    @Schema(description = "用户名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("用户名")
    private String username;

    @Schema(description = "花名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("花名")
    private String flowerName;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}