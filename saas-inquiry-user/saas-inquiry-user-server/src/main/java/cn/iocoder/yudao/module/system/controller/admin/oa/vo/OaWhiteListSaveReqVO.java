package cn.iocoder.yudao.module.system.controller.admin.oa.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - OA用户白名单新增/修改 Request VO")
@Data
public class OaWhiteListSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "28867")
    private Long id;

    @Schema(description = "系统类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "系统类型不能为空")
    private List<Integer> bizTypes;

    @Schema(description = "用户名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "用户名不能为空")
    private String username;

    @Schema(description = "花名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "花名不能为空")
    private String flowerName;

    @Schema(description = "备注", example = "随便")
    private String remark;

}