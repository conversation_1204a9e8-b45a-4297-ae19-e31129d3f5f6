package cn.iocoder.yudao.module.system.controller.admin.user.vo.profile;

import cn.iocoder.yudao.framework.common.validation.InEnum;
import cn.iocoder.yudao.module.system.enums.sms.SmsSceneEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;


@Schema(description = "管理后台 - 用户个人更换手机号 VO")
@Data
public class UserProfileChangeMobileReqVO {


    @Schema(description = "是否新手机号,当输入更换后的新手机号时,传true")
    private boolean newMobile;

    @Schema(description = "短信场景", requiredMode = Schema.RequiredMode.REQUIRED, example = "修改手机号:2")
    @NotNull(message = "场景不能为空")
    @InEnum(SmsSceneEnum.class)
    private Integer scene;

    @Schema(description = "手机号码", example = "15601691300")
    @Length(min = 11, max = 11, message = "手机号长度必须 11 位")
    @NotEmpty(message = "手机号码不能为空")
    private String mobile;

    @Schema(description = "短信验证码", requiredMode = Schema.RequiredMode.REQUIRED, example = "默认验证码:1111")
    @NotEmpty(message = "验证码不能为空")
    private String code;

    @Schema(description = "默认不传,当输入更换后的新手机号后,接口返回code:1002003016 时,前端将msg作为二次确认的弹框,点击确认后传入true,")
    private boolean confirm;

}
