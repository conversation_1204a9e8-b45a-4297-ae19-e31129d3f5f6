package cn.iocoder.yudao.module.system.controller.admin.appversion.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - App版本分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AppVersionPageReqVO extends PageParam {

    @Schema(description = "app类型 0-荷叶问诊,1-智慧脸...")
    private Integer appBiz;

    @Schema(description = "app版本，如:v1.0.0")
    private String appVersion;

    @Schema(description = "当前版本编码,如100")
    private Integer appVersionCode;

    @Schema(description = "app版本升级内容描述")
    private String appVersionDesc;

    @Schema(description = "系统类型: android 、 ios", example = "1")
    private String osType;

    @Schema(description = "当前版本支持的最低操作系统版本", example = "2")
    private String minOsType;

    @Schema(description = "下载地址", example = "https://www.iocoder.cn")
    private String downloadUrl;

    @Schema(description = "升级更新类型：0-强制更新  1-提示可选更新  2-不提示可选更新", example = "2")
    private Integer upgradeType;

    @Schema(description = "升级范围：0-全量升级  1-比例用户灰度   2-指定用户灰度")
    private Integer upgradeScope;

    @Schema(description = "灰度比例0-100")
    private Integer grayRatio;

    @Schema(description = "是否禁用")
    private Boolean disable;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}