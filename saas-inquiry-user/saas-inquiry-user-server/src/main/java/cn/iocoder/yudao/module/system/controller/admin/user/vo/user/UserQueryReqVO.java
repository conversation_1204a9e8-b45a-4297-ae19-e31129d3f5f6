package cn.iocoder.yudao.module.system.controller.admin.user.vo.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "管理后台 - 用户精简信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserQueryReqVO {

    private Long tenantId;

    @Schema(description = "用户状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Integer status;

    @Schema(description = "是否有手机号")
    private boolean hasMobile;

    @Schema(description = "是否过滤店主")
    private boolean noAdmin;

    private Long nid;

}
