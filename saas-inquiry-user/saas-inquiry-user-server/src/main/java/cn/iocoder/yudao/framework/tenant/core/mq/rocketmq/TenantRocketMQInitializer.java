package cn.iocoder.yudao.framework.tenant.core.mq.rocketmq;


import com.xyy.saas.inquiry.product.server.mq.FlowableRocketMQConsumeMessageHook;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.impl.consumer.DefaultMQPushConsumerImpl;
import org.apache.rocketmq.client.impl.producer.DefaultMQProducerImpl;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import java.lang.reflect.Field;
import java.util.Map;

/**
 * 多租户的 RocketMQ 初始化器
 *
 * <AUTHOR>
 */
@Slf4j
public class TenantRocketMQInitializer implements BeanPostProcessor, ApplicationContextAware, ApplicationListener<ContextRefreshedEvent> {

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        initializeTenants();
    }

    private void initializeTenants() {
        // 获取所有 bean
        Map<String, Object> beans = applicationContext.getBeansOfType(Object.class);

        // 动态加载 EventBusConsumerHolder 类
        Class<?> eventBusConsumerHolderClass = null;
        try {
            eventBusConsumerHolderClass = Class.forName("com.xyy.saas.eventbus.rocketmq.core.original.EventBusConsumerHolder");
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("EventBusConsumerHolder class not found", e);
        }

        for (Object bean : beans.values()) {
            if (eventBusConsumerHolderClass.isInstance(bean)) {
                try {
                    Field consumerField = bean.getClass().getDeclaredField("consumer");
                    consumerField.setAccessible(true); // 设置为可访问
                    DefaultMQPushConsumer consumer = (DefaultMQPushConsumer) consumerField.get(bean);
                    initTenantConsumer(consumer);
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
        }

        // 初始化其他类型的 bean
        Map<String, DefaultRocketMQListenerContainer> listenerContainers = applicationContext.getBeansOfType(DefaultRocketMQListenerContainer.class);
        for (DefaultRocketMQListenerContainer container : listenerContainers.values()) {
            initTenantConsumer(container.getConsumer());
        }

        Map<String, RocketMQTemplate> templates = applicationContext.getBeansOfType(RocketMQTemplate.class);
        for (RocketMQTemplate template : templates.values()) {
            initTenantProducer(template.getProducer());
        }
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof DefaultRocketMQListenerContainer) {
            DefaultRocketMQListenerContainer container = (DefaultRocketMQListenerContainer) bean;
            initTenantConsumer(container.getConsumer());
        } else if (bean instanceof RocketMQTemplate) {
            RocketMQTemplate template = (RocketMQTemplate) bean;
            initTenantProducer(template.getProducer());
        }
        return bean;
    }

    private void initTenantProducer(DefaultMQProducer producer) {
        if (producer == null) {
            return;
        }
        DefaultMQProducerImpl producerImpl = producer.getDefaultMQProducerImpl();
        if (producerImpl == null) {
            return;
        }
        producerImpl.registerSendMessageHook(new TenantRocketMQSendMessageHook());
        log.info("[initTenantProducer][注册 SendMessageHook 成功]");
    }

    private void initTenantConsumer(DefaultMQPushConsumer consumer) {
        if (consumer == null) {
            return;
        }
        DefaultMQPushConsumerImpl consumerImpl = consumer.getDefaultMQPushConsumerImpl();
        if (consumerImpl == null) {
            return;
        }
        consumerImpl.registerConsumeMessageHook(new TenantRocketMQConsumeMessageHook());
        consumerImpl.registerConsumeMessageHook(new FlowableRocketMQConsumeMessageHook());
        log.info("[initTenantConsumer][注册 ConsumeMessageHook 成功]");
    }


}