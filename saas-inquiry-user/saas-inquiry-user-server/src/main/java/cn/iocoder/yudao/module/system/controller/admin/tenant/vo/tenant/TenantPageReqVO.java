package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 门店分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TenantPageReqVO extends PageParam {

    @Schema(description = "门店编码", example = "MD100001")
    @Length(max = 64, message = "门店编码最大长度为 {value}")
    private String pref;

    @Schema(description = "总部门店ID", example = "MD100001")
    private Long headTenantId;

    @Schema(description = "业务线类型", example = "1")
    private Integer bizType;

    @Schema(description = "业务线门店类型", example = "1")
    private Integer bizTenantType;

    @Schema(description = "业务线总部租户编号", example = "28554")
    private Long bizHeadTenantId;

    /**
     * 租户类型（1-单店 2连锁门店 3连锁总部）
     */
    @Schema(description = "门店类型", example = "1")
    private Integer type;

    @Schema(description = "门店名", example = "芋艿")
    @Length(max = 64, message = "门店名最大长度为 {value}")
    private String name;

    @Schema(description = "联系人", example = "张三")
    @Length(max = 64, message = "联系人最大长度为 {value}")
    private String contactName;

    @Schema(description = "联系手机")
    @Length(max = 32, message = "联系手机最大长度为 {value}")
    private String contactMobile;

    @Schema(description = "门店状态（0正常 1停用）", example = "1")
    private Integer status;

    /**
     * {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "问诊系统业务类型开通", example = "0")
    private Integer wzBizTypeStatus;

    @Schema(description = "账号数量", example = "26469")
    private Integer wzAccountCount;


    @Schema(description = "智慧脸业务线类型开通", example = "2")
    private Integer zhlBizTypeStatus;

    @Schema(description = "智慧脸账号数量", example = "17503")
    private Integer zhlAccountCount;


    @Schema(description = "营业执照名称", example = "张三")
    @Length(max = 64, message = "营业执照名称最大长度为 {value}")
    private String businessLicenseName;

    @Schema(description = "营业执照号")
    @Length(max = 64, message = "营业执照号最大长度为 {value}")
    private String businessLicenseNumber;


    @Schema(description = "省")
    private String province;

    @Schema(description = "省编码")
    private String provinceCode;

    @Schema(description = "市")
    private String city;

    @Schema(description = "市编码")
    private String cityCode;

    @Schema(description = "区")
    private String area;

    @Schema(description = "区编码")
    private String areaCode;

    @Schema(description = "药店地址")
    private String address;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "环境标志：prod-真实数据；test-测试数据；show-线上演示数据")
    private String envTag;

    private String nameOrPref;

    @Schema(description = "ERP对接机构")
    private Integer transmissionOrganId;

    @Schema(description = "不等于得门店id - 非系统门店 则过滤")
    private Long nid;

    @Schema(description = "选项类型", example = "1")
    private Integer optionType;

}
