package cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

@Schema(description = "管理后台 - 门店员工打卡记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TenantUserClockInLogRespVO {

    @Schema(description = "指纹ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21706")
    @ExcelProperty("指纹ID")
    private Long id;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19808")
    @ExcelProperty("用户ID")
    private Long userId;

    @Schema(description = "用户 IP", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("用户 IP")
    private String userIp;

    /**
     * 打卡类型 {@link ClockInTypeEnum}
     */
    private Integer clockInType;

    @Schema(description = "浏览器 UA", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("浏览器 UA")
    private String userAgent;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}