package cn.iocoder.yudao.module.system.controller.admin.user.vo.user;

import com.xyy.saas.inquiry.constant.SystemConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

@Schema(description = "管理后台 - 用户更新密码 Request VO")
@Data
public class UserUpdatePasswordReqVO {

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "用户编号不能为空")
    private Long id;

    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    @NotEmpty(message = "密码不能为空")
    @Pattern(regexp = SystemConstant.PASSWORD_PATTERN_REGEXP, message = SystemConstant.PASSWORD_PATTERN_REGEXP_MESSAGE)
    private String password;

}
