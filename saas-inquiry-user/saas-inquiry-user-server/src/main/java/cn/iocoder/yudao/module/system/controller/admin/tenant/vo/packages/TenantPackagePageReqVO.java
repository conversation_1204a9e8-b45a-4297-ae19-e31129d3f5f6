package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import cn.iocoder.yudao.framework.mybatis.core.type.IntegerListTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 门店套餐分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TenantPackagePageReqVO extends PageParam {

    @Schema(description = "套餐名", example = "VIP")
    @Length(max = 64, message = "套餐名最大长度不可超过64")
    private String name;

    @Schema(description = "编码", example = "xx100001")
    @Length(max = 64, message = "编码最大长度为 {value}")
    private String pref;

    @Schema(description = "状态", example = "1")
    private Integer status;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @Schema(description = "创建时间")
    private LocalDateTime[] createTime;

    @Schema(description = "系统类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @InEnum(value = BizTypeEnum.class, message = "状态必须是 {value}")
    private Integer bizType;

    @Schema(description = "套餐类型（1-单店 2连锁门店 3连锁总部）", example = "1")
    @InEnum(value = TenantTypeEnum.class, message = "租户类型必须是 {value}")
    private Integer packageType;

    @Schema(description = "问诊类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer inquiryBizType;

    @Schema(description = "问诊套餐包关联医院pref", example = "1,2")
    private String hospitalPref;

    /**
     * {@link InquiryWayTypeEnum}
     */
    @Schema(description = "问诊方式", example = "1,2")
    private List<Integer> inquiryWayTypes;

    /**
     * 问诊处方类型 {@link com.xyy.saas.inquiry.enums.prescription.PrescriptionTypeEnum}
     */
    @Schema(description = "问诊处方类型", example = "0,1")
    private List<Integer> prescriptionTypes;

    @Schema(description = "套餐可见地区编码,对应区域编码", example = "420000,420100")
    private List<String> regionArr;

}
