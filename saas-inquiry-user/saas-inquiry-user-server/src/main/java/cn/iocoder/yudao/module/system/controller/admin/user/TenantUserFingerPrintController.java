package cn.iocoder.yudao.module.system.controller.admin.user;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint.TenantUserFingerPrintCheckReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint.TenantUserFingerPrintPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint.TenantUserFingerPrintRespVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint.TenantUserFingerPrintSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.TenantUserFingerPrintDO;
import cn.iocoder.yudao.module.system.service.user.TenantUserFingerPrintService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 门店员工指纹")
@RestController
@RequestMapping("/system/tenant-user-finger-print")
@Validated
public class TenantUserFingerPrintController {

    @Resource
    private TenantUserFingerPrintService tenantUserFingerPrintService;

    @PostMapping("/create")
    @Operation(summary = "创建门店员工指纹")
    @PreAuthorize("@ss.hasPermission('system:tenant-user-finger-print:create')")
    public CommonResult<Long> createTenantUserFingerPrint(@Valid @RequestBody TenantUserFingerPrintSaveReqVO createReqVO) {
        return success(tenantUserFingerPrintService.createTenantUserFingerPrint(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新(录入)门店员工指纹+校验密码")
    @PreAuthorize("@ss.hasPermission('system:tenant-user-finger-print:update')")
    public CommonResult<Long> updateTenantUserFingerPrint(@Valid @RequestBody TenantUserFingerPrintSaveReqVO updateReqVO) {
        return success(tenantUserFingerPrintService.updateTenantUserFingerPrint(updateReqVO));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除门店员工指纹")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:tenant-user-finger-print:delete')")
    public CommonResult<Boolean> deleteTenantUserFingerPrint(@RequestParam("id") Long id) {
        tenantUserFingerPrintService.deleteTenantUserFingerPrint(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得门店员工指纹")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:tenant-user-finger-print:query')")
    public CommonResult<TenantUserFingerPrintRespVO> getTenantUserFingerPrint(@RequestParam("id") Long id) {
        TenantUserFingerPrintDO tenantUserFingerPrint = tenantUserFingerPrintService.getTenantUserFingerPrint(id);
        return success(BeanUtils.toBean(tenantUserFingerPrint, TenantUserFingerPrintRespVO.class));
    }

    @GetMapping("/get-by-user")
    @Operation(summary = "获得当前门店员工指纹")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:tenant-user-finger-print:query')")
    public CommonResult<TenantUserFingerPrintRespVO> getTenantUserDefaultFingerPrint() {
        TenantUserFingerPrintRespVO tenantUserFingerPrint = tenantUserFingerPrintService.getTenantUserDefaultFingerPrint();
        return success(tenantUserFingerPrint);
    }

    @GetMapping("/check-finger-print")
    @Operation(summary = "校验员工指纹(打卡、审方)")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:tenant-user-finger-print:query')")
    public CommonResult<Boolean> checkFingerPrint(@Valid TenantUserFingerPrintCheckReqVO checkReqVO) {
        tenantUserFingerPrintService.checkFingerPrint(checkReqVO);
        return success(true);
    }


    @GetMapping("/page")
    @Operation(summary = "获得门店员工指纹分页")
    @PreAuthorize("@ss.hasPermission('system:tenant-user-finger-print:query')")
    public CommonResult<PageResult<TenantUserFingerPrintRespVO>> getTenantUserFingerPrintPage(@Valid TenantUserFingerPrintPageReqVO pageReqVO) {
        PageResult<TenantUserFingerPrintDO> pageResult = tenantUserFingerPrintService.getTenantUserFingerPrintPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TenantUserFingerPrintRespVO.class));
    }

}