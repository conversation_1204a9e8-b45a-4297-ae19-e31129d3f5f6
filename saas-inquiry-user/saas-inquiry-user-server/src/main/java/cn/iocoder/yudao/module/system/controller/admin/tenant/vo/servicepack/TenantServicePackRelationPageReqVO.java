package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 门店-开通服务包关系分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TenantServicePackRelationPageReqVO extends PageParam {

    /**
     * 门店id
     */
    private Long tenantId;

    private List<Long> tenantIds;

    @Schema(description = "门店信息")
    @Size(max = 50, message = "门店信息搜索最大长度50")
    private String nameOrPref;

    @Schema(description = "联系电话")
    @Size(max = 11, message = "联系电话搜索最大长度11")
    private String contactMobile;

    @Schema(description = "省编码")
    private String provinceCode;

    @Schema(description = "市编码")
    private String cityCode;

    @Schema(description = "区编码")
    private String areaCode;

    @Schema(description = "服务包名称", example = "20204")
    private String servicePackName;

    @Schema(description = "服务包ID", example = "20204")
    private Integer servicePackId;

    private List<Integer> servicePackIds;

    @Schema(description = "目录版本id", example = "20204")
    private Long catalogId;

    @Schema(description = "医药行业行政机构ID", example = "17013")
    private Integer organId;

    @Schema(description = "服务机构类型（1-医保、2-药监、3-互联网医院监管、99-其他）", example = "1")
    private Integer organType;

    @Schema(description = "版本号（实际存储：2025012209；页面展示：医药行业行政机构名称+服务提供商名称+机构类型名称+日期+小时，比如：陕西省医保局创智医保20250122）")
    private Long servicePackVersion;

    @Schema(description = "状态 0未开通 1开通", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}