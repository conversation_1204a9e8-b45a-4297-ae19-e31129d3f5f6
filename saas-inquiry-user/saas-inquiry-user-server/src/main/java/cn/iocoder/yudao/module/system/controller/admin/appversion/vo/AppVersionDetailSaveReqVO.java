package cn.iocoder.yudao.module.system.controller.admin.appversion.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - app版本用户详情新增/修改 Request VO")
@Data
public class AppVersionDetailSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "16523")
    private Integer id;

    @Schema(description = "app版本id", requiredMode = Schema.RequiredMode.REQUIRED, example = "15576")
    @NotNull(message = "app版本id不能为空")
    private Integer appVersionId;

    @Schema(description = "业务类型：0-灰度租户  1-用户忽略版本", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "业务类型：0-灰度租户  1-用户忽略版本不能为空")
    private Integer bussnissType;

    @Schema(description = "忽略用户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "3351")
    @NotNull(message = "忽略用户id不能为空")
    private Long userId;

}