package cn.iocoder.yudao.module.system.controller.admin.auth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 账号密码登录 Request VO,多门店登录选择VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class AuthLoginTenantReqVO {

    @Schema(description = "客户端类型: 0、app  1、pc  2、小程序 ", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "0")
    private Integer clientChannelType;

    @Schema(description = "userId", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "userId不能为空")
    private Long userId;

    @Schema(description = "username", requiredMode = Schema.RequiredMode.REQUIRED, example = "jack")
    // @NotNull(message = "username不能为空")
    private String username;

    //     前端将选择的tenantId存入前端缓存并带入请求的header中
    @Schema(description = "门店Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "34")
    @NotNull(message = "门店Id不能为空")
    private Long tenantId;

    @Schema(description = "第一次登录的token", requiredMode = Schema.RequiredMode.REQUIRED, example = "17asd1qwe")
    @NotEmpty(message = "第一次登录的token不能为空")
    private String token;
}