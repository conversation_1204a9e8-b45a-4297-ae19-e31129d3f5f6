package cn.iocoder.yudao.module.system.controller.app.tenant;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantSimpleRespVO;
import cn.iocoder.yudao.module.system.controller.app.tenant.vo.SystemDefaultConfigVO;
import cn.iocoder.yudao.module.system.service.system.SystemDefaultService;
import cn.iocoder.yudao.module.system.service.tenant.TenantService;
import cn.iocoder.yudao.module.system.service.user.UserMobileBusinessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "门店 App - 门店信息")
@RestController
@RequestMapping("/system/tenant")
@Validated
public class AppTenantController {

    @Resource
    private TenantService tenantService;

    @Resource
    private SystemDefaultService systemDefaultService;

    @Resource
    private UserMobileBusinessService userMobileBusinessService;

    @GetMapping("/default-config")
    @Operation(summary = "获取当前系统默认的配置", description = "eg:是否展示密码登录,是否xxx")
    @PermitAll
    public CommonResult<SystemDefaultConfigVO> getAppDefaultConfig() {
        return success(systemDefaultService.getAppDefaultConfig());
    }


    @PutMapping("/release-lock")
    @Operation(summary = "释放锁")
    @PermitAll
    public CommonResult<Boolean> updateUserProfile(String lockKey, String lockValue) {
        userMobileBusinessService.releaseLock(lockKey, lockValue);
        return success(true);
    }


    @GetMapping("/get-list-by-user")
    @Operation(summary = "获取当前用户关联的门店列表", description = "获取当前用户关联的门店列表")
    public CommonResult<List<TenantSimpleRespVO>> getTenantListByUserId() {
        List<TenantSimpleRespVO> list = tenantService.getTenantListByUserId(SecurityFrameworkUtils.getLoginUserId());
        return success(list);
    }

}
