package cn.iocoder.yudao.module.system.controller.admin.tenant;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation.TenantPackageRelationChangeVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation.TenantPackageRelationPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation.TenantPackageRelationRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation.TenantPackageRelationSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation.TenantPackageRelationStatusChangeReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageRelationDO;
import cn.iocoder.yudao.module.system.service.tenant.TenantPackageRelationService;
import com.baomidou.lock.annotation.Lock4j;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "管理后台 - 门店套餐包")
@RestController
@RequestMapping("/system/tenant-package-relation")
@Validated
public class TenantPackageRelationController {

    @Resource
    private TenantPackageRelationService tenantPackageRelationService;

    @PostMapping("/create")
    @Operation(summary = "创建门店套餐包")
    @PreAuthorize("@ss.hasPermission('system:tenant-package-relation:create')")
    public CommonResult<Long> createTenantPackageRelation(@Valid @RequestBody TenantPackageRelationSaveReqVO createReqVO) {
        return success(tenantPackageRelationService.createTenantPackageRelation(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新门店套餐包")
    @PreAuthorize("@ss.hasPermission('system:tenant-package-relation:update')")
    public CommonResult<Boolean> updateTenantPackageRelation(@Valid @RequestBody TenantPackageRelationSaveReqVO updateReqVO) {
        tenantPackageRelationService.updateTenantPackageRelation(updateReqVO);
        return success(true);
    }

    @PostMapping("/batchChangeTcb")
    @Operation(summary = "批量切换门店套餐包")
    @PreAuthorize("@ss.hasPermission('system:tenant-package-relation:update')")
    @Lock4j
    public CommonResult<Boolean> batchUpdateTenantPackageRelation(@Valid @RequestBody TenantPackageRelationChangeVO changeVO) {
        tenantPackageRelationService.batchUpdateTenantPackageRelation(changeVO);
        return success(true);
    }


    @PutMapping("/updateStatus")
    @Operation(summary = "更新门店套餐包状态")
    @PreAuthorize("@ss.hasPermission('system:tenant-package-relation:update')")
    public CommonResult<Boolean> updateTenantPackageRelationStatus(@Validated(value = TenantPackageRelationStatusChangeReqVO.Update.class) @RequestBody TenantPackageRelationStatusChangeReqVO invalidReqVO) {
        tenantPackageRelationService.updateTenantPackageRelationStatus(invalidReqVO);
        return success(true);
    }

    @PutMapping("/batchUpdateStatus")
    @Operation(summary = "批量更新门店套餐包状态")
    @PreAuthorize("@ss.hasPermission('system:tenant-package-relation:update')")
    @Lock4j
    public CommonResult<Boolean> batchUpdateTenantPackageRelationStatus(@Validated(value = TenantPackageRelationStatusChangeReqVO.BatchUpdate.class) @RequestBody TenantPackageRelationStatusChangeReqVO statusChangeReqVO) {
        tenantPackageRelationService.batchUpdateTenantPackageRelationStatus(statusChangeReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除门店套餐包")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:tenant-package-relation:delete')")
    public CommonResult<Boolean> deleteTenantPackageRelation(@RequestParam("id") Long id) {
        tenantPackageRelationService.deleteTenantPackageRelation(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得门店套餐包")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:tenant-package-relation:query')")
    @TenantIgnore
    public CommonResult<TenantPackageRelationRespVO> getTenantPackageRelation(@RequestParam("id") Long id) {
        TenantPackageRelationDO tenantPackageRelation = tenantPackageRelationService.getTenantPackageRelation(id);
        return success(BeanUtils.toBean(tenantPackageRelation, TenantPackageRelationRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得门店套餐包分页")
    @PreAuthorize("@ss.hasPermission('system:tenant-package-relation:query')")
    @TenantIgnore
    public CommonResult<PageResult<TenantPackageRelationRespVO>> getTenantPackageRelationPage(@Valid TenantPackageRelationPageReqVO pageReqVO) {
        PageResult<TenantPackageRelationRespVO> pageResult = tenantPackageRelationService.getTenantPackageRelationPage(pageReqVO);
        return success(pageResult);
    }

    // @GetMapping("/export-excel")
    // @Operation(summary = "导出门店套餐包 Excel")
    // @PreAuthorize("@ss.hasPermission('system:tenant-package-relation:export')")
    // @ApiAccessLog(operateType = EXPORT)
    // @TenantIgnore
    // public void exportTenantPackageRelationExcel(@Valid TenantPackageRelationPageReqVO pageReqVO,
    //     HttpServletResponse response) throws IOException {
    //     pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
    //     List<TenantPackageRelationRespVO> list = tenantPackageRelationService.getTenantPackageRelationPage(pageReqVO).getList();
    //     // 导出 Excel
    //     ExcelUtils.write(response, "门店套餐包.xls", "数据", TenantPackageRelationRespVO.class,
    //         BeanUtils.toBean(list, TenantPackageRelationRespVO.class));
    // }

}