package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 门店Info VO")
@Data
public class TenantInfoVO {

    @Schema(description = "门店编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private String pref;

    @Schema(description = "门店名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道")
    private String name;

    /**
     * 租户类型（1-单店 2连锁门店 3连锁总部）
     */
    private Integer type;

    @Schema(description = "连锁总部ID", example = "MD100001")
    private Long headTenantId;

    @Schema(description = "连锁总部", example = "MD100001")
    private String headTenantName;

    @Schema(description = "省", requiredMode = Schema.RequiredMode.REQUIRED)
    private String province;

    @Schema(description = "省编码")
    private String provinceCode;

    @Schema(description = "市", requiredMode = Schema.RequiredMode.REQUIRED)
    private String city;

    @Schema(description = "市编码")
    private String cityCode;

    @Schema(description = "区", requiredMode = Schema.RequiredMode.REQUIRED)
    private String area;

    @Schema(description = "区编码")
    private String areaCode;

    @Schema(description = "药店地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private String address;

    @Schema(description = "联系人的用户id", example = "11333")
    private Long contactUserId;

    @Schema(description = "联系人", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String contactName;

    @Schema(description = "联系手机")
    private String contactMobile;

    @Schema(description = "营业执照名称", example = "张三")
    private String businessLicenseName;

    @Schema(description = "营业执照号")
    private String businessLicenseNumber;


}
