package cn.iocoder.yudao.module.system.controller.app.permission;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.controller.admin.permission.vo.role.RoleRespVO;
import cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleDO;
import cn.iocoder.yudao.module.system.service.permission.RoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Comparator;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * @Author:chenxiaoyi
 * @Date:2024/10/24 14:55
 */
@Tag(name = "角色 App")
@RestController
@RequestMapping("/system/role")
@Validated
public class AppRoleController {

    @Resource
    private RoleService roleService;

    @GetMapping("/list-signature-role")
    @Operation(summary = "获取签章用户角色列表", description = "只包含问诊app端签章授权角色列表-核对/发药/调配/药师")
    public CommonResult<List<RoleRespVO>> getSignatureRoleList() {

        // 查门店角色
        List<RoleDO> list = roleService.selectWzCadRole(); // 查询问诊核对/发药/调配/药师角色列表
        list.sort(Comparator.comparing(RoleDO::getSort));
        return success(BeanUtils.toBean(list, RoleRespVO.class));
    }
}
