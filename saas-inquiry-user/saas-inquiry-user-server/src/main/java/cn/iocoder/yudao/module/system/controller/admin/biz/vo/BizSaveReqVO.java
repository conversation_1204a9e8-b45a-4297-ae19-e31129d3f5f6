package cn.iocoder.yudao.module.system.controller.admin.biz.vo;

import com.mzt.logapi.starter.annotation.DiffLogField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 业务新增/修改 Request VO")
@Data
public class BizSaveReqVO {

    @Schema(description = "业务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "17842")
    private Long id;

    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "类型不能为空")
    @DiffLogField(name = "类型")
    private Integer type;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "名称不能为空")
    @DiffLogField(name = "名称")
    private String name;

    @Schema(description = "总部角色id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "总部角色id不能为空")
    @DiffLogField(name = "总部角色")
    private Set<Long> headRoleIds;

    @Schema(description = "门店角色id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "门店角色id不能为空")
    @DiffLogField(name = "门店角色")
    private Set<Long> storeRoleIds;

    @Schema(description = "菜单id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "菜单id不能为空")
    @DiffLogField(name = "菜单")
    private Set<Long> menuIds;

    @Schema(description = "备注", example = "你猜")
    private String remark;

}