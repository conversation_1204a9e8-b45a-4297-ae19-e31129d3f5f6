package cn.iocoder.yudao.module.system.controller.admin.user;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint.TenantUserClockInLogPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint.TenantUserClockInLogRespVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint.TenantUserClockInLogSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.TenantUserClockInLogDO;
import cn.iocoder.yudao.module.system.service.user.TenantUserClockInLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 门店员工打卡记录")
@RestController
@RequestMapping("/system/tenant-user-clock-in-log")
@Validated
public class TenantUserClockInLogController {

    @Resource
    private TenantUserClockInLogService tenantUserClockInLogService;

    @PostMapping("/create")
    @Operation(summary = "创建门店员工打卡记录")
    @PreAuthorize("@ss.hasPermission('system:tenant-user-clock-in-log:create')")
    public CommonResult<Long> createTenantUserClockInLog(@Valid @RequestBody TenantUserClockInLogSaveReqVO createReqVO) {
        return success(tenantUserClockInLogService.createTenantUserClockInLog(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新门店员工打卡记录")
    @PreAuthorize("@ss.hasPermission('system:tenant-user-clock-in-log:update')")
    public CommonResult<Boolean> updateTenantUserClockInLog(@Valid @RequestBody TenantUserClockInLogSaveReqVO updateReqVO) {
        tenantUserClockInLogService.updateTenantUserClockInLog(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除门店员工打卡记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:tenant-user-clock-in-log:delete')")
    public CommonResult<Boolean> deleteTenantUserClockInLog(@RequestParam("id") Long id) {
        tenantUserClockInLogService.deleteTenantUserClockInLog(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得门店员工打卡记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:tenant-user-clock-in-log:query')")
    public CommonResult<TenantUserClockInLogRespVO> getTenantUserClockInLog(@RequestParam("id") Long id) {
        TenantUserClockInLogDO tenantUserClockInLog = tenantUserClockInLogService.getTenantUserClockInLog(id);
        return success(BeanUtils.toBean(tenantUserClockInLog, TenantUserClockInLogRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得门店员工打卡记录分页")
    @PreAuthorize("@ss.hasPermission('system:tenant-user-clock-in-log:query')")
    public CommonResult<PageResult<TenantUserClockInLogRespVO>> getTenantUserClockInLogPage(@Valid TenantUserClockInLogPageReqVO pageReqVO) {
        PageResult<TenantUserClockInLogDO> pageResult = tenantUserClockInLogService.getTenantUserClockInLogPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TenantUserClockInLogRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出门店员工打卡记录 Excel")
    @PreAuthorize("@ss.hasPermission('system:tenant-user-clock-in-log:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTenantUserClockInLogExcel(@Valid TenantUserClockInLogPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TenantUserClockInLogDO> list = tenantUserClockInLogService.getTenantUserClockInLogPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "门店员工打卡记录.xls", "数据", TenantUserClockInLogRespVO.class,
            BeanUtils.toBean(list, TenantUserClockInLogRespVO.class));
    }

}