package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.share;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 总部门店套餐共享分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class TenantPackageShareRelationPageReqVO extends PageParam {

    @Schema(description = "总部id", example = "9866")
    private Long headTenantId;

    private Long tenantId;

    private List<Long> tenantIds;

    @Schema(description = "业务类型 0-问诊,1-智慧脸...", example = "1")
    private Integer bizType = BizTypeEnum.HYWZ.getCode();

    @Schema(description = "共享套餐开通关系表id", example = "574")
    private Long tenantPackageId;

    @Schema(description = "扩展信息")
    private String ext;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}