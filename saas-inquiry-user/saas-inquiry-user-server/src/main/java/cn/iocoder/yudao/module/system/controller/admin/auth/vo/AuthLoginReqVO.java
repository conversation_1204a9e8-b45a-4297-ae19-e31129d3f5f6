package cn.iocoder.yudao.module.system.controller.admin.auth.vo;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import cn.iocoder.yudao.module.system.enums.social.SocialTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xyy.saas.inquiry.enums.inquiry.ClientChannelTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.sshd.client.channel.ClientChannel;
import org.hibernate.validator.constraints.Length;

@Schema(description = "管理后台 - 账号密码登录 Request VO，如果登录并绑定社交用户，需要传递 social 开头的参数")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class AuthLoginReqVO {

    @Schema(description = "客户端类型: 0、app  1、pc  2、小程序 ", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "0")
    private Integer clientChannelType;


    @Schema(description = "门店名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "xx大药房")
//    @Length(min = 2, max = 32, message = "门店名称长度为 2-32 位")
    private String tenantName;

    @Schema(description = "账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "admin")
    @NotEmpty(message = "登录账号不能为空")
    @Length(min = 4, max = 32, message = "账号长度为 4-32 位")
    @Pattern(regexp = "^[A-Za-z0-9]+$", message = "账号格式为数字以及字母")
    private String username;

    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "admin123")
    @NotEmpty(message = "密码不能为空")
//    @Pattern(regexp = SystemConstant.PASSWORD_PATTERN_REGEXP, message = SystemConstant.PASSWORD_PATTERN_REGEXP_MESSAGE)
    private String password;

    // ========== 图片验证码相关 ==========

    @Schema(description = "验证码，验证码开启时，需要传递", requiredMode = Schema.RequiredMode.REQUIRED,
        example = "PfcH6mgr8tpXuMWFjvW6YVaqrswIuwmWI5dsVZSg7sGpWtDCUbHuDEXl3cFB1+VvCC/rAkSwK8Fad52FSuncVg==")
    @NotEmpty(message = "验证码不能为空", groups = CodeEnableGroup.class)
    private String captchaVerification;

    // ========== 绑定社交登录时，需要传递如下参数 ==========

    @Schema(description = "社交平台的类型，参见 SocialTypeEnum 枚举值", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    @InEnum(SocialTypeEnum.class)
    private Integer socialType;

    @Schema(description = "授权码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private String socialCode;

    @Schema(description = "state", requiredMode = Schema.RequiredMode.REQUIRED, example = "9b2ffbc1-7425-4155-9894-9d5c08541d62")
    private String socialState;

    /**
     * 开启验证码的 Group
     */
    public interface CodeEnableGroup {

    }

    @AssertTrue(message = "授权码不能为空")
    public boolean isSocialCodeValid() {
        return socialType == null || StrUtil.isNotEmpty(socialCode);
    }

    @AssertTrue(message = "授权 state 不能为空")
    public boolean isSocialState() {
        return socialType == null || StrUtil.isNotEmpty(socialState);
    }


    // ========== 三方应用授权登录时，需要传递如下参数 ==========
    @Schema(description = "应用标识", example = "erp_key")
    private String appKey;
    @Schema(description = "时间戳", example = "123456789")
    private Long timestamp;
    @Schema(description = "随机字符串", example = "abc")
    @Length(max = 36, message = "随机字符串长度不能超过36")
    private String nonce;
    @Schema(description = "签名", example = "aaa")
    private String sign;


}