package cn.iocoder.yudao.module.system.controller.app.appversion.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: xucao
 * @Date: 2025/01/20 9:47
 * @Description: 必须描述类做什么事情, 实现什么功能
 */
@Schema(description = "用户 App - 检查app版本入参")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CheckAppVersionReqVO {
    @Schema(description = "app业务标识", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "app业务标识不能为空")
    private Integer appBiz;

    @Schema(description = "系统类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "android")
    @NotEmpty(message = "系统类型不能为空")
    private String osType;

    @Schema(description = "当前版本编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @NotNull(message = "当前版本编码不能为空")
    private Integer versionCode;

    @Schema(description = "当前用户userId", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long userId;

    @Schema(description = "当前租户Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long tenantId;

    @Schema(description = "是否用户主动检查更新", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private Boolean voluntaryCheck;
}
