package cn.iocoder.yudao.module.system.controller.app.user;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.idempotent.core.annotation.Idempotent;
import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import cn.iocoder.yudao.module.system.controller.app.user.vo.UserFacePageReqVO;
import cn.iocoder.yudao.module.system.controller.app.user.vo.UserFaceRespVO;
import cn.iocoder.yudao.module.system.controller.app.user.vo.UserFaceSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.UserFaceDO;
import cn.iocoder.yudao.module.system.service.user.UserFaceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Optional;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "PC+APP - 用户人脸信息")
@RestController
@RequestMapping(value = {"/admin-api/system/user-face", "/app-api/system/user-face"})
@Validated
public class UserFaceController {

    @Resource
    private UserFaceService userFaceService;

    @PostMapping("/save")
    @Operation(summary = "创建用户人脸信息")
    @Idempotent
    public CommonResult<Long> saveUserFace(@Valid @RequestBody UserFaceSaveReqVO createReqVO) {
        createReqVO.setUserId(Optional.ofNullable(createReqVO.getUserId()).orElse(WebFrameworkUtils.getLoginUserId()));
        return success(userFaceService.saveUserFace(createReqVO));
    }

    @PostMapping("/match")
    @Operation(summary = "匹配校验人脸信息")
    @Idempotent
    public CommonResult<Boolean> matchUserFace(@Valid @RequestBody UserFaceSaveReqVO createReqVO) {
        createReqVO.setUserId(Optional.ofNullable(createReqVO.getUserId()).orElse(WebFrameworkUtils.getLoginUserId()));
        return success(userFaceService.matchUserFace(createReqVO));
    }


    @GetMapping("/validate-face")
    @Operation(summary = "校验是否存在用户人脸信息")
    public CommonResult<UserFaceRespVO> validateFace() {
        UserFaceDO userFace = userFaceService.validateFace(WebFrameworkUtils.getLoginUserId());
        return success(BeanUtils.toBean(userFace, UserFaceRespVO.class));
    }

    @GetMapping("/get")
    @Operation(summary = "获得用户人脸信息")
    public CommonResult<UserFaceRespVO> getUserFace() {
        UserFaceDO userFace = userFaceService.getUserFace(WebFrameworkUtils.getLoginUserId());
        return success(BeanUtils.toBean(userFace, UserFaceRespVO.class));
    }

    @GetMapping("/has-face")
    @Operation(summary = "当前用户是否有录入了人脸")
    public CommonResult<Boolean> userHasFace() {
        UserFaceDO userFace = userFaceService.getUserFace(WebFrameworkUtils.getLoginUserId());
        return success(userFace != null);
    }


    @DeleteMapping("/delete")
    @Operation(summary = "删除用户人脸信息")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteUserFace(@RequestParam("id") Long id) {
        userFaceService.deleteUserFace(id);
        return success(true);
    }


    @GetMapping("/page")
    @Operation(summary = "获得用户人脸信息分页")
    @PreAuthorize("@ss.hasPermission('system:user-face:query')")
    public CommonResult<PageResult<UserFaceRespVO>> getUserFacePage(@Valid UserFacePageReqVO pageReqVO) {
        PageResult<UserFaceDO> pageResult = userFaceService.getUserFacePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, UserFaceRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出用户人脸信息 Excel")
    @PreAuthorize("@ss.hasPermission('system:user-face:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportUserFaceExcel(@Valid UserFacePageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<UserFaceDO> list = userFaceService.getUserFacePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "用户人脸信息.xls", "数据", UserFaceRespVO.class,
            BeanUtils.toBean(list, UserFaceRespVO.class));
    }

}