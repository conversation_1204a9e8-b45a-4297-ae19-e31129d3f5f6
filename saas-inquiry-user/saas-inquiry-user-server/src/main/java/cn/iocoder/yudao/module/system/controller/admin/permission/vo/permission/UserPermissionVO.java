package cn.iocoder.yudao.module.system.controller.admin.permission.vo.permission;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: xucao
 * @DateTime: 2025/4/1 18:09
 * @Description: 用户权限VO
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserPermissionVO implements Serializable {

    @Schema(description = "是否门店", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    @Builder.Default
    private boolean drugStore=false;
    @Schema(description = "是否药师", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private boolean pharmacist=false;
    @Schema(description = "是否医生", requiredMode = Schema.RequiredMode.REQUIRED, example = "false")
    private boolean physician=false;

    @Schema(description = "关联租户列表", requiredMode = Schema.RequiredMode.REQUIRED, example = "[1,2,3]")
    private List<Long> tenantIdList;
}
