package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages;

import com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItem;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Schema(description = "管理后台 - 门店套餐精简 Response VO")
@Data
public class TenantPackageSimpleRespVO {

    @Schema(description = "套餐编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "套餐编号不能为空")
    private Long id;

    @Schema(description = "编码", example = "xx100001")
    private String pref;

    @Schema(description = "套餐名", requiredMode = Schema.RequiredMode.REQUIRED, example = "VIP")
    @NotNull(message = "套餐名不能为空")
    private String name;

    @Schema(description = "套餐定价", example = "999")
    private BigDecimal price;

    @Schema(description = "套餐时限", example = "1")
    private Integer term;

    @Schema(description = "时限类型", example = "year")
    private Integer termType;

    @Schema(description = "问诊业务类型", example = "1")
    private Integer inquiryBizType;

    @Schema(description = "问诊审方类型", example = "1")
    private Integer inquiryAuditType;

    @Schema(description = "套餐类型", example = "1")
    private Integer packageType;

    @Schema(description = "问诊包信息")
    private List<InquiryPackageItem> inquiryPackageItems;

    @Schema(description = "问诊包信息文案")
    private String inquiryPackageItemStr;

    public String getInquiryPackageItemStr() {
        return InquiryPackageItem.getInquiryPackageItemStr(getInquiryBizType(), getInquiryPackageItems());

    }
}
