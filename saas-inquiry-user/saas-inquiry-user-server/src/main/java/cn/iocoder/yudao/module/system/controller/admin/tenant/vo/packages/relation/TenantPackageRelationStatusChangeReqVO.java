package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation;

import cn.iocoder.yudao.framework.common.validation.InEnum;
import com.xyy.saas.inquiry.pojo.tenant.TenantPackageRelationStatusChangeDto;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageRelationStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Schema(description = "管理后台 - 门店套餐包状态变更(作废退款暂停) Request VO")
@Data
@Accessors(chain = true)
public class TenantPackageRelationStatusChangeReqVO {

    @Schema(description = "门店套餐包ids", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "门店套餐关系id不能为空", groups = BatchUpdate.class)
    private List<Long> tenantPackageRelationIds;


    @Schema(description = "门店套餐关系id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13351")
    @NotNull(message = "门店套餐关系id不能为空", groups = Update.class)
    private Long id;

    /**
     * {@link TenantPackageRelationStatusEnum}
     */
    @Schema(description = "套餐包状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "套餐包状态不能为空", groups = {Update.class, BatchUpdate.class})
    @InEnum(value = TenantPackageRelationStatusEnum.class, message = "状态必须是 {value}", groups = {Update.class, BatchUpdate.class})
    private Integer status;


    @Schema(description = "套餐包状态变更信息Dto", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "套餐包状态变更信息不能为空", groups = {Update.class, BatchUpdate.class})
    private TenantPackageRelationStatusChangeDto statusChangeInfo;

    public static interface Update {

    }

    public static interface BatchUpdate {

    }


}