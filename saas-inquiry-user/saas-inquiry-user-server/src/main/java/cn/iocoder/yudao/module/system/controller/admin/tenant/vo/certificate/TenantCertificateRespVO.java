package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.certificate;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 门店资质证件信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TenantCertificateRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "1572")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "门店id", example = "2")
    private Long tenantId;

    @Schema(description = "证件类型 1营业执照 2药品经营许可证 3药品经营质量管理规范认证号", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("证件类型 1营业执照 2药品经营许可证 3药品经营质量管理规范认证号")
    private Integer certificateType;

    @Schema(description = "证件名称", example = "芋艿")
    @ExcelProperty("证件名称")
    private String certificateName;

    @Schema(description = "证件号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("证件号")
    private String certificateNo;

    @Schema(description = "证件url地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("证件url地址")
    private List<String> certificateImgUrls;

    @Schema(description = "注册时间")
    @ExcelProperty("注册时间")
    private LocalDateTime registerTime;

    @Schema(description = "有效期至")
    @ExcelProperty("有效期至")
    private LocalDateTime validTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}