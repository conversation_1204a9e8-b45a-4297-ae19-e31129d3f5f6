package cn.iocoder.yudao.module.system.controller.admin.biz.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.util.List;

@Schema(description = "管理后台 - 业务列表 Request VO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class BizListReqVO {

    @Schema(description = "业务类型", example = "2")
    private Integer type;

    @Schema(description = "业务类型列表", example = "2")
    private List<Integer> typeList;

    @Schema(description = "名称", example = "赵六")
    private String name;

}