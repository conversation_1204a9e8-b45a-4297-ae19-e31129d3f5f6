package cn.iocoder.yudao.module.system.controller.app.tool;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.security.PermitAll;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * @Author: xucao
 * @Date: 2025/2/21 15:24
 * @Description: app 端时间相关基础工具接口
 */
@Tag(name = "门店 App - 时间相关基础接口")
@RestController
@RequestMapping(value = {"/admin-api/system/date", "/app-api/system/date"})
@Validated
public class TimeToolController {
    @GetMapping("/time-now")
    @Operation(summary = "获取服务器当前时间", description = "获取服务器当前时间")
    @PermitAll
    public CommonResult<LocalDateTime> getServerTimeNow() {
        return success(LocalDateTime.now());
    }
}
