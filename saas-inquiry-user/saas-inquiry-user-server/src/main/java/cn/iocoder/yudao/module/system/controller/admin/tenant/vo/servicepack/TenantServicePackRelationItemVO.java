package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack;


import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.pojo.servicepack.ServicePackRelationExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 门店-开通服务包关系包Item VO")
@Data
@Accessors(chain = true)
public class TenantServicePackRelationItemVO {

    /**
     * 门店id
     */
    private Long tenantId;

    @Schema(description = "服务包id")
    private Integer servicePackId;

    @Schema(description = "目录Pref")
    private String catalogPref;

    @Schema(description = "目录版本id")
    private Long catalogId;

    @Schema(description = "医药行业行政机构ID")
    private Integer organId;

    @Schema(description = "服务机构类型")
    private Integer organType;

    @Schema(description = "服务包状态 0未开通 1开通")
    private Integer status;

    @Schema(description = "服务包扩展信息ext")
    private ServicePackRelationExtDto ext;

    @Schema(description = "版本号-查询时返回")
    private Long servicePackVersion;

    // @Schema(description = "服务包详情信息-查询时返回")
    // private List<String> detailInfo;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;
}