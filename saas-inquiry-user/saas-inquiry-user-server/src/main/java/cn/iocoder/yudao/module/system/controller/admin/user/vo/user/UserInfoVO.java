package cn.iocoder.yudao.module.system.controller.admin.user.vo.user;

import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantInfoVO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.Set;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 用户信息 Response VO")
@Data
@Accessors(chain = true)
public class UserInfoVO {

    @Schema(description = "用户编号", example = "1")
    private Long id;

    @Schema(description = "用户账号", example = "yudao")
    private String username;

    @Schema(description = "姓名", example = "芋艿")
    private String nickname;

    @Schema(description = "部门ID", example = "我是一个用户")
    private Long deptId;

    @Schema(description = "部门名称", example = "IT 部")
    private String deptName;

    @Schema(description = "用户邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "手机号码", example = "15601691300")
    private String mobile;

    @Schema(description = "身份证号", example = "******************")
    private String idCard;

    @Schema(description = "用户性别，参见 SexEnum 枚举类", example = "1")
    private Integer sex;

    @Schema(description = "用户头像", example = "https://www.iocoder.cn/xxx.png")
    private String avatar;

    @Schema(description = "状态，参见 CommonStatusEnum 枚举类", example = "1")
    private Integer status;

    @Schema(description = "账号状态，参见 CommonStatusEnum 枚举类", example = "1")
    private Integer accountStatus;

    @Schema(description = "最后登录 IP", example = "***********")
    private String loginIp;

    @Schema(description = "最后登录时间")
    private LocalDateTime loginDate;


    @Schema(description = "门店信息")
    private TenantInfoVO tenant;

    @Schema(description = "角色名称集合")
    private Set<String> roleNames;

    @Schema(description = "角色code集合")
    private Set<String> roleCodes;

    @Schema(description = "角色ids")
    private Set<Long> roleIds;

    @Schema(description = "门店用户关系id")
    private Long tenantUserRelationId;

    @Schema(description = "门店管理员id")
    private Long tenantAdminUserId;

    @Schema(description = "是否拥有指纹")
    private boolean hasFingerPrint;

    /**
     * 是否需要打卡 (0是 1否) {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer needClockIn;
}
