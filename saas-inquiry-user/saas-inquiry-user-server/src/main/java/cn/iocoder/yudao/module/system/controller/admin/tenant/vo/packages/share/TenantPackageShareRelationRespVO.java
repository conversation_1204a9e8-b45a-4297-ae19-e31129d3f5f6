package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.share;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

@Schema(description = "管理后台 - 总部门店套餐共享 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TenantPackageShareRelationRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "12924")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "总部id", example = "9866")
    private Long headTenantId;

    private Long tenantId;

    @ExcelProperty("门店名称")
    private String tenantName;

    @ExcelProperty("门店编码")
    private String tenantPref;

    @Schema(description = "业务类型 0-问诊,1-智慧脸...", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("业务类型 0-问诊,1-智慧脸...")
    private Integer bizType;

    @Schema(description = "共享套餐开通关系表id", requiredMode = Schema.RequiredMode.REQUIRED, example = "574")
    @ExcelProperty("共享套餐开通关系表id")
    private Long tenantPackageId;

    @Schema(description = "扩展信息")
    @ExcelProperty("扩展信息")
    private String ext;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;


    /**
     * 联系人
     */
    private String contactName;
    /**
     * 联系手机
     */
    private String contactMobile;

    /**
     * 省
     */
    private String province;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 市
     */
    private String city;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 区
     */
    private String area;
    /**
     * 区编码
     */
    private String areaCode;
    /**
     * 药店地址
     */
    private String address;

}