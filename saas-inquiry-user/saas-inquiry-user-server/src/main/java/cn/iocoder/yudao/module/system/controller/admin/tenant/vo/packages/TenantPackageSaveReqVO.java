package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import cn.iocoder.yudao.module.system.framework.operatelog.core.CommonStatusParseFunction;
import com.mzt.logapi.starter.annotation.DiffLogField;
import com.xyy.saas.inquiry.enums.inquiry.InquiryAuditTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.DateTermTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageEnum;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItem;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@Schema(description = "管理后台 - 门店套餐创建/修改 Request VO")
@Data
@Accessors(chain = true)
public class TenantPackageSaveReqVO {

    @Schema(description = "套餐编号", example = "1024")
    private Long id;

    @Schema(description = "编码", example = "xx100001")
    private String pref;

    @Schema(description = "套餐名", requiredMode = Schema.RequiredMode.REQUIRED, example = "VIP")
    @NotEmpty(message = "套餐名不能为空")
    @Length(max = 64, message = "套餐名最大长度不可超过64")
    @DiffLogField(name = "套餐名")
    private String name;

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "业务类型不能为空")
    @InEnum(value = BizTypeEnum.class, message = "状态必须是 {value}")
    private Integer bizType;

    @Schema(description = "套餐类型（1-单体套餐 3连锁套餐）", example = "1")
    @InEnum(value = TenantPackageEnum.class, message = "套餐类型必须是 {value}")
    private Integer packageType;

    @Schema(description = "状态，参见 CommonStatusEnum 枚举", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    @InEnum(value = CommonStatusEnum.class, message = "状态必须是 {value}")
    @DiffLogField(name = "状态", function = CommonStatusParseFunction.NAME)
    private Integer status;

    @Schema(description = "备注", example = "好")
    @Length(max = 255, message = "备注最大长度为 {value}")
    private String remark;

    @Schema(description = "关联的菜单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "关联的菜单编号不能为空")
    private Set<Long> menuIds;

    @Schema(description = "总部菜单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "总部菜单编号不能为空")
    private Set<Long> headMenuIds;

    @Schema(description = "门店菜单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "门店菜单编号不能为空")
    private Set<Long> storeMenuIds;

    // ------------------------- 问诊包信息 -------------------------

    @Schema(description = "问诊套餐包关联医院id", example = "1,2")
    private List<String> hospitalPrefs;


    @Schema(description = "问诊业务类型", example = "1")
    @InEnum(value = InquiryWayTypeEnum.class, message = "问诊业务类型必须是 {value}")
    private Integer inquiryBizType;

    @Schema(description = "问诊审方类型", example = "1")
    @InEnum(value = InquiryAuditTypeEnum.class, message = "问诊审方类型必须是 {value}")
    private Integer inquiryAuditType;


    @Schema(description = "套餐定价", example = "999")
    @Min(value = 0, message = "套餐定价最小值为 {value}")
    @Max(value = 999999, message = "套餐定价最小值为 {value}")
    private BigDecimal price;

    @Schema(description = "套餐时限", example = "1")
    @Max(value = 999, message = "套餐时限最大不值为 {value}")
    private Integer term;

    @Schema(description = "时限类型", example = "year")
    @InEnum(value = DateTermTypeEnum.class, message = "时限类型必须是 {value}")
    private Integer termType;

    /**
     * {@link InquiryWayTypeEnum}
     */
    @Schema(description = "问诊方式类型", example = "1,2")
    private List<Integer> inquiryWayTypes;

    @Schema(description = "问诊包信息")
    private List<InquiryPackageItem> inquiryPackageItems;

    @Schema(description = "套餐排序", example = "99")
    @Min(value = 0, message = "套餐排序最小值为 {value}")
    @Max(value = 99999, message = "套餐排序最大值为 {value}")
    @DiffLogField(name = "套餐排序")
    private Integer sorted;

    @Schema(description = "套餐可见地区编码,对应区域编码", example = "420000,420100")
    private List<String> regionArr;

    @Schema(description = "套餐底部文案", example = "套餐底部文案")
    @Length(max = 256, message = "套餐底部文案最大长度为 {value}")
    @DiffLogField(name = "套餐底部文案")
    private String bottomTxt;

    @Schema(description = "套餐推荐文案", example = "套餐推荐文案")
    @Length(max = 256, message = "套餐推荐文案最大长度为 {value}")
    @DiffLogField(name = "套餐推荐文案")
    private String recommendTxt;

}
