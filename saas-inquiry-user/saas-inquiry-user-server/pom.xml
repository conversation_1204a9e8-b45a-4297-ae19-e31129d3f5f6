<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>com.xyy.saas</groupId>
    <artifactId>saas-inquiry-user</artifactId>
    <version>${revision}</version>
    <relativePath>../pom.xml</relativePath>
  </parent>

  <modelVersion>4.0.0</modelVersion>
  <artifactId>saas-inquiry-user-server</artifactId>

  <description>inquiry-user 模块 server，提供核心业务支撑服务 例如说：用户、部门、权限、数据字典等等</description>


  <dependencies>
    <!-- Nacos Discovery Starter -->
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>biz-soa-starter</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>rocketmq-event-bus-spring-boot-starter</artifactId>
          <groupId>com.xyy.saas</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-spring-boot-starter-test</artifactId>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-user-api</artifactId>
      <version>${revision}</version>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-product-server</artifactId>
      <version>${revision}</version>
    </dependency>


    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-module-system-biz</artifactId>
      <version>${yudao.version}</version>
    </dependency>

    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-module-member-biz</artifactId>
      <version>${yudao.version}</version>
    </dependency>


    <dependency>
      <groupId>com.xyy.common</groupId>
      <artifactId>xyy-common-dubbo-client</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>rocketmq-event-bus-spring-boot-starter</artifactId>
      <version>0.0.1-SNAPSHOT</version>
    </dependency>

    <!-- JWT (higress官方JWT插件指定) -->
    <dependency>
      <groupId>org.bitbucket.b_c</groupId>
      <artifactId>jose4j</artifactId>
      <version>0.7.0</version>
    </dependency>

    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-module-infra-biz</artifactId>
      <version>${yudao.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>spring-boot-admin-starter-server</artifactId>
          <groupId>de.codecentric</groupId>
        </exclusion>
        <exclusion>
          <artifactId>yudao-spring-boot-starter-monitor</artifactId>
          <groupId>cn.iocoder.boot</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>

    <!-- dsl 解析 -->
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-localserver-dsl</artifactId>
      <version>2.0.0-SNAPSHOT</version>
    </dependency>

    <!-- 百度api - 人脸识别 -->
    <dependency>
      <groupId>com.baidu.aip</groupId>
      <artifactId>java-sdk</artifactId>
      <version>${baidu.api.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-simple</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>

  <build>
    <!--  打包依赖到jar包中  -->
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>${maven-springboot-plugin.version}</version>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>


      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <version>3.0.0-M3</version>
        <executions>
          <execution>
            <id>enforce-banned-dependencies</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <!-- 规则1：确保所有依赖版本收敛，避免版本冲突 -->
                <!-- <dependencyConvergence/> -->

                <!-- 规则2：禁止不合规的依赖关系 -->
                <bannedDependencies>
                  <excludes>
                    <exclude>com.xyy.saas:saas-transmitter*:*</exclude>

                    <exclude>com.xyy.saas:saas-inquiry-kernel*:*</exclude>
                    <exclude>com.xyy.saas:saas-inquiry-drugstore*:*</exclude>
                    <exclude>com.xyy.saas:saas-inquiry-patient*:*</exclude>
                    <exclude>com.xyy.saas:saas-inquiry-hospital*:*</exclude>
                    <exclude>com.xyy.saas:saas-inquiry-pharmacist*:*</exclude>
                    <exclude>com.xyy.saas:saas-inquiry-im*:*</exclude>
                    <exclude>com.xyy.saas:saas-inquiry-signature*:*</exclude>

                  </excludes>
                  <searchTransitive>true</searchTransitive>
                </bannedDependencies>
              </rules>
              <!-- 如果规则被违反，构建将失败 -->
              <fail>true</fail>
            </configuration>
          </execution>
        </executions>
      </plugin>

    </plugins>
  </build>
</project>